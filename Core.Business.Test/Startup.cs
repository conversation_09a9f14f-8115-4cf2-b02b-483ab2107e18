using Core.Business.Tests.Factories.Singleton;
using Core.DataLog;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Core.Business.Tests
{
    public class Startup
    {
        public IConfiguration Configuration { get; }

        public Startup()
        {
            Configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .Build();
        }

        public void ConfigureServices(IServiceCollection services)
        {
            // Register configuration for MongoDBDatabaseSettings
            services.Configure<MongoDBDatabaseSettings>(Configuration.GetSection("MongoDBDatabaseSettings"));

            //Register lifetime service
            services.AddSingleton<ISystemDbContextSingleton, SystemDbContextSingleton>();
            services.AddSingleton<IMongoDbContextSingleton, MongoDbContextSingleton>();
        }
    }
}
