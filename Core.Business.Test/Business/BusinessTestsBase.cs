using Microsoft.Extensions.DependencyInjection;

namespace Core.Business.Tests.Business
{
    public class BusinessTestsBase
    {
        protected readonly ServiceProvider ServiceProvider;
        public BusinessTestsBase()
        {
            // Initialize Startup
            var startup = new Startup();
            var services = new ServiceCollection();

            // Configure services
            startup.ConfigureServices(services);

            // Build ServiceProvider
            ServiceProvider = services.BuildServiceProvider();
        }
    }
}
