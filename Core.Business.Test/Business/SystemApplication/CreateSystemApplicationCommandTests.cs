using Core.Business.Tests.Factories.Singleton;
using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Moq;

namespace Core.Business.Tests.Business.SystemApplication
{
    public class CreateSystemApplicationCommandTests : BusinessTestsBase
    {
        private readonly SystemDataContext _systemDataContext;
        public CreateSystemApplicationCommandTests()
        {
            _systemDataContext = ServiceProvider.GetRequiredService<ISystemDbContextSingleton>().GetInstance();
        }
        [Fact]
        public async Task Handle_ShouldThrowException_WhenCodeExists()
        {
            // Arrange
            //Giả lập 1 bản ghi có Code = EXISTING_CODE để thực hiện check trùng code theo nghiệp vụ bên dưới
            await _systemDataContext.SystemApplication.AddAsync(new Data.SystemApplication { Code = "EXISTING_CODE" });
            await _systemDataContext.SaveChangesAsync();

            //Setup: <PERSON><PERSON><PERSON> nghĩa cách phản hồi của mock khi thuộc tính hoặc phương thức này được gọi trong quá trình kiểm thử.
            //Returns: Mock sẽ trả về một đối tượng LocalizedString có key "system-application.code.existed" và giá trị "Code already exists".
            //Tương tự khi giả lập các Service cần thiết khác
            var mockCacheService = new Mock<ICacheService>();
            var mockLocalizer = new Mock<IStringLocalizer<Resources>>();
            mockLocalizer.Setup(l => l["system-application.code.existed"])
                .Returns(new LocalizedString("system-application.code.existed", "Code already exists"));
            var mockConfig = new Mock<IConfiguration>();

            //Thực hiện gọi hàm Business cần test với các tham số đã Mock ở trên
            var handler = new CreateSystemApplicationCommand.Handler(_systemDataContext, mockCacheService.Object, mockLocalizer.Object, mockConfig.Object);

            var command = new CreateSystemApplicationCommand(new CreateSystemApplicationModel
            {
                Code = "EXISTING_CODE"
            }, new SystemLogModel { TraceId = Guid.NewGuid().ToString() });

            // Act & Assert
            //So sánh kết quả trả về với kết quả mong mốn
            var exception = await Assert.ThrowsAsync<ArgumentException>(() => handler.Handle(command, CancellationToken.None));
            Assert.Equal(mockLocalizer.Object["system-application.code.existed"], exception.Message);
        }

        [Fact]
        public async Task Handle_ShouldCreateApplicationSuccessfully()
        {
            // Arrange
            var mockCacheService = new Mock<ICacheService>();
            var mockLocalizer = new Mock<IStringLocalizer<Resources>>();
            var mockConfig = new Mock<IConfiguration>();
            var handler = new CreateSystemApplicationCommand.Handler(_systemDataContext, mockCacheService.Object, mockLocalizer.Object, mockConfig.Object);
            var sysAppModel = new CreateSystemApplicationModel
            {
                Code = "NEW_CODE",
                Name = "Test Application",
                Description = "Test Description",
                CreatedUserId = 1,
                IsActive = true,
                Order = 1,
                CreatedDate = DateTime.Now
            };
            var sysLog = new SystemLogModel
            {
                TraceId = Guid.NewGuid().ToString(),
                ActionCode = nameof(LogConstants.ACTION_SYS_APP_CREATE),
                ActionName = LogConstants.ACTION_SYS_APP_CREATE,
                ListAction = new List<ActionDetail>()
            };

            var command = new CreateSystemApplicationCommand(sysAppModel, sysLog);

            // Act
            var result = await handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.Equal(Unit.Value, result);
            var createdApplication = await _systemDataContext.SystemApplication.FirstOrDefaultAsync(x => x.Code == "NEW_CODE");
            Assert.NotNull(createdApplication);
        }
    }
}
