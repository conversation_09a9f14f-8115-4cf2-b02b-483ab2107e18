using Core.Business.Tests.Factories.Dtos;
using Core.DataLog;
using Microsoft.Extensions.Options;
using MongoDB.Driver;

namespace Core.Business.Tests.Factories.Singleton
{
    public interface IMongoDbContextSingleton
    {
        MongoSettingDtos GetInstance();
    }
    public class MongoDbContextSingleton : IMongoDbContextSingleton
    {
        private IMongoDatabase _mongoDatabase;
        private IMongoClient _mongoClient;
        private readonly MongoDBDatabaseSettings _settings;

        public MongoDbContextSingleton(IOptions<MongoDBDatabaseSettings> options)
        {
            // Bind to MongoDBDatabaseSettings class
            _settings = options.Value;
        }
        public MongoSettingDtos GetInstance()
        {
            bool isCreateInstance = false;
            if (_mongoDatabase == null)
                isCreateInstance = true;

            if (isCreateInstance)
            {
                // Khởi động MongoDB instance

                // Tạo MongoDB client
                _mongoClient = new MongoClient(_settings.ConnectionString);
                _mongoDatabase = _mongoClient.GetDatabase(_settings.DatabaseName);
            }
            return new MongoSettingDtos()
            {
                ConnectionString = _settings.ConnectionString,
                MongoClient = _mongoClient,
                MongoDatabase = _mongoDatabase,
                DatabaseName = _settings.DatabaseName
            };
        }
    }
}
