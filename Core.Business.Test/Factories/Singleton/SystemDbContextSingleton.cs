using Core.Data;
using Microsoft.EntityFrameworkCore;

namespace Core.Business.Tests.Factories.Singleton
{
    public interface ISystemDbContextSingleton
    {
        SystemDataContext GetInstance();
    }
    public class SystemDbContextSingleton : ISystemDbContextSingleton
    {
        private SystemDataContext _systemDataContext;
        private const string UniSystemInMemoryDbName = "UniSystemInMemoryDb";

        public SystemDataContext GetInstance()
        {
            bool isCreateInstance = false;
            if (_systemDataContext == null)
                isCreateInstance = true;

            if (isCreateInstance)
            {
                var uniSystemDbOptions = new DbContextOptionsBuilder<SystemDataContext>()
                    .UseInMemoryDatabase(UniSystemInMemoryDbName)
                    .Options;

                _systemDataContext = new SystemDataContext(uniSystemDbOptions);
                return _systemDataContext;
            }
            return _systemDataContext;
        }
    }
}
