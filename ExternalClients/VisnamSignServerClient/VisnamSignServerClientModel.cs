using System.Text.Json.Serialization;
using System;
using System.Collections.Generic;

namespace VisnamSignServerClient
{
    public class VisnamSignServerClientConfiguration
    {
        public string Domain { get; set; }
        public string Schema  { get; set; }
        public int Port { get; set; }
    }

    public class HouResponseDataModel<T>
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public T Data { get; set; }
    }

    #region EndCert

    public class EndCertRequestModel
    {
        public string UserId { get; set; }
        public string UserKey { get; set; }
    }
    
    public class EndCertResponseModel
    {
        [JsonPropertyName("data")]
        public string Data { get; set; }
    }

    #endregion

    #region SignHash

    public class SignHashDataClientRequestModel
    {
        public string Base64Hash { get; set; }
        
        public string UserId { get; set; }
        
        public string UserKey { get; set; }
    }
    
    public class SignHashRequestModel
    {
        [JsonPropertyName("hashalg")]
        public string HashAlg { get; set; } = "SHA256";
        
        [JsonPropertyName("base64hash")]
        public string Base64Hash { get; set; }
    }
    
    public class SignHashResponseModel
    {
        [JsonPropertyName("status")]
        public int Status { get; set; }
        
        [JsonPropertyName("description")]
        public string Description { get; set; }
        
        [JsonPropertyName("error")]
        public string Error { get; set; }
        
        [JsonPropertyName("obj")]
        public string Obj { get; set; }
    }

    #endregion

    #region SignPDF

    public class SignPDFSyncRequestModel
    {
        /// <summary>
        /// Base64 của file PDF cần ký
        /// </summary>
        public string Base64Image { get; set; }
        public string Base64Pdf { get; set; }
        public string TextOut { get; set; }
        public string SignatureName { get; set; } = "Signature";

        public int PageSign { get; set; } = 1;
        public int XPoint { get; set; } = 50;
        public int YPoint { get; set; } = 50;
        public int Width { get; set; } = 200;
        public int Height { get; set; } = 100;
        public bool AppendDateSign { get; set; } = true;
        
        /// <summary>
        /// Loại hiển thị chữ ký: 
        /// 0 - Không hiển thị chữ ký
        /// 1 - Hiển thị dưới dạng text
        /// 2 - Hiển thị dưới dạng hình ảnh
        /// 3 - Hiển thị cả hình ảnh và text
        /// </summary>
        public int TypeSignature { get; set; } = 1;
        public string TextLocationIdentifier { get; set; } = "SIGN";
        public string UserId { get; set; }
        public string UserKey { get; set; }
    }

    public class PdfSigningContent
    {
        /// <summary>
        /// Base64 của file PDF cần ký
        /// </summary>
        [JsonPropertyName("base64image")]
        public string Base64Image { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("base64pdf")]
        public string Base64Pdf { get; set; }

        /// <summary>
        /// Thuật toán hàm băm: SHA1, SHA256, SHA512
        /// </summary>
        [JsonPropertyName("hashalg")]
        public string HashAlg { get; set; } = "SHA256";

        /// <summary>
        /// Chuỗi text sẽ hiển thị lên (trong trường hợp muốn hiển thị text)
        /// </summary>
        [JsonPropertyName("textout")]
        public string TextOut { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("textoutcolor")]
        public string TextOutColor { get; set; }
        
        /// <summary>
        /// Tên của vị trí ký (trường hợp có vị trí)
        /// </summary>
        [JsonPropertyName("signaturename")]
        public string SignatureName { get; set; } = "Signature";

        /// <summary>
        /// Trang hiển thị chữ ký, trong trường hợp chọn có hiển thị
        /// </summary>
        [JsonPropertyName("pagesign")]
        public int PageSign { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("xpoint")]
        public int XPoint { get; set; } = 50;

        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("ypoint")]
        public int YPoint { get; set; } = 50;

        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("width")]
        public int Width { get; set; } = 200;

        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("height")]
        public int Height { get; set; } = 100;

        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("AppendDateSign")]
        public bool AppendDateSign { get; set; } = true;

        /// <summary>
        /// Loại hiển thị chữ ký: 
        /// 0 - Không hiển thị chữ ký
        /// 1 - Hiển thị dưới dạng text
        /// 2 - Hiển thị dưới dạng hình ảnh
        /// 3 - Hiển thị cả hình ảnh và text
        /// </summary>
        [JsonPropertyName("typesignature")]
        public int TypeSignature { get; set; } = 1;

        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("TextLocationIdentifier")]
        public string TextLocationIdentifier { get; set; } = "SIGN";
        
        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("DateFormatString")]
        public string DateFormatString { get; set; }
    }

    public class PdfSignedContent
    {
        [JsonPropertyName("status")]
        public int Status { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("error")]
        public string Error { get; set; }

        [JsonPropertyName("obj")]
        public string Obj { get; set; }
    }

    #endregion
}