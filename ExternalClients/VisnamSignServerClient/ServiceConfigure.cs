using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;

namespace VisnamSignServerClient
{
    public static class ServiceConfigure
    {
        public static IServiceCollection Configure(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<VisnamSignServerApiClient>(provider =>
            {
                // var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
                var httpClient = provider.GetRequiredService<HttpClient>();

                var config = configuration.GetSection("Signing:Visnam").Get<VisnamSignServerClientConfiguration>();

                return new VisnamSignServerApiClient(config, httpClient);
            });
            return services;
        }
    }
}
