# Client kết nối đến các api ký của Visnam

Thông tin về tài liệu kết nối API sẽ được note tại đây

## C<PERSON>c hàm có sẵn

### 1. GetEndCertAsync
Lấy chứng thư số cuối của người dùng (async)

### 2. SignHashAsync
Ký hash dữ liệu (async)

### 3. SignPDFSync
Ký PDF đồng bộ (synchronous) - **MỚI**

## Hướng dẫn sử dụng SignPDFSync

Hàm `SignPDFSync` cho phép ký PDF một cách đồng bộ, tham khảo từ logic trong `HSM_Visnam_Sign_Test`.

### Cách sử dụng cơ bản:

```csharp
// Khởi tạo client
var config = new VisnamSignServerClientConfiguration
{
    Domain = "your-domain.com",
    Schema = "https",
    Port = 443
};
var httpClient = new HttpClient();
var client = new VisnamSignServerApiClient(config, httpClient);

// Tạo request
var request = new SignPDFSyncRequestModel
{
    Base64Pdf = "base64-encoded-pdf-content",
    Base64Image = "base64-encoded-signature-image",
    UserId = "your-visnam-user-id",
    UserKey = "your-visnam-user-key",
    TextOut = "Tên người ký",
    SignatureName = "Signature",
    XPoint = 50,
    YPoint = 50,
    Width = 200,
    Height = 100,
    AppendDateSign = true,
    TypeSignature = 1,
    TextLocationIdentifier = "SIGN"
};

// Gọi hàm ký
var result = client.SignPDFSync(request);

// Xử lý kết quả
if (result.Status == 0)
{
    var signedPdfBytes = Convert.FromBase64String(result.Obj);
    File.WriteAllBytes("signed-document.pdf", signedPdfBytes);
}
```

### Tham số của SignPDFSyncRequestModel:

- **Base64Pdf**: Nội dung file PDF cần ký (base64)
- **Base64Image**: Hình ảnh chữ ký (base64)
- **UserId**: User ID từ Visnam
- **UserKey**: User Key từ Visnam
- **TextOut**: Text hiển thị trên chữ ký
- **SignatureName**: Tên chữ ký (mặc định: "Signature")
- **XPoint**: Tọa độ X của chữ ký (mặc định: 50)
- **YPoint**: Tọa độ Y của chữ ký (mặc định: 50)
- **Width**: Chiều rộng chữ ký (mặc định: 200)
- **Height**: Chiều cao chữ ký (mặc định: 100)
- **AppendDateSign**: Có thêm ngày ký không (mặc định: true)
- **TypeSignature**: Loại chữ ký (mặc định: 1)
- **TextLocationIdentifier**: Định danh vị trí text (mặc định: "SIGN")

### Xem thêm ví dụ:

Tham khảo file `SignPDFSyncExample.cs` để xem các ví dụ sử dụng chi tiết.


