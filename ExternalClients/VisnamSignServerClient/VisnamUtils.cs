using System;
using System.Security.Cryptography;

namespace VisnamSignServerClient;

public class VisnamUtils
{
    // Hàm sinh giá trị ngẫu nhiên (128 bit) do client sinh ra, không trùng lặp với các lần gọi khác
    public static string GenerateUnique128Bit()
    {
        // 128 bit = 16 bytes
        byte[] bytes = new byte[16];

        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(bytes);
        }

        // Trả về chuỗi hex, 32 ký tự (128-bit), không có dấu "-"
        return BitConverter.ToString(bytes).Replace("-", "").ToLower();
    }
}