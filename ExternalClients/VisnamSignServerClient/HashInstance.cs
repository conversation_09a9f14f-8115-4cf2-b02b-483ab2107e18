using System;
using System.Security.Cryptography;
using System.Text;

namespace VisnamSignServerClient;

public class HashInstance
{
    /// <summary>
    /// value in UPERCASE like POST, GET, PUT, DELETE
    /// </summary>
    public string Method { get; set; }

    /// <summary>
    /// Default equal http
    /// </summary>
    public string Schema { get; set; } = "http";

    public string Host { get; set; }

    /// <summary>
    /// Default equal 80
    /// </summary>
    public int Port { get; set; } = 80;

    public string Path { get; set; }

    /// <summary>
    /// Default equal application/json; charset=utf-8
    /// </summary>
    public string ContentType { get; set; } = "application/json; charset=utf-8";

    public string SharedKey { get; set; }
    
    public string UserId { get; set; }
    
    public string Nonce { get; set; }

    public HashInstance()
    {
    }

    public string GetHmacSha256(string time, string hashContentObj)
    {
        string result = null;
        var sb = new StringBuilder();
        sb.Append($"{Method}\n");
        sb.Append($"{Schema}\n");
        sb.Append($"{Host}:{Port}\n");
        sb.Append($"{Path}\n");
        if (!string.IsNullOrWhiteSpace(hashContentObj))
        {
            sb.Append($"{ContentType}\n");
        }

        sb.Append($"{UserId}\n");
        sb.Append($"{Nonce}\n");
        sb.Append($"{time}\n");
        sb.Append($"{hashContentObj}\n");

        var ab = sb.ToString();

        byte[] signature_raw = Encoding.UTF8.GetBytes(sb.ToString());
        byte[] secretKeyBytes = Convert.FromBase64String(SharedKey);

        using (HMACSHA256 hmac = new HMACSHA256(secretKeyBytes))
        {
            byte[] signatureBytes = hmac.ComputeHash(signature_raw);
            result = Convert.ToBase64String(signatureBytes);
        }

        return result;
    }
}