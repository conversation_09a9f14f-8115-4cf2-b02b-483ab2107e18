using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;

namespace Core.Shared
{
    public static class DataRowToObject
    {
        public static T ToObject<T>(this DataRow dataRow) where T : new()
        {
            T item = new T();
            foreach (DataColumn column in dataRow.Table.Columns)
            {
                PropertyInfo property = item.GetType().GetProperty(column.ColumnName);

                if (property != null && dataRow[column] != DBNull.Value && dataRow[column] != null)
                {
                    object result = Convert.ChangeType(dataRow[column], property.PropertyType);
                    property.SetValue(item, result, null);
                }
            }

            return item;
        }
        
        public static T ToObjectWithColumnName<T>(this DataRow row) where T : new()
        {
            T obj = new T();
            foreach (PropertyInfo prop in typeof(T).GetProperties())
            {
                var attribute = prop.GetCustomAttributes(typeof(DataColumnAttribute), false).FirstOrDefault() as DataColumnAttribute;
                string columnName = attribute != null ? attribute.ColumnName : prop.Name;

                if (row.Table.Columns.Contains(columnName) && row[columnName] != DBNull.Value)
                {
                    Type propertyType = prop.PropertyType;
                    Type underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

                    object safeValue = Convert.ChangeType(row[columnName], underlyingType);
                    prop.SetValue(obj, safeValue, null);
                }
            }
            return obj;
        }

        public static DataTable ToDataTable<T>(this IEnumerable<T> data) where T : new()
        {
            DataTable dataTable = new DataTable(typeof(T).Name);

            // Lấy danh sách các thuộc tính của đối tượng T
            PropertyInfo[] properties = typeof(T).GetProperties();

            // Tạo các cột trong DataTable từ các thuộc tính của đối tượng
            foreach (var prop in properties)
            {
                var attribute = prop.GetCustomAttributes(typeof(DataColumnAttribute), false).FirstOrDefault() as DataColumnAttribute;
                string columnName = attribute != null ? attribute.ColumnName : prop.Name;

                Type columnType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
                dataTable.Columns.Add(columnName, columnType);
            }

            // Duyệt qua danh sách đối tượng để thêm dữ liệu vào DataTable
            foreach (var item in data)
            {
                DataRow row = dataTable.NewRow();
                foreach (var prop in properties)
                {
                    var attribute = prop.GetCustomAttributes(typeof(DataColumnAttribute), false).FirstOrDefault() as DataColumnAttribute;
                    string columnName = attribute != null ? attribute.ColumnName : prop.Name;

                    object value = prop.GetValue(item) ?? DBNull.Value;
                    row[columnName] = value;
                }
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }
    }
}
