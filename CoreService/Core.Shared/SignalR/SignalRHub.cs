using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Serilog;
using System;
using System.Threading.Tasks;

namespace Core.Shared
{
    [Authorize]
    public class SignalRHub : Hub
    {
        public override Task OnConnectedAsync()
        {
            Log.Information("SignalRHub - Client connected: {0}", Context.ConnectionId);
            Log.Information("SignalRHub - Client UserIdentifier: {0}", Context.UserIdentifier);

            return base.OnConnectedAsync();
        }

        public override Task OnDisconnectedAsync(Exception exception)
        {
            Log.Information("SignalRHub - Clent disconnect: " + Context.ConnectionId);
            return base.OnDisconnectedAsync(exception);
        }

        public async Task SendNotifyToUser(string user, string message)
        {
            // <PERSON><PERSON><PERSON> thông báo đến user có id là "user", dữ liệu được lưu trong Context.UserIdentifier
            Log.Information("Send Notify to User: {0}", user);
            await Clients.User(user).SendAsync(SignalRMethodEnum.SystemReceiveNotify.ToString(), message);
        }
        
        public async Task SendMessage(string message)
        {
            await Clients.All.SendAsync("ReceiveMessage", message);
        }
    }
}