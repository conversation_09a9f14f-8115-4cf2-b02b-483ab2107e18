using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Core.Shared
{
    #region Systems

    public class UserConstants
    {
        public static int AdministratorId => 1;
        public static int UserId => 2;
    }
    public class SelectItemModel
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Note { get; set; } = "";
    }

    public class MessageConstants
    {
        public static string ErrorLogMessage = "An error occurred: ";
        public static string CreateSuccessMessage = "Thêm mới thành công";
        public static string CreateErrorMessage = "Thêm mới thất bại";
        public static string UpdateSuccessMessage = "Cập nhật thành công";
        public static string UpdateErrorMessage = "Cập nhật thất bại";
        public static string DeleteSuccessMessage = "Kết quả xóa";
        public static string DeleteErrorMessage = "Xóa thất bại";
        public static string DeleteItemSuccessMessage = "Xóa thành công";
        public static string DeleteItemErrorMessage = "Xóa không thành công";
        public static string DeleteItemNotFoundMessage = "Không tìm thấy đối tượng";
        public static string GetDataSuccessMessage = "Tải dữ liệu thành công";
        public static string GetDataErrorMessage = "Tải dữ liệu thất bại";
        public static string SignSuccess = "Ký thành công";
    }

    public class ClaimConstants
    {
        public const string USER_ID = "x-user-id";
        public const string USER_EMAIL = "x-user-email";
        public const string USER_NAME = "x-user-name";
        public const string FULL_NAME = "x-full-name";
        public const string AVATAR = "x-avatar";
        public const string APP_ID = "x-app-id";
        public const string ORG_ID = "x-org-id";
        public const string ROLES = "x-role";
        public const string RIGHTS = "x-right";
        public const string PERMISSTTIONS = "x-permission";
        public const string ISSUED_AT = "x-iat";
        public const string EXPIRES_AT = "x-exp";
        public const string CHANNEL = "x-channel";
        public const string REQUEST_ID = "x-request-id";
        public const string API_KEY = "x-api-key";
    }

    public class LanguageConstant
    {
        public const string VI = "vi-VN";
        public const string EN = "en-US";
    }
    #endregion

    #region Business

    /// <summary>
    /// Cachprefix - Lưu ý không được viết hoa vì elastic không nhận chữ hoa trong index
    /// </summary>
    public partial class CacheConstants
    {
        public const string LIST_SELECT = "list-select";

        public const string USER = "user";
        public const string PHAN_HE = "phan_he";
        public const string USER_ACCESS_HE = "user_access_he";
        public const string SYS_APP = "sys_application";
        public const string USER_MAP_ROLE = "user_map_role";
        public const string PERMISSION = "permission";
        public const string ROLE = "role";
        public const string ROLE_MAP_PERMISSION = "role_map_permission";
        public const string ROLE_MAP_USER = "role_map_user";
        public const string FILE_ATTACHMENT = "file_attachment";
        public const string EMAIL_TEMPLATE = "email_template";
        public const string CHUNG_THU_SO = "chung_thu_so";
        public const string MAU_CHU_KY = "mau_chu_ky";
        public const string VISNAM_TAI_KHOAN_KET_NOI = "visnam_tai_khoan_ket_noi";
        public const string API_KEY = "api_key";
        public const string HE = "he";
        public const string KHOA = "khoa";
        public const string KHOA_HOC = "khoa_hoc";
        public const string THAM_SO_HE_THONG = "tham_so_he_thong";
        public const string TKB_GIAO_VIEN = "tkb_giao_vien";
        public const string TON_GIAO = "ton_giao";
        public const string DIEM_CONG_THUC = "diem_cong_thuc";
        public const string QUOC_TICH = "quoc_tich";
        public const string LOP = "lop";
        public const string CHUC_DANH = "chuc_danh";
        public const string GIOI_TINH = "gioi_tinh";
        public const string HOC_VI = "hoc_vi";
        public const string HOC_HAM = "hoc_ham";
        public const string DAN_TOC = "dan_toc";
        public const string NGANH = "nganh";
        public const string HUYEN = "huyen";
        public const string CHUYEN_NGANH = "chuyen_nganh";
        public const string TINH = "tinh";
        public const string KHU_VUC = "khu_vuc";
        public const string NHOM_DOI_TUONG = "nhom_doi_tuong";
        public const string DOI_TUONG = "doi_tuong";
        public const string DOI_TUONG_HOC_BONG = "doi_tuong_hoc_bong";
        public const string CAP_KHEN_THUONG_KY_LUAT = "cap_khen_thuong_ky_luat";
        public const string LOAI_KHEN_THUONG = "loai_khen_thuong";
        public const string HANH_VI = "hanh_vi";
        public const string XU_LY = "xu_ly";
        public const string LOAI_REN_LUYEN = "loai_ren_luyen";
        public const string PHONG_HOC = "phong_hoc";
        public const string HINH_THUC_HOC = "hinh_thuc_hoc";
        public const string CHUC_VU = "chuc_vu";
        public const string HOC_KY_DANG_KY = "hoc_ky_dang_ky";
        public const string XEP_LOAI_REN_LUYEN = "xep_loai_ren_luyen";
        public const string LOAI_GIAY_TO = "loai_giay_to";
        public const string XEP_LOAI_HOC_BONG = "xep_loai_hoc_bong";
        public const string PHUONG = "phuong";
        public const string XA = "xa";
        public const string LOAI_DIEM_THANH_PHAN = "Loai_diem_thanh_phan";
        public const string XEP_LOAI_HOC_TAP_THANG_DIEM_10 = "Xep_loai_hoc_tap_thang_diem_10";
        public const string XEP_HANG_HOC_LUC = "Xep_loai_hoc_tap";
        public const string XEP_LOAI_TOT_NGHIEP_THANG_4 = "Xep_loai_tot_nghiep_thang_4";
        public const string XEP_HANG_TOT_NGHIEP_THANG_DIEM_10 = "Xep_hang_tot_nghiep_thang_diem_10";
        public const string LOAI_CHUNG_CHI = "Loai_chung_chi";
        public const string XEP_LOAI_CHUNG_CHI = "Xep_loai_chung_chi";
        public const string DIEM_REN_LUYEN_QUY_DOI = "diem_ren_luyen_quy_doi";
        public const string LOAI_QUYET_DINH = "loai_quyet_dinh";
        public const string LOAI_RANG_BUOC = "loai_rang_buoc";
        public const string TOA_NHA = "toa_nha";
        public const string CO_SO_DAO_TAO = "co_so_dao_tao";
        public const string BO_MON = "bo_mon";
        public const string DOI_TUONG_HOC_PHI = "doi_tuong_hoc_phi";
        public const string NHOM_CHUNG_CHI = "nhom_chung_chi";
        public const string LOAI_THU_CHI = "loai_thu_chi";
        public const string HINH_THUC_THI = "hinh_thuc_thi";
        public const string NOI_THUC_TAP = "noi_thuc_tap";
        public const string THANH_PHAN_MON_THEO_HE = "thanh_phan_mon_theo_he";
        public const string DIEM_QUY_DOI = "diem_quy_doi";
        public const string CHUONG_TRINH_DAO_TAO_KIEN_THUC = "chuong_trinh_dao_tao_kien_thuc";
        public const string CAP_REN_LUYEN = "cap_ren_luyen";
        public const string PHONG = "phong";
        public const string XEP_HANG_NAM_DAO_TAO = "xep_hang_nam_dao_tao";
        public const string XEP_LOAI_HOC_TAP_THANG_4 = "xep_hoc_tap_thang_4";
        public const string TANG = "tang";
        public const string MON_HOC = "mon_hoc";
        public const string LOAI_PHONG = "loai_phong";
        public const string CHUONG_TRINH_DAO_TAO_RANG_BUOC = "chuong_trinh_dao_tao_rang_buoc";
        public const string HOC_PHAN_TUONG_DUONG = "hoc_phan_tuong_duong";
        public const string CHUONG_TRINH_DAO_TAO_CHI_TIET = "chuong_trinh_dao_tao_chi_tiet";
        public const string CHUONG_TRINH_DAO_TAO = "chuong_trinh_dao_tao";
        public const string GIAO_AN = "giao_an";
        public const string CHI_TIEU_TUYEN_SINH = "chi_tieu_tuyen_sinh";
        public const string THAM_SO_QUY_CHE = "tham_so_quy_che";
        public const string MUC_HUONG_BHYT = "muc_huong_bhyt";
        public const string VUNG = "vung";
        public const string BENH_VIEN = "benh_vien";
        public const string PHUONG_AN = "phuong_an";
        public const string PHUONG_THUC_DONG = "phuong_thuc_dong";
        public const string BAC_DAO_TAO = "bac_dao_tao";
        public const string WORK_FLOW = "work_flow";
        public const string SV_LOP_TIN_CHI = "lop_tin_chi";
    }

    public partial class LogConstants
    {
        #region Khởi tạo dữ liệu
        public const string ACTION_SEED_DATA = "Khởi tạo dữ liệu ứng dụng";
        #endregion

        #region Login
        public const string ACTION_LOGIN_SUCCESS = "Đăng nhập thành công";
        public const string ACTION_LOGIN_FAIL = "Đăng nhập thất bại";
        public const string ACTION_LOGOUT = "Đăng xuất";
        public const string ACTION_WSO2_LOGIN = "Truy cập hệ thống";
        #endregion

        #region System Application
        public const string ACTION_SYS_APP_CREATE = "Thêm mới ứng dụng";
        public const string ACTION_SYS_APP_UPDATE = "Cập nhật ứng dụng";
        public const string ACTION_SYS_APP_DELETE = "Xóa ứng dụng";
        #endregion

        #region Organization
        public const string ACTION_ORG_CREATE = "Thêm mới đơn vị/phòng ban";
        public const string ACTION_ORG_UPDATE = "Cập nhật đơn vị/phòng ban";
        public const string ACTION_ORG_DELETE = "Xóa đơn vị/phòng ban";
        #endregion

        #region User
        public const string ACTION_USER_CREATE = "Thêm mới người dùng";
        public const string ACTION_USER_FORGOT_PASS = "Quên mật khẩu";
        public const string ACTION_USER_SENDMAIL_FORGOT_PASS = "Gửi mail quên mật khẩu";
        public const string ACTION_USER_UPDATE_PASS_FORGOT = "Cập nhật mật khẩu từ quên mật khẩu";
        public const string ACTION_USER_UPDATE = "Cập nhật người dùng";
        public const string ACTION_USER_UPDATE_USER_INFO = "Cập nhật thông tin cá nhân";
        public const string ACTION_USER_UPDATE_LOCK = "Khóa/mở khóa người dùng";
        public const string ACTION_USER_DELETE = "Xóa người dùng";
        public const string ACTION_USER_DELETE_ASSIGN_CLASS = "Xóa quyền quản lý lớp";
        public const string ACTION_USER_ASSIGN_CLASS = "Gán lớp quản lý";
        #endregion

        #region Role
        public const string ACTION_ROLE_CREATE = "Thêm mới nhóm người dùng";
        public const string ACTION_ROLE_UPDATE = "Cập nhật nhóm người dùng";
        public const string ACTION_ROLE_DELETE = "Xóa nhóm người dùng";
        public const string ACTION_ROLE_UPDATE_USER = "Cập nhật phân quyền người dùng thuộc nhóm";
        public const string ACTION_ROLE_UPDATE_PERMISSION = "Cập nhật phân quyền chức năng thuộc nhóm";
        #endregion

        #region Right
        public const string ACTION_RIGHT_CREATE = "Thêm mới quyền người dùng";
        public const string ACTION_RIGHT_UPDATE = "Cập nhật quyền người dùng";
        public const string ACTION_RIGHT_DELETE = "Xóa quyền người dùng";
        #endregion

        #region Device
        public const string DEVICE_WEB = "Web";
        public const string DEVICE_MOBILE = "mobile";

        #endregion

        #region Email Template
        public const string ACTION_EMAIL_TEMPLATE_CREATE = "Thêm mới mẫu email";
        public const string ACTION_EMAIL_TEMPLATE_UPDATE = "Cập nhật mẫu email";
        public const string ACTION_EMAIL_TEMPLATE_DELETE = "Xóa mẫu email";
        #endregion

        #region Chứng thư số
        public const string ACTION_CHUNG_THU_SO_CREATE = "Thêm mới chứng thư số";
        public const string ACTION_CHUNG_THU_SO_UPDATE = "Cập nhật chứng thư số";
        public const string ACTION_CHUNG_THU_SO_DELETE = "Xóa chứng thư số";
        #endregion

        #region Mẫu chữ ký
        public const string ACTION_MAU_CHU_KY_CREATE = "Thêm mới mẫu chữ ký";
        public const string ACTION_MAU_CHU_KY_UPDATE = "Cập nhật mẫu chữ ký";
        public const string ACTION_MAU_CHU_KY_DELETE = "Xóa mẫu chữ ký";
        #endregion

        #region Visnam tài khoản kết nối
        public const string ACTION_VISNAM_TAI_KHOAN_KET_NOI_CREATE = "Thêm mới tài khoản kết nối Visnam";
        public const string ACTION_VISNAM_TAI_KHOAN_KET_NOI_UPDATE = "Cập nhật tài khoản kết nối Visnam";
        public const string ACTION_VISNAM_TAI_KHOAN_KET_NOI_DELETE = "Xóa tài khoản kết nối Visnam";
        #endregion

        #region API Key
        public const string ACTION_API_KEY_CREATE = "Thêm mới API Key";
        public const string ACTION_API_KEY_UPDATE = "Cập nhật API Key";
        public const string ACTION_API_KEY_DELETE = "Xóa API Key";
        #endregion
    }

    public class QueryFilter
    {
        public const int DefaultPageNumber = 1;
        public const int DefaultPageSize = 20;
    }

    #endregion

    public class TableDataModel
    {
        public string TableName { get; set; }
        public List<Dictionary<string, string>> TableData { get; set; }
    }

    public class KeyValueModel
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public static class RabbitMQHeaderConstant
    {
        public static string TraceId = "TraceId";
        public static string UserId = "UserId";
    }

    public static class MongoCollections
    {
        public static string SysLog = "sys_log";
        public static string ForgotPasswordLog = "forgot_password_log";
        public static string NotifyLog = "notify_log";
        public static string SendMailLog = "send_mail_log";
    }

    public class Constant
    {
        public static string DefaultCorsPolicyName = "Default";
        public static string DateFormatDdMMyyyy = "dd/MM/yyyy";
    }

    public enum SendMailStatus
    {
        DontSend = 0,
        Error = 1, 
        Success = 2,
    }

    public class SystemLogConstants
    {
        #region Hệ
        public const string ACTION_HE_CREATE = "Thêm mới hệ";
        public const string ACTION_HE_UPDATE = "Cập nhật hệ";
        public const string ACTION_HE_DELETE = "Xóa hệ";
        public const string ACTION_HE_CREATE_MANY = "Import file excel hệ";

        #endregion

        #region Tôn giáo
        public const string ACTION_TON_GIAO_CREATE = "Thêm mới tôn giáo";
        public const string ACTION_TON_GIAO_UPDATE = "Cập nhật tôn giáo";
        public const string ACTION_TON_GIAO_DELETE = "Xóa tôn giáo";
        public const string ACTION_TON_GIAO_CREATE_MANY = "Import file excel tôn giáo";

        #endregion

        #region Điểm công thức
        public const string ACTION_DIEM_CONG_THUC_CREATE = "Thêm mới điểm công thức";
        public const string ACTION_DIEM_CONG_THUC_UPDATE = "Cập nhật điểm công thức";
        public const string ACTION_DIEM_CONG_THUC_DELETE = "Xóa điểm công thức";
        public const string ACTION_DIEM_CONG_THUC_CREATE_MANY = "Import file excel điểm công thức";

        #endregion

        #region chương trình đào tạo ràng buộc
        public const string ACTION_CHUONG_TRINH_DAO_TAO_RANG_BUOC_CREATE = "Thêm mới chương trình đào tạo ràng buộc";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_RANG_BUOC_UPDATE = "Cập nhật chương trình đào tạo ràng buộc";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_RANG_BUOC_DELETE = "Xóa chương trình đào tạo ràng buộc";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_RANG_BUOC_CREATE_MANY = "Import file excel chương trình đào tạo ràng buộc";

        #endregion

        #region Quốc tịch
        public const string ACTION_QUOC_TICH_CREATE = "Thêm mới quốc tịch";
        public const string ACTION_QUOC_TICH_UPDATE = "Cập nhật quốc tịch";
        public const string ACTION_QUOC_TICH_DELETE = "Xóa quốc tịch";
        public const string ACTION_QUOC_TICH_CREATE_MANY = "Import file excel quốc tịch";

        #endregion

        #region Chức danh
        public const string ACTION_CHUC_DANH_CREATE = "Thêm mới chức danh";
        public const string ACTION_CHUC_DANH_UPDATE = "Cập nhật chức danh";
        public const string ACTION_CHUC_DANH_DELETE = "Xóa chức danh";
        public const string ACTION_CHUC_DANH_CREATE_MANY = "Import file excel chức danh";

        #endregion

        #region Giới tính
        public const string ACTION_GIOI_TINH_CREATE = "Thêm mới giới tính";
        public const string ACTION_GIOI_TINH_UPDATE = "Cập nhật giới tính";
        public const string ACTION_GIOI_TINH_DELETE = "Xóa giới tính";
        public const string ACTION_GIOI_TINH_CREATE_MANY = "Import file excel giới tính";

        #endregion

        #region Học vị
        public const string ACTION_HOC_VI_CREATE = "Thêm mới học vị";
        public const string ACTION_HOC_VI_UPDATE = "Cập nhật học vị";
        public const string ACTION_HOC_VI_DELETE = "Xóa học vị";
        public const string ACTION_HOC_VI_CREATE_MANY = "Import file excel học vị";

        #endregion

        #region Học hàm
        public const string ACTION_HOC_HAM_CREATE = "Thêm mới học hàm";
        public const string ACTION_HOC_HAM_UPDATE = "Cập nhật học hàm";
        public const string ACTION_HOC_HAM_DELETE = "Xóa học hàm";
        public const string ACTION_HOC_HAM_CREATE_MANY = "Import file excel học hàm";

        #endregion

        #region Dân tộc
        public const string ACTION_DAN_TOC_CREATE = "Thêm mới dân tộc";
        public const string ACTION_DAN_TOC_UPDATE = "Cập nhật dân tộc";
        public const string ACTION_DAN_TOC_DELETE = "Xóa dân tộc";
        public const string ACTION_DAN_TOC_CREATE_MANY = "Import file excel dân tộc";

        #endregion

        #region Khoa
        public const string ACTION_KHOA_CREATE = "Thêm mới khoa";
        public const string ACTION_KHOA_UPDATE = "Cập nhật khoa";
        public const string ACTION_KHOA_DELETE = "Xóa khoa";
        public const string ACTION_KHOA_CREATE_MANY = "Import file excel khoa";

        #endregion

        #region Ngành
        public const string ACTION_NGANH_CREATE = "Thêm mới ngành";
        public const string ACTION_NGANH_UPDATE = "Cập nhật ngành";
        public const string ACTION_NGANH_DELETE = "Xóa ngành";
        public const string ACTION_NGANH_CREATE_MANY = "Import file excel ngành";

        #endregion

        #region Huyện
        public const string ACTION_HUYEN_CREATE = "Thêm mới huyện";
        public const string ACTION_HUYEN_UPDATE = "Cập nhật huyện";
        public const string ACTION_HUYEN_DELETE = "Xóa huyện";
        public const string ACTION_HUYEN_CREATE_MANY = "Import file excel huyện";

        #endregion

        #region Chuyên ngành
        public const string ACTION_CHUYEN_NGANH_CREATE = "Thêm mới chuyên ngành";
        public const string ACTION_CHUYEN_NGANH_UPDATE = "Cập nhật chuyên ngành";
        public const string ACTION_CHUYEN_NGANH_DELETE = "Xóa chuyên ngành";
        public const string ACTION_CHUYEN_NGANH_CREATE_MANY = "Import file excel chuyên ngành";

        #endregion

        #region Tỉnh
        public const string ACTION_TINH_CREATE = "Thêm mới tỉnh";
        public const string ACTION_TINH_UPDATE = "Cập nhật tỉnh";
        public const string ACTION_TINH_DELETE = "Xóa tỉnh";
        public const string ACTION_TINH_CREATE_MANY = "Import file excel tỉnh";

        #endregion

        #region Khu vực
        public const string ACTION_KHU_VUC_CREATE = "Thêm mới Khu vực";
        public const string ACTION_KHU_VUC_UPDATE = "Cập nhật Khu vực";
        public const string ACTION_KHU_VUC_DELETE = "Xóa Khu vực";
        public const string ACTION_KHU_VUC_CREATE_MANY = "Import file excel Khu vực";

        #endregion

        #region Nhóm đối tượng
        public const string ACTION_NHOM_DOI_TUONG_CREATE = "Thêm mới nhóm đối tượng";
        public const string ACTION_NHOM_DOI_TUONG_UPDATE = "Cập nhật nhóm đối tượng";
        public const string ACTION_NHOM_DOI_TUONG_DELETE = "Xóa nhóm đối tượng";
        public const string ACTION_NHOM_DOI_TUONG_CREATE_MANY = "Import file excel nhóm đối tượng";

        #endregion

        #region Đối tượng
        public const string ACTION_DOI_TUONG_CREATE = "Thêm mới đối tượng";
        public const string ACTION_DOI_TUONG_UPDATE = "Cập nhật đối tượng";
        public const string ACTION_DOI_TUONG_DELETE = "Xóa  đối tượng";
        public const string ACTION_DOI_TUONG_CREATE_MANY = "Import file excel đối tượng";

        #endregion

        #region Đối tượng học bổng
        public const string ACTION_DOI_TUONG_HOC_BONG_CREATE = "Thêm mới đối tượng học bổng";
        public const string ACTION_DOI_TUONG_HOC_BONG_UPDATE = "Cập nhật đối tượng học bổng";
        public const string ACTION_DOI_TUONG_HOC_BONG_DELETE = "Xóa đối tượng học bổng";
        public const string ACTION_DOI_TUONG_HOC_BONG_CREATE_MANY = "Import file excel đối tượng học bổng";

        #endregion

        #region Cấp khen thưởng kỷ luật
        public const string ACTION_CAP_KHEN_THUONG_KY_LUAT_CREATE = "Thêm mới cấp khen thưởng kỷ luật";
        public const string ACTION_CAP_KHEN_THUONG_KY_LUAT_UPDATE = "Cập nhật cấp khen thưởng kỷ luật";
        public const string ACTION_CAP_KHEN_THUONG_KY_LUAT_DELETE = "Xóa  cấp khen thưởng kỷ luật";
        public const string ACTION_CAP_KHEN_THUONG_KY_LUAT_CREATE_MANY = "Import file excel cấp khen thưởng kỷ luật";

        #endregion

        #region Loại khen thưởng
        public const string ACTION_LOAI_KHEN_THUONG_CREATE = "Thêm mới loại khen thưởng";
        public const string ACTION_LOAI_KHEN_THUONG_UPDATE = "Cập nhật loại khen thưởng";
        public const string ACTION_LOAI_KHEN_THUONG_DELETE = "Xóa  loại khen thưởng";
        public const string ACTION_LOAI_KHEN_THUONG_CREATE_MANY = "Import file excel loại khen thưởng";

        #endregion


        #region Hành vi kỷ luật
        public const string ACTION_HANH_VI_CREATE = "Thêm mới hành vi kỷ luật";
        public const string ACTION_HANH_VI_UPDATE = "Cập nhật hành vi kỷ luật";
        public const string ACTION_HANH_VI_DELETE = "Xóa  hành vi kỷ luật";
        public const string ACTION_HANH_VI_CREATE_MANY = "Import file excel hành vi kỷ luật";

        #endregion

        #region Xử lý kỷ luật
        public const string ACTION_XU_LY_CREATE = "Thêm mới xử lý kỷ luật";
        public const string ACTION_XU_LY_UPDATE = "Cập nhật xử lý kỷ luật";
        public const string ACTION_XU_LY_DELETE = "Xóa  xử lý kỷ luật";
        public const string ACTION_XU_LY_CREATE_MANY = "Import file excel xử lý kỷ luật";

        #endregion


        #region Loại rèn luyện
        public const string ACTION_LOAI_REN_LUYEN_CREATE = "Thêm mới loại rèn luyện";
        public const string ACTION_LOAI_REN_LUYEN_UPDATE = "Cập nhật loại rèn luyện";
        public const string ACTION_LOAI_REN_LUYEN_DELETE = "Xóa  loại rèn luyện";
        public const string ACTION_LOAI_REN_LUYEN_CREATE_MANY = "Import file excel loại rèn luyện";

        #endregion

        #region Phòng học
        public const string ACTION_PHONG_HOC_CREATE = "Thêm mới phòng học";
        public const string ACTION_PHONG_HOC_UPDATE = "Cập nhật phòng học";
        public const string ACTION_PHONG_HOC_DELETE = "Xóa  phòng học";
        public const string ACTION_PHONG_HOC_CREATE_MANY = "Import file excel phòng học";

        #endregion

        #region Hình thức học
        public const string ACTION_HINH_THUC_HOC_CREATE = "Thêm mới hình thức học";
        public const string ACTION_HINH_THUC_HOC_UPDATE = "Cập nhật hình thức học";
        public const string ACTION_HINH_THUC_HOC_DELETE = "Xóa  hình thức học";
        public const string ACTION_HINH_THUC_HOC_CREATE_MANY = "Import file excel hình thức học";

        #endregion

        #region Chức vụ
        public const string ACTION_CHUC_VU_CREATE = "Thêm mới chức vụ";
        public const string ACTION_CHUC_VU_UPDATE = "Cập nhật chức vụ";
        public const string ACTION_CHUC_VU_DELETE = "Xóa  chức vụ";
        public const string ACTION_CHUC_VU_CREATE_MANY = "Import file excel chức vụ";

        #endregion

        #region Học kỳ đăng ký
        public const string ACTION_HOC_KY_DANG_KY_CREATE = "Thêm mới học kỳ đăng ký";
        public const string ACTION_HOC_KY_DANG_KY_UPDATE = "Cập nhật học kỳ đăng ký";
        public const string ACTION_HOC_KY_DANG_KY_DELETE = "Xóa  học kỳ đăng ký";
        public const string ACTION_HOC_KY_DANG_KY_CREATE_MANY = "Import file excel học kỳ đăng ký";

        #endregion

        #region Xếp loại rèn luyện
        public const string ACTION_XEP_LOAI_REN_LUYEN_CREATE = "Thêm mới xếp loại rèn luyện";
        public const string ACTION_XEP_LOAI_REN_LUYEN_UPDATE = "Cập nhật xếp loại rèn luyện";
        public const string ACTION_XEP_LOAI_REN_LUYEN_DELETE = "Xóa xếp loại rèn luyện";
        public const string ACTION_XEP_LOAI_REN_LUYEN_CREATE_MANY = "Import file excel xếp loại rèn luyện";

        #endregion

        #region Loại giấy tờ
        public const string ACTION_LOAI_GIAY_TO_CREATE = "Thêm mới loại giấy tờ";
        public const string ACTION_LOAI_GIAY_TO_UPDATE = "Cập nhật loại giấy tờ";
        public const string ACTION_LOAI_GIAY_TO_DELETE = "Xóa loại giấy tờ";
        public const string ACTION_LOAI_GIAY_TO_CREATE_MANY = "Import file excel loại giấy tờ";

        #endregion


        #region Xếp loại học bổng
        public const string ACTION_XEP_LOAI_HOC_BONG_CREATE = "Thêm mới xếp loại học bổng";
        public const string ACTION_XEP_LOAI_HOC_BONG_UPDATE = "Cập nhật xếp loại học bổng";
        public const string ACTION_XEP_LOAI_HOC_BONG_DELETE = "Xóa xếp loại học bổng";
        public const string ACTION_XEP_LOAI_HOC_BONG_CREATE_MANY = "Import file excel xếp loại học bổng";

        #endregion

        #region Phường
        public const string ACTION_PHUONG_CREATE = "Thêm mới phường";
        public const string ACTION_PHUONG_UPDATE = "Cập nhật phường";
        public const string ACTION_PHUONG_DELETE = "Xóa phường";
        public const string ACTION_PHUONG_CREATE_MANY = "Import file excel phường";

        #endregion

        #region Xã
        public const string ACTION_XA_CREATE = "Thêm mới xã";
        public const string ACTION_XA_UPDATE = "Cập nhật xã";
        public const string ACTION_XA_DELETE = "Xóa xã";
        public const string ACTION_XA_CREATE_MANY = "Import file excel xã";

        #endregion

        #region Loại điểm thành phần
        public const string ACTION_LOAI_DIEM_THANH_PHAN_CREATE = "Thêm mới Loại điểm thành phần";
        public const string ACTION_LOAI_DIEM_THANH_PHAN_UPDATE = "Cập nhật Loại điểm thành phần";
        public const string ACTION_LOAI_DIEM_THANH_PHAN_DELETE = "Xóa Loại điểm thành phần";
        public const string ACTION_LOAI_DIEM_THANH_PHAN_CREATE_MANY = "Import file excel Loại điểm thành phần";

        #endregion

        #region Xếp loại học tập thang điểm 10
        public const string ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_CREATE = "Thêm mới xếp loại học tập thang điểm 10";
        public const string ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_UPDATE = "Cập nhật xếp loại học tập thang điểm 10";
        public const string ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_DELETE = "Xóa xếp loại học tập thang điểm 10";
        public const string ACTION_XEP_LOAI_HOC_TAP_THANG_DIEM_10_CREATE_MANY = "Import file excel xếp loại học tập thang điểm 10";

        #endregion

        #region Xếp hạng năm đào tạo
        public const string ACTION_XEP_HANG_NAM_DAO_TAO_CREATE = "Thêm mới xếp hạng năm đào tạo";
        public const string ACTION_XEP_HANG_NAM_DAO_TAO_UPDATE = "Cập nhật xếp hạng năm đào tạo";
        public const string ACTION_XEP_HANG_NAM_DAO_TAO_DELETE = "Xóa xếp hạng năm đào tạo";
        public const string ACTION_XEP_HANG_NAM_DAO_TAO_CREATE_MANY = "Import file excel xếp hạng năm đào tạo";

        #endregion

        #region Xếp hạng học lực
        public const string ACTION_XEP_HANG_HOC_LUC_CREATE = "Thêm mới Xếp hạng học lực";
        public const string ACTION_XEP_HANG_HOC_LUC_UPDATE = "Cập nhật Xếp hạng học lực";
        public const string ACTION_XEP_HANG_HOC_LUC_DELETE = "Xóa Xếp hạng học lực";
        public const string ACTION_XEP_HANG_HOC_LUC_CREATE_MANY = "Import file excel Xếp hạng học lực";

        #endregion

        #region Xếp loại học tập thang 4
        public const string ACTION_XEP_LOAI_HOC_TAP_THANG_4_CREATE = "Thêm mới Xếp loại học tập thang 4";
        public const string ACTION_XEP_LOAI_HOC_TAP_THANG_4_UPDATE = "Cập nhật Xếp loại học tập thang 4";
        public const string ACTION_XEP_LOAI_HOC_TAP_THANG_4_DELETE = "Xóa Xếp loại học tập thang 4";
        public const string ACTION_XEP_LOAI_HOC_TAP_THANG_4_CREATE_MANY = "Import file excel Xếp loại học tập thang 4";

        #endregion

        #region Xếp loại tốt nghiệp thang 4
        public const string ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_CREATE = "Thêm mới Xếp loại tốt nghiệp thang 4";
        public const string ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_UPDATE = "Cập nhật Xếp loại tốt nghiệp thang 4";
        public const string ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_DELETE = "Xóa Xếp loại tốt nghiệp thang 4";
        public const string ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_CREATE_MANY = "Import file excel Xếp loại tốt nghiệp thang 4";

        #endregion

        #region Xếp hạng tốt nghiệp thang điểm 10
        public const string ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_CREATE = "Thêm mới xếp hạng tốt nghiệp thang điểm 10";
        public const string ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_UPDATE = "Cập nhật xếp hạng tốt nghiệp thang điểm 10";
        public const string ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_DELETE = "Xóa xếp hạng tốt nghiệp thang điểm 10";
        public const string ACTION_XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_CREATE_MANY = "Import file excel xếp hạng tốt nghiệp thang điểm 10";

        #endregion

        #region Loại chúng chỉ
        public const string ACTION_LOAI_CHUNG_CHI_CREATE = "Thêm mới Loại chúng chỉ";
        public const string ACTION_LOAI_CHUNG_CHI_UPDATE = "Cập nhật Loại chúng chỉ";
        public const string ACTION_LOAI_CHUNG_CHI_DELETE = "Xóa Loại chúng chỉ";
        public const string ACTION_LOAI_CHUNG_CHI_CREATE_MANY = "Import file excel Loại chúng chỉ";

        #endregion

        #region Xếp loại chúng chỉ
        public const string ACTION_XEP_LOAI_CHUNG_CHI_CREATE = "Thêm mới Xếp loại chúng chỉ";
        public const string ACTION_XEP_LOAI_CHUNG_CHI_UPDATE = "Cập nhật Xếp loại chúng chỉ";
        public const string ACTION_XEP_LOAI_CHUNG_CHI_DELETE = "Xóa Xếp loại chúng chỉ";
        public const string ACTION_XEP_LOAI_CHUNG_CHI_CREATE_MANY = "Import file excel Xếp loại chúng chỉ";

        #endregion


        #region Điểm rèn luyện quy đổi
        public const string ACTION_DIEM_REN_LUYEN_QUY_DOI_CREATE = "Thêm mới điểm rèn luyện quy đổi";
        public const string ACTION_DIEM_REN_LUYEN_QUY_DOI_UPDATE = "Cập nhật điểm rèn luyện quy đổi";
        public const string ACTION_DIEM_REN_LUYEN_QUY_DOI_DELETE = "Xóa điểm rèn luyện quy đổi";
        public const string ACTION_DIEM_REN_LUYEN_QUY_DOI_CREATE_MANY = "Import file excel điểm rèn luyện quy đổi";

        #endregion

        #region Loại quyết định
        public const string ACTION_LOAI_QUYET_DINH_CREATE = "Thêm mới loại quyết định";
        public const string ACTION_LOAI_QUYET_DINH_UPDATE = "Cập nhật loại quyết định";
        public const string ACTION_LOAI_QUYET_DINH_DELETE = "Xóa loại quyết định";
        public const string ACTION_LOAI_QUYET_DINH_CREATE_MANY = "Import file excel loại quyết định";

        #endregion

        #region Tòa nhà
        public const string ACTION_TOA_NHA_CREATE = "Thêm mới tòa nhà";
        public const string ACTION_TOA_NHA_UPDATE = "Cập nhật tòa nhà";
        public const string ACTION_TOA_NHA_DELETE = "Xóa tòa nhà";
        public const string ACTION_TOA_NHA_CREATE_MANY = "Import file excel tòa nhà";

        #endregion

        #region Cơ sở đào tạo
        public const string ACTION_CO_SO_DAO_TAO_CREATE = "Thêm mới cơ sở đào tạo";
        public const string ACTION_CO_SO_DAO_TAO_UPDATE = "Cập nhật cơ sở đào tạo";
        public const string ACTION_CO_SO_DAO_TAO_DELETE = "Xóa cơ sở đào tạo";
        public const string ACTION_CO_SO_DAO_TAO_CREATE_MANY = "Import file excel cơ sở đào tạo";

        #endregion

        #region Bộ môn
        public const string ACTION_BO_MON_CREATE = "Thêm mới bộ môn";
        public const string ACTION_BO_MON_UPDATE = "Cập nhật bộ môn";
        public const string ACTION_BO_MON_DELETE = "Xóa bộ môn";
        public const string ACTION_BO_MON_CREATE_MANY = "Import file excel bộ môn";

        #endregion

        #region Đối tượng học phí
        public const string ACTION_DOI_TUONG_HOC_PHI_CREATE = "Thêm mới đối tượng học phí";
        public const string ACTION_DOI_TUONG_HOC_PHI_UPDATE = "Cập nhật đối tượng học phí";
        public const string ACTION_DOI_TUONG_HOC_PHI_DELETE = "Xóa đối tượng học phí";
        public const string ACTION_DOI_TUONG_HOC_PHI_CREATE_MANY = "Import file excel đối tượng học phí";

        #endregion

        #region Loại thu chi
        public const string ACTION_LOAI_THU_CHI_CREATE = "Thêm mới loại thu chi";
        public const string ACTION_LOAI_THU_CHI_UPDATE = "Cập nhật loại thu chi";
        public const string ACTION_LOAI_THU_CHI_DELETE = "Xóa loại thu chi";
        public const string ACTION_LOAI_THU_CHI_CREATE_MANY = "Import file excel loại thu chi";

        #endregion

        #region Lớp
        public const string ACTION_LOP_CREATE = "Thêm mới lớp";
        public const string ACTION_LOP_UPDATE = "Cập nhật lớp";
        public const string ACTION_LOP_DELETE = "Xóa lớp";

        #endregion

        #region Hình thức thi
        public const string ACTION_HINH_THUC_THI_CREATE = "Thêm mới hình thức thi";
        public const string ACTION_HINH_THUC_THI_UPDATE = "Cập nhật hình thức thi";
        public const string ACTION_HINH_THUC_THI_DELETE = "Xóa hình thức thi";
        public const string ACTION_HINH_THUC_THI_CREATE_MANY = "Import file excel hình thức thi";

        #endregion

        #region Nơi thực tập
        public const string ACTION_NOI_THUC_TAP_CREATE = "Thêm mới nơi thực tập";
        public const string ACTION_NOI_THUC_TAP_UPDATE = "Cập nhật nơi thực tập";
        public const string ACTION_NOI_THUC_TAP_DELETE = "Xóa nơi thực tập";
        public const string ACTION_NOI_THUC_TAP_CREATE_MANY = "Import file excel nơi thực tập";

        #endregion

        #region Thành phần môn theo hệ
        public const string ACTION_THANH_PHAN_MON_THEO_HE_CREATE = "Thêm mới thành phần môn theo hệ";
        public const string ACTION_THANH_PHAN_MON_THEO_HE_UPDATE = "Cập nhật thành phần môn theo hệ";
        public const string ACTION_THANH_PHAN_MON_THEO_HE_DELETE = "Xóa thành phần môn theo hệ";
        public const string ACTION_THANH_PHAN_MON_THEO_HE_CREATE_MANY = "Import file excel thành phần môn theo hệ";

        #endregion

        #region Điểm quy đổi
        public const string ACTION_DIEM_QUY_DOI_CREATE = "Thêm mới điểm quy đổi";
        public const string ACTION_DIEM_QUY_DOI_UPDATE = "Cập nhật điểm quy đổi";
        public const string ACTION_DIEM_QUY_DOI_DELETE = "Xóa điểm quy đổi";
        public const string ACTION_DIEM_QUY_DOI_CREATE_MANY = "Import file excel điểm quy đổi";

        #endregion

        #region Chương trình đào tạo kiến thức
        public const string ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_CREATE = "Thêm mới chương trình đào tạo kiến thức";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_UPDATE = "Cập nhật chương trình đào tạo kiến thức";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_DELETE = "Xóa chương trình đào tạo kiến thức";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_CREATE_MANY = "Import file excel chương trình đào tạo kiến thức";

        #endregion

        #region Cấp rèn luyện
        public const string ACTION_CAP_REN_LUYEN_CREATE = "Thêm mới cấp rèn luyện";
        public const string ACTION_CAP_REN_LUYEN_UPDATE = "Cập nhật cấp rèn luyện";
        public const string ACTION_CAP_REN_LUYEN_DELETE = "Xóa cấp rèn luyện";
        public const string ACTION_CAP_REN_LUYEN_CREATE_MANY = "Import file excel cấp rèn luyện";

        #endregion

        #region Phòng
        public const string ACTION_PHONG_CREATE = "Thêm mới phòng";
        public const string ACTION_PHONG_UPDATE = "Cập nhật phòng";
        public const string ACTION_PHONG_DELETE = "Xóa phòng";
        public const string ACTION_PHONG_CREATE_MANY = "Import file excel phòng";

        #endregion


        #region Nhóm chứng chỉ
        public const string ACTION_NHOM_CHUNG_CHI_CREATE = "Thêm mới nhóm chứng chỉ";
        public const string ACTION_NHOM_CHUNG_CHI_UPDATE = "Cập nhật nhóm chứng chỉ";
        public const string ACTION_NHOM_CHUNG_CHI_DELETE = "Xóa nhóm chứng chỉ";
        public const string ACTION_NHOM_CHUNG_CHI_CREATE_MANY = "Import file excel nhóm chứng chỉ";

        #endregion

        #region Tầng
        public const string ACTION_TANG_CREATE = "Thêm mới tầng";
        public const string ACTION_TANG_UPDATE = "Cập nhật tầng";
        public const string ACTION_TANG_DELETE = "Xóa tầng";
        public const string ACTION_TANG_CREATE_MANY = "Import file excel tầng";

        #endregion
        #region Môn học
        public const string ACTION_MON_HOC_CREATE = "Thêm mới môn học";
        public const string ACTION_MON_HOC_UPDATE = "Cập nhật môn học";
        public const string ACTION_MON_HOC_DELETE = "Xóa môn học";
        public const string ACTION_MON_HOC_CREATE_MANY = "Import file excel môn học";

        #endregion

        #region Loại phòng
        public const string ACTION_LOAI_PHONG_CREATE = "Thêm mới loại phòng";
        public const string ACTION_LOAI_PHONG_UPDATE = "Cập nhật loại phòng";
        public const string ACTION_LOAI_PHONG_DELETE = "Xóa loại phòng";
        public const string ACTION_LOAI_PHONG_CREATE_MANY = "Import file excel loại phòng";

        #endregion

        #region Chương trình đào tạo
        public const string ACTION_CHUONG_TRINH_DAO_TAO_CREATE = "Thêm mới chương trình đào tạo";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_UPDATE = "Cập nhật chương trình đào tạo";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_DELETE = "Xóa chương trình đào tạo";
        #endregion

        #region Học phần tương đương
        public const string ACTION_HOC_PHAN_TUONG_DUONG_CREATE = "Thêm mới học phần tương đương";
        public const string ACTION_HOC_PHAN_TUONG_DUONG_UPDATE = "Cập nhật học phần tương đương";
        public const string ACTION_HOC_PHAN_TUONG_DUONG_DELETE = "Xóa học phần tương đương";
        #endregion

         #region Chương trình đào tạo chi tiết
        public const string ACTION_CHUONG_TRINH_DAO_TAO_CHI_TIET_CREATE = "Thêm mới chương trình đào tạo chi tiết";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_CHI_TIET_UPDATE = "Cập nhật chương trình đào tạo chi tiết";
        public const string ACTION_CHUONG_TRINH_DAO_TAO_CHI_TIET_DELETE = "Xóa chương trình đào tạo chi tiết";
        #endregion

        #region Generate PDF
        public const string ACTION_GENERATE_PDF_FROM_WORD_BASE64_CREATE = "tạo pdf từ word base64";
        #endregion

        #region Giáo án
        public const string ACTION_GIAO_AN_CREATE = "Thêm mới giáo án";
        public const string ACTION_GIAO_AN_UPDATE = "Cập nhật giáo án";
        public const string ACTION_GIAO_AN_DELETE = "Xóa giáo án";
        #endregion


        #region Chỉ tiêu tuyển sinh
        public const string ACTION_CHI_TIEU_TUYEN_SINH_CREATE = "Thêm mới chỉ tiêu tuyển sinh";
        public const string ACTION_CHI_TIEU_TUYEN_SINH_UPDATE = "Cập nhật chỉ tiêu tuyển sinh";
        public const string ACTION_CHI_TIEU_TUYEN_SINH_DELETE = "Xóa chỉ tiêu tuyển sinh";
        #endregion

        #region Mức hưởng bhyt
        public const string ACTION_MUC_HUONG_BHYT_CREATE = "Thêm mới mức hưởng bhyt";
        public const string ACTION_MUC_HUONG_BHYT_UPDATE = "Cập nhật mức hưởng bhyt";
        public const string ACTION_MUC_HUONG_BHYT_DELETE = "Xóa mức hưởng bhyt";
        #endregion

        #region Vùng
        public const string ACTION_VUNG_CREATE = "Thêm mới vùng";
        public const string ACTION_VUNG_UPDATE = "Cập nhật vùng";
        public const string ACTION_VUNG_DELETE = "Xóa vùng";
        #endregion

        #region Bệnh viện
        public const string ACTION_BENH_VIEN_CREATE = "Thêm mới bệnh viện";
        public const string ACTION_BENH_VIEN_UPDATE = "Cập nhật bệnh viện";
        public const string ACTION_BENH_VIEN_DELETE = "Xóa bệnh viện";

        #endregion

        #region Phương án
        public const string ACTION_PHUONG_AN_CREATE = "Thêm mới phương án";
        public const string ACTION_PHUONG_AN_UPDATE = "Cập nhật phương án";
        public const string ACTION_PHUONG_AN_DELETE = "Xóa phương án";
        #endregion

        #region Phương thức đóng
        public const string ACTION_PHUONG_THUC_DONG_CREATE = "Thêm mới phương thức đóng";
        public const string ACTION_PHUONG_THUC_DONG_UPDATE = "Cập nhật phương thức đóng";
        public const string ACTION_PHUONG_THUC_DONG_DELETE = "Xóa phương thức đóng";
        #endregion

        #region Bậc đào tạo
        public const string ACTION_BAC_DAO_TAO_CREATE = "Thêm mới bậc đào tạo";
        public const string ACTION_BAC_DAO_TAO_UPDATE = "Cập nhật bậc đào tạo";
        public const string ACTION_BAC_DAO_TAO_DELETE = "Xóa bậc đào tạo";
        public const string ACTION_BAC_DAO_TAO_CREATE_MANY = "Import file excel bậc đào tạo";
        #endregion
    }
}