using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared
{
    public enum WorkFlowNameSciCodeEnum
    {
        work_flow_name_phe_duyet_bai_bao,
        work_flow_name_phe_duyet_hoc_lieu,
        work_flow_name_phe_duyet_de_tai
    }
    public class WorkFlowNameMapFunctionModel 
    {
        public string Function { get; set; }
        public string WorkFlowName { get; set; }
        public string MaPhanHe {  get; set; }
    }
    public static class WorkFlowSeedConstants
    {
        public static readonly Dictionary<string, WorkFlowNameMapFunctionModel> WorkflowNameMapFunction = new Dictionary<string, WorkFlowNameMapFunctionModel>
        {
            { WorkFlowNameSciCodeEnum.work_flow_name_phe_duyet_bai_bao.ToString(), new WorkFlowNameMapFunctionModel { Function = "Phê duyệt bài báo", WorkFlowName = "Quy trình phê duyệt bài báo", MaPhanHe = "UniSci" } },
            { WorkFlowNameSciCodeEnum.work_flow_name_phe_duyet_hoc_lieu.ToString(), new WorkFlowNameMapFunctionModel { Function = "Phê duyệt học liệu", WorkFlowName = "Quy trình phê duyệt học liệu", MaPhanHe = "UniSci" }},
            { WorkFlowNameSciCodeEnum.work_flow_name_phe_duyet_de_tai.ToString(), new WorkFlowNameMapFunctionModel { Function = "Phê duyệt đề tài", WorkFlowName = "Quy trình phê duyệt đề tài", MaPhanHe = "UniSci" } }
        };
    }
}
