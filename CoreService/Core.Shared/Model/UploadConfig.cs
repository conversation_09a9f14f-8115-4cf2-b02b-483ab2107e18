using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared
{
    public class UploadConfig
    {
        /// <summary>
        /// Th<PERSON>ụ<PERSON> lư<PERSON> file upload
        /// </summary>
        public string FolderUpload { get; set; } = "file-uploads";

        /// <summary>
        /// Các định dạng file được phép upload
        /// </summary>
        public string[] AllowedExtensions { get; set; } = [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"];

        /// <summary>
        /// <PERSON><PERSON><PERSON> thước file tối đa được phép upload Mb
        /// </summary>
        public float MaxFileSize { get; set; } = 10;
    }
}
