using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Core.Shared
{
    public class RequestUser
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = "";
        public string MaCB { get; set; } = "";
        public int? GiaoVienId { get; set; }
        public string Language { get; set; } = LanguageConstant.VI;
        public string TraceId { get; set; }
        public SystemLogModel SystemLog { get; set; } = new SystemLogModel();
    }
}
