namespace Core.Shared.Model;

public class CauHinhViTriKyModel
{
    public string Code { get; set; }
    public string TenViTri { get; set; }
    public int Page { get; set; }
    public bool IsShow { get; set; }
    public decimal LLX { get; set; }
    public decimal LLY { get; set; }
    public decimal Height { get; set; }
    public decimal Width { get; set; }
    public int BorderWidthOfPage { get; set; }

    //Tọa độ động => nếu tìm thấy thì sẽ lấy theo tọa độ này
    public bool IsDynamicPosition { get; set; }
    public string TextAnchor { get; set; }
    public int FromPage { get; set; }
    public int TextFindedPosition { get; set; } = 1;
    public decimal DynamicFromAnchorLLX { get; set; }
    public decimal DynamicFromAnchorLLY { get; set; }
}