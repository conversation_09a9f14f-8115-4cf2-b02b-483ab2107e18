using System;
using System.Collections.Generic;

namespace Core.Shared.Model
{
    public class PdfConvertFromWordBase64Model
    {
        public string FileName { get; set; } = Guid.NewGuid().ToString();
        public string FileBase64 { get; set; }
        public List<KeyValueModel> Metadatas { get; set; }
        public List<TableDataModel> TableDatas { get; set; }
    }

    public class FileBase64Response
    {
        public string FileBase64 { get; set; }
        public string FileName { get; set; }
    }

    public class DocxConvertFromWordBase64Model
    {
        public string FileName { get; set; } = Guid.NewGuid().ToString();
        public string FileBase64 { get; set; }
        public List<KeyValueModel> Metadatas { get; set; }
        public List<TableDataModel> TableDatas { get; set; }
    }
}