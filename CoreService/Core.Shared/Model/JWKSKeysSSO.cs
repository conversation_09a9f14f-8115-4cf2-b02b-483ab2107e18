using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Core.Shared
{
    public class JWKSKeysSSO
    {
        [JsonPropertyName("keys")]
        public List<JWKSKeyModel> Keys { get; set; }
    }

    public class JWKSKeyModel
    {
        [JsonPropertyName("kty")]
        public string Kty { get; set; }

        [JsonPropertyName("use")]
        public string Use { get; set; }

        [JsonPropertyName("kid")]
        public string Kid { get; set; }

        [JsonPropertyName("e")]
        public string E { get; set; }

        [JsonPropertyName("n")]
        public string N { get; set; }

        [JsonPropertyName("alg")]
        public string Alg { get; set; }
    }
}
