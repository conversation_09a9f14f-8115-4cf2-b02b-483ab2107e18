using System;
using System.Collections.Generic;

namespace Core.Shared
{
    public class SystemLogModel
    {
        public string CorrelationId { get; set; }
        public string TraceId { get; set; }
        public string DeviceId { get; set; }
        public string ActionCode { get; set; }
        public string ActionName { get; set; }
        public string ClientIP { get; set; }
        public string RequestMethod { get; set; }
        public string RequestPath { get; set; }
        public string UserAgent { get; set; }
        public string Os { get; set; }
        public string Browser { get; set; }
        public string ClientInfo { get; set; }
        public DateTime CreatedDate { get; set; }
        public Location Location { get; set; }
        public string UserId { get; set; }
        public string UserName { get; set; }
        public List<ActionDetail> ListAction { get; set; } = new List<ActionDetail>();
        public long TimeExecution { get; set; }
    }
}
