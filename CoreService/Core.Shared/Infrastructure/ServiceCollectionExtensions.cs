using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;

namespace Core.Shared.Infrastructure
{
    public static class ServiceCollectionExtensions
    {
        private static readonly Dictionary<string, Type> _namedServices = new Dictionary<string, Type>();

        public static void AddNamedTransient<TService, TImplementation>(this IServiceCollection services, string name)
            where TService : class
            where TImplementation : class, TService
        {
            _namedServices[name] = typeof(TImplementation);
            services.AddTransient(typeof(TImplementation));
        }

        public static TService GetNamedService<TService>(this IServiceProvider serviceProvider, string name)
            where TService : class
        {
            if (_namedServices.TryGetValue(name, out var implementationType))
            {
                return (TService)serviceProvider.GetService(implementationType);
            }

            throw new KeyNotFoundException($"Service with name '{name}' not found.");
        }
    }
}
