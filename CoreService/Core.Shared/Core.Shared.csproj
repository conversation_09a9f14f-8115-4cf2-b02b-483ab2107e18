<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="AutoMapper.Collection" Version="10.0.0" />
    <PackageReference Include="JetBrains.Annotations" Version="2023.3.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.10.0" />
    <PackageReference Include="MongoDB.Driver" Version="2.24.0" />
    <PackageReference Include="NEST" Version="7.17.5" />
    <PackageReference Include="prometheus-net" Version="8.2.1" />
    <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
    <PackageReference Include="prometheus-net.AspNetCore.HealthChecks" Version="8.2.1" />
    <PackageReference Include="RabbitMQ.Client" Version="6.8.1" />
    <PackageReference Include="Sentry.AspNetCore" Version="4.1.2" />
    <PackageReference Include="Sentry.Serilog" Version="4.1.2" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Formatting.Elasticsearch" Version="9.0.3" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="9.0.3" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Grafana.Loki" Version="8.3.0" />
    <PackageReference Include="Serilog.Sinks.MongoDB" Version="5.4.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
	  <PackageReference Include="Spire.Doc" Version="9.4.18" />
	  <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.10.0" />
    <PackageReference Include="WorkflowEngine.NETCore-Core" Version="17.2.0" />
    <PackageReference Include="WorkflowEngine.NETCore-ProviderForMSSQL" Version="17.2.0" />

    <!-- Explicit Microsoft.CodeAnalysis package versions to resolve conflicts -->
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.5.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.5.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.5.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Scripting.Common" Version="4.5.0" />
  </ItemGroup>
</Project>