using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared
{
    public class XCrypto
    {
        [DebuggerNonUserCode]
        public XCrypto()
        {
        }

        private static string HashData(HashAlgorithm algo, string data)
        {
            byte[] bytes = Encoding.ASCII.GetBytes(data);
            byte[] array = algo.ComputeHash(bytes);
            return Convert.ToBase64String(array, 0, array.Length);
        }

        public static string SHA(string txt)
        {
            SHA1CryptoServiceProvider algo = new SHA1CryptoServiceProvider();
            return HashData(algo, txt);
        }

        public static string MD5(string txt)
        {
            MD5CryptoServiceProvider algo = new MD5CryptoServiceProvider();
            return HashData(algo, txt);
        }

        public static string SHA256(string txt)
        {
            SHA256Managed algo = new SHA256Managed();
            return HashData(algo, txt);
        }

        public static string SHA384(string txt)
        {
            SHA384Managed algo = new SHA384Managed();
            return HashData(algo, txt);
        }

        public static string SHA512(string txt)
        {
            SHA512Managed algo = new SHA512Managed();
            return HashData(algo, txt);
        }
    }
}
