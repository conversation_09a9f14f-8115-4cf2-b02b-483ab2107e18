using Microsoft.Extensions.Configuration;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Core.Shared
{
    public static class AesEncryption
    {
        public const string KeyDefault = "S43F3seXGYhM76xUjMzqUSZn3rV48zupJ1cIEVT37zXeKwWHMd";
        
        // Dùng SHA256 để tạo key 32 byte từ string
        private static byte[] GetAesKey(string key)
        {
            using var sha256 = SHA256.Create();
            return sha256.ComputeHash(Encoding.UTF8.GetBytes(key));
        }

        public static string Encrypt(string plainText, string key)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            byte[] aesKey = GetAesKey(key);
            using var aes = Aes.Create();
            aes.Key = aesKey;
            aes.GenerateIV(); // Tạo IV ngẫu nhiên

            using var encryptor = aes.CreateEncryptor();
            using var ms = new MemoryStream();
            // Ghi IV vào đầu stream
            ms.Write(aes.IV, 0, aes.IV.Length);
            using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
            using (var writer = new StreamWriter(cs))
            {
                writer.Write(plainText);
            }

            return Convert.ToBase64String(ms.ToArray());
        }

        public static string Decrypt(string cipherTextBase64, string key)
        {
            if (string.IsNullOrEmpty(cipherTextBase64))
                return string.Empty;

            byte[] aesKey = GetAesKey(key);
            byte[] cipherBytes = Convert.FromBase64String(cipherTextBase64);

            using var aes = Aes.Create();
            aes.Key = aesKey;

            // Lấy IV từ đầu chuỗi mã hóa
            byte[] iv = new byte[aes.BlockSize / 8];
            Array.Copy(cipherBytes, iv, iv.Length);
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            using var ms = new MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length);
            using var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
            using var reader = new StreamReader(cs);

            return reader.ReadToEnd();
        }

        public static string Encrypt(IConfiguration config, string plainText)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));
            var key = config["AppSettings:EncryptionKey"];
            if (string.IsNullOrEmpty(key))
            {
                key = "S43F3seXGYhM76xUjMzqUSZn3rV48zupJ1cIEVT37zXeKwWHMd"; // Mặc định nếu không có trong config
            }
            return Encrypt(plainText, key);
        }
        public static string Decrypt(IConfiguration config, string cipherTextBase64)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));
            var key = config["AppSettings:EncryptionKey"];
            if (string.IsNullOrEmpty(key))
            {
                key = "S43F3seXGYhM76xUjMzqUSZn3rV48zupJ1cIEVT37zXeKwWHMd"; // Mặc định nếu không có trong config
            }
            return Decrypt(cipherTextBase64, key);
        }
    }
}
