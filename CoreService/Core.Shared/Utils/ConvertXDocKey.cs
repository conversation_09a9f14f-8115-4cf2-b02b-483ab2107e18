using Nest;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared
{
    public static class ConvertXDocKey
    {
        public static List<KeyValueModel> ToMetadata<T>(T model)
        {
            if (model == null)
                return new List<KeyValueModel>();

            var properties = typeof(T).GetProperties();
            var metadata = new List<KeyValueModel>();

            foreach (var prop in properties)
            {
                var value = prop.GetValue(model);
                string stringValue;

                if (value is DateTime or DateTimeOffset)
                    stringValue = ((IFormattable)value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
                else
                    stringValue = value?.ToString() ?? string.Empty;

                metadata.Add(new KeyValueModel
                {
                    Key = "{" + char.ToLowerInvariant(prop.Name[0]) + prop.Name.Substring(1) + "}",
                    Value = stringValue
                });
            }

            return metadata;
        }

        // Hàm tạo TableDataModel từ danh sách object
        public static TableDataModel CreateTableDataModel<T>(string tableName, List<T> dataList)
        {
            return new TableDataModel
            {
                TableName = tableName,
                TableData = ConvertObjectListToDictionaryList(dataList)
            };
        }

        // Hàm dùng reflection để chuyển object thành Dictionary<string, string>
        public static List<Dictionary<string, string>> ConvertObjectListToDictionaryList<T>(List<T> dataList)
        {
            var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var result = new List<Dictionary<string, string>>();

            foreach (var item in dataList)
            {
                var dict = new Dictionary<string, string>();

                foreach (var prop in properties)
                {
                    var valueObj = prop.GetValue(item);
                    string valueStr = "";

                    if (valueObj == null)
                    {
                        valueStr = "";
                    }
                    else if (valueObj is DateTime dt)
                    {
                        valueStr = dt.ToString("dd/MM/yyyy");
                    }
                    else if (valueObj is bool b)
                    {
                        valueStr = b ? "Có" : "Không";
                    }
                    else
                    {
                        valueStr = valueObj.ToString();
                    }

                    dict.Add("{" + char.ToLowerInvariant(prop.Name[0]) + prop.Name.Substring(1) + "}", valueStr);
                }

                result.Add(dict);
            }

            return result;
        }
    }
}
