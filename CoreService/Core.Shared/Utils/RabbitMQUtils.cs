using AutoMapper;
using AutoMapper.EquivalencyExpression;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text;
using System.Collections;
using System.Threading.Channels;

namespace Core.Shared
{
    public static class RabbitMQUtils
    {
        public static void PublishMessage(string uri, Type type, object obj, string traceId = "", int userId = 0)
        {
            // <PERSON>hai báo thông tin theo EventName Attribute của Class
            string attName = Utils.GetEventNameAttribute(type);
            string exchange = attName;
            string queue = attName;
            string routingKey = attName;

            var factory = new ConnectionFactory
            {
                Uri = new Uri(uri)
            };

            // Khởi tạo connection và channel
            using (var connection = factory.CreateConnection())
            using (var channel = connection.CreateModel())
            {
                channel.InitConfiguration(type);

                // Tạo thuộc tính và thêm vào header
                var properties = channel.CreateBasicProperties();
                properties.Headers = new Dictionary<string, object>
                    {
                        { RabbitMQHeaderConstant.TraceId, traceId },
                        { RabbitMQHeaderConstant.UserId, userId.ToString() }
                    };

                // Publish message
                var body = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(obj));
                channel.BasicPublish(exchange, routingKey, properties, body);
            }
        }

        /// <summary>
        /// Khởi tạo exchange, queue cho channel với Type của exchange là Exchange.Direct
        /// </summary>
        /// <param name="channel"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static IModel InitConfiguration(this IModel channel, Type type)
        {
            // Khai báo thông tin theo EventName Attribute của Class
            string attName = Utils.GetEventNameAttribute(type);
            string exchange = attName;
            string queue = attName;
            string routingKey = attName;

            // Khai báo exchange và queue
            channel.ExchangeDeclare(exchange, ExchangeType.Direct, true, false);
            channel.QueueDeclare(queue, true, false, false);

            // Ràng buộc queue với exchange
            channel.QueueBind(queue, exchange, routingKey);
            return channel;
        }
    }
}
