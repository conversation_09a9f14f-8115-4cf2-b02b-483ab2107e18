using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.IdentityModel.Tokens;
using System.Drawing;
using System.Net.Sockets;
using System.Net.NetworkInformation;
using Nest;
using System.Collections.Generic;
using System.Text.Json;
using System.Data;
using System.Reflection;
using Core.Shared;
using Spire.Doc;
using Core.Shared.Model;
using Spire.Doc.Documents;
using Spire.Doc.Fields;
using System.Xml.Linq;

namespace Core.Shared
{
    public static class AsyncHelper
    {
        private static readonly TaskFactory _taskFactory = new
            TaskFactory(CancellationToken.None,
                        TaskCreationOptions.None,
                        TaskContinuationOptions.None,
                        TaskScheduler.Default);

        public static TResult RunSync<TResult>(Func<Task<TResult>> func)
            => _taskFactory
                .StartNew(func)
                .Unwrap()
                .GetAwaiter()
                .GetResult();

        public static void RunSync(Func<Task> func)
            => _taskFactory
                .StartNew(func)
                .Unwrap()
                .GetAwaiter()
                .GetResult();
    }

    public class ObjectScore
    {
        public object Value { get; set; }
        public int Score { get; set; }
    }

    public partial class Utils
    {
        //public static string GetConfig(string code)
        //{

        //    IConfigurationRoot configuration = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory())
        //                                                                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: false)
        //                                                                 .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json",
        //                                                                 optional: true, reloadOnChange: false)
        //                                                                 //.AddJsonFile($"appsettings.{AppConstants.EnvironmentName}.json",
        //                                                                 //optional: true, reloadOnChange: false)
        //                                                                 .Build();
        //    var value = configuration[code];
        //    return value;
        //}
        //public static T GetConfigObject<T>(string code)
        //{

        //    IConfigurationRoot configuration = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory())
        //                                                                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: false)
        //                                                                 .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json",
        //                                                                 optional: true, reloadOnChange: false)
        //                                                                 //.AddJsonFile($"appsettings.{AppConstants.EnvironmentName}.json",
        //                                                                 //optional: true, reloadOnChange: false)
        //                                                                 .Build();
        //    var value = configuration.GetSection(code).Get<T>();
        //    return value;
        //}

        #region Date function
        public static DateTime FirstDayOfMonth(DateTime date)
        {
            return new DateTime(date.Year, date.Month, 1);
        }
        public static DateTime LastDayOfMonth(DateTime date)
        {
            return (new DateTime(date.Year, date.Month, 1)).AddMonths(1).AddDays(-1);
        }
        public static DateTime FirstDayOfYear(DateTime date)
        {
            return new DateTime(date.Year, 1, 1);
        }
        public static DateTime LastDayOfYear(DateTime date)
        {
            return (new DateTime(date.Year, 1, 1)).AddYears(1).AddDays(-1);
        }
        #endregion

        public static string GenerateUniqueApiKey()
        {
            // Tạo API Key theo format: ak_[timestamp]_[random]
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var randomBytes = new byte[16];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomBytes);
            }
            var randomString = Convert.ToBase64String(randomBytes)
                .Replace("+", "")
                .Replace("/", "")
                .Replace("=", "")
                .Substring(0, 16);

            return $"ak_{timestamp}_{randomString}";
        }
        
        public static string GetMd5Hash(string input)
        {
            using (MD5 md5Hash = MD5.Create())
            {
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(input));

                StringBuilder sBuilder = new StringBuilder();

                for (int i = 0; i < data.Length; i++)
                {
                    sBuilder.Append(data[i].ToString("x2"));
                }

                return sBuilder.ToString();
            }
        }

        public static DateTime UnixTimeStampToDateTime(double unixTimeStamp)
        {
            // Unix timestamp is seconds past epoch
            DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            dateTime = dateTime.AddSeconds(unixTimeStamp).ToLocalTime();
            return dateTime;
        }

        public static bool IsValidBase64String(string base64String)
        {
            base64String = base64String.Replace("-----BEGIN CERTIFICATE REQUEST-----", "").Replace("-----END CERTIFICATE REQUEST-----", "");
            if (string.IsNullOrEmpty(base64String) || base64String.Length % 4 != 0 || base64String.Contains(" ") || base64String.Contains("\t") || base64String.Contains("\r") || base64String.Contains("\n"))
                return false;
            try
            {
                Convert.FromBase64String(base64String);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static bool IsValidEmail(string email)
        {
            // Kiểm tra nếu email là null hoặc rỗng
            if (string.IsNullOrEmpty(email))
                return false;

            // Sử dụng Regular Expression để kiểm tra định dạng email
            var emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
            return Regex.IsMatch(email, emailPattern);
        }

        // This presumes that weeks start with Monday.
        // Week 1 is the 1st week of the year with a Thursday in it.
        public static int GetIso8601WeekOfYear(DateTime time)
        {
            // Seriously cheat.  If its Monday, Tuesday or Wednesday, then it'll 
            // be the same week# as whatever Thursday, Friday or Saturday are,
            // and we always get those right
            DayOfWeek day = CultureInfo.InvariantCulture.Calendar.GetDayOfWeek(time);
            if (day >= DayOfWeek.Monday && day <= DayOfWeek.Wednesday)
            {
                time = time.AddDays(3);
            }

            // Return the week of our adjusted day
            return CultureInfo.InvariantCulture.Calendar.GetWeekOfYear(time, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
        }

        private static readonly string[] VietnameseSigns = new string[]
        {
        "aAeEoOuUiIdDyY",
        "áàạảãâấầậẩẫăắằặẳẵ",
        "ÁÀẠẢÃÂẤẦẬẨẪĂẮẰẶẲẴ",
        "éèẹẻẽêếềệểễ",
        "ÉÈẸẺẼÊẾỀỆỂỄ",
        "óòọỏõôốồộổỗơớờợởỡ",
        "ÓÒỌỎÕÔỐỒỘỔỖƠỚỜỢỞỠ",
        "úùụủũưứừựửữ",
        "ÚÙỤỦŨƯỨỪỰỬỮ",
        "íìịỉĩ",
        "ÍÌỊỈĨ",
        "đ",
        "Đ",
        "ýỳỵỷỹ",
        "ÝỲỴỶỸ"
        };

        public static string RemoveVietnameseSign(string str)
        {
            for (int i = 1; i < VietnameseSigns.Length; i++)
            {
                for (int j = 0; j < VietnameseSigns[i].Length; j++)

                    str = str.Replace(VietnameseSigns[i][j], VietnameseSigns[0][i - 1]);

            }

            return str;
        }

        public static string BuildVietnameseSign(string str)
        {
            for (int i = 1; i < VietnameseSigns.Length; i++)
            {
                for (int j = 0; j < VietnameseSigns[i].Length; j++)
                    str = str.Replace(VietnameseSigns[0][i - 1], VietnameseSigns[i][j]);
            }
            return str;
        }

        public static string GetValidFileName(string fileName)
        {
            // remove any invalid character from the filename.
            String ret = Regex.Replace(fileName.Trim(), "[^A-Za-z0-9_. ]+", "");
            return ret.Replace(" ", String.Empty);
        }

        /// <summary>
        /// Convert url title
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static string ConvertToUrlTitle(string name)
        {
            string strNewName = name;

            #region Replace unicode chars
            Regex regex = new Regex("\\p{IsCombiningDiacriticalMarks}+");
            string temp = name.Normalize(NormalizationForm.FormD);
            strNewName = regex.Replace(temp, String.Empty).Replace('\u0111', 'd').Replace('\u0110', 'D');
            #endregion

            #region Replace special chars
            string strSpecialString = "~\"“”#%&*:;<>?/\\{|}.+_@$^()[]`,!-'";

            foreach (char c in strSpecialString)
            {
                strNewName = strNewName.Replace(c, ' ');
            }
            #endregion

            #region Replace space

            // Create the Regex.
            var r = new Regex(@"\s+");
            // Strip multiple spaces.
            strNewName = r.Replace(strNewName, @" ").Replace(" ", "-").Trim('-');

            #endregion)

            return strNewName;
        }

        /// <summary>
        /// Check if a string is a guid or not
        /// </summary>
        /// <param name="inputString"></param>
        /// <returns></returns>
        public static bool IsGuid(string inputString)
        {
            try
            {
                var guid = new Guid(inputString);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static bool IsNumber(string inputString)
        {
            try
            {
                var number = int.Parse(inputString);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Tạo chuỗi 6 chữ số
        /// </summary>
        /// <returns></returns>
        public static string GenerateNewRandom()
        {
            Random generator = new Random();
            String r = generator.Next(0, 1000000).ToString("D6");
            if (r.Distinct().Count() == 1)
            {
                r = GenerateNewRandom();
            }
            return r;
        }

        /// <summary>
        /// Tạo chuỗi 4 chữ số
        /// </summary>
        /// <returns></returns>
        public static string GenerateNewRandom4Number()
        {
            Random generator = new Random();
            String r = generator.Next(0, 10000).ToString("D");
            if (r.Distinct().Count() == 1)
            {
                r = GenerateNewRandom();
            }
            return r;
        }

        public static string PassowrdRandomString(int size, bool lowerCase)
        {
            var builder = new StringBuilder();
            var random = new Random();
            for (int i = 0; i < size; i++)
            {
                char ch = Convert.ToChar(Convert.ToInt32(Math.Floor(26 * random.NextDouble() + 65)));
                builder.Append(ch);
            }
            return lowerCase ? builder.ToString().ToLower() : builder.ToString();
        }

        public static string PassowrdCreateSalt512()
        {
            var message = PassowrdRandomString(512, false);
            return BitConverter.ToString((new SHA512Managed()).ComputeHash(Encoding.ASCII.GetBytes(message))).Replace("-", "");
        }

        public static string RandomPassword(int numericLength, int lCaseLength, int uCaseLength, int specialLength)
        {
            Random random = new Random();

            //char set random
            string PASSWORD_CHARS_LCASE = "abcdefgijkmnopqrstwxyz";
            string PASSWORD_CHARS_UCASE = "ABCDEFGHJKLMNPQRSTWXYZ";
            string PASSWORD_CHARS_NUMERIC = "1234567890";
            string PASSWORD_CHARS_SPECIAL = "!@#$%^&*()-+<>?";
            if ((numericLength + lCaseLength + uCaseLength + specialLength) < 8)
                return string.Empty;
            else
            {
                //get char
                var strNumeric = new string(Enumerable.Repeat(PASSWORD_CHARS_NUMERIC, numericLength)
                    .Select(s => s[random.Next(s.Length)]).ToArray());

                var strUper = new string(Enumerable.Repeat(PASSWORD_CHARS_UCASE, uCaseLength)
                    .Select(s => s[random.Next(s.Length)]).ToArray());

                var strSpecial = new string(Enumerable.Repeat(PASSWORD_CHARS_SPECIAL, specialLength)
                    .Select(s => s[random.Next(s.Length)]).ToArray());

                var strLower = new string(Enumerable.Repeat(PASSWORD_CHARS_LCASE, lCaseLength)
                    .Select(s => s[random.Next(s.Length)]).ToArray());

                //result : ký tự số + chữ hoa + chữ thường + các ký tự đặc biệt > 8
                var strResult = strNumeric + strUper + strSpecial + strLower;
                return strResult;
            }
        }

        public static string PasswordGenerateHmac(string clearMessage, string secretKeyString)
        {
            var encoder = new ASCIIEncoding();
            var messageBytes = encoder.GetBytes(clearMessage);
            var secretKeyBytes = new byte[secretKeyString.Length / 2];
            for (int index = 0; index < secretKeyBytes.Length; index++)
            {
                string byteValue = secretKeyString.Substring(index * 2, 2);
                secretKeyBytes[index] = byte.Parse(byteValue, NumberStyles.HexNumber, CultureInfo.InvariantCulture);
            }
            var hmacsha512 = new HMACSHA512(secretKeyBytes);
            byte[] hashValue = hmacsha512.ComputeHash(messageBytes);
            string hmac = "";
            foreach (byte x in hashValue)
            {
                hmac += String.Format("{0:x2}", x);
            }
            return hmac.ToUpper();
        }

        public static Expression<Func<T, bool>> PredicateByName<T>(string propName, object propValue)
        {
            var parameterExpression = Expression.Parameter(typeof(T));
            var propertyOrField = Expression.PropertyOrField(parameterExpression, propName);
            var binaryExpression = Expression.GreaterThan(propertyOrField, Expression.Constant(propValue));
            return Expression.Lambda<Func<T, bool>>(binaryExpression, parameterExpression);
        }

        public static string GenerateAutoCode(string prefix, long number)
        {
            return prefix + (number > 9999 ? number.ToString() : (10000 + number).ToString().Remove(0, 1)); ;
        }

        /// <summary>
        /// Chuyển đổi mã HEX sang RGB
        /// </summary>
        /// <param name="hexString"></param>
        /// <returns></returns>
        public static Color HexToColor(String hexString)
        // Translates a html hexadecimal definition of a color into a .NET Framework Color.
        // The input string must start with a '#' character and be followed by 6 hexadecimal
        // digits. The digits A-F are not case sensitive. If the conversion was not successfull
        // the color white will be returned.
        {
            Color actColor;
            int r, g, b;
            r = 0;
            g = 0;
            b = 0;
            if ((hexString.StartsWith("#")) && (hexString.Length == 7))
            {
                r = int.Parse(hexString.Substring(1, 2), NumberStyles.AllowHexSpecifier);
                g = int.Parse(hexString.Substring(3, 2), NumberStyles.AllowHexSpecifier);
                b = int.Parse(hexString.Substring(5, 2), NumberStyles.AllowHexSpecifier);
                actColor = Color.FromArgb(r, g, b);
            }
            else
            {
                actColor = Color.Black;
            }
            return actColor;
        }

        //Encode
        public static string Base64Encode(string plainText)
        {
            try
            {
                var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
                return System.Convert.ToBase64String(plainTextBytes);
            }
            catch (Exception)
            {
                return string.Empty;
            }

        }
        //Decode
        public static string Base64Decode(string base64EncodedData)
        {
            try
            {
                var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
                return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
        public static byte[] Base64DecodeToByteArray(string base64EncodedData)
        {
            try
            {
                var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
                return base64EncodedBytes;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public static string BuildStringFromTemplate(string source, params object[] parameters)
        {
            return String.Format(source, parameters);
        }

        public static string ByteArrayToHexString(byte[] ba)
        {
            StringBuilder hex = new StringBuilder(ba.Length * 2);
            foreach (byte b in ba)
                hex.AppendFormat("{0:x2}", b);
            return hex.ToString();
        }

        public static bool PingPort(string url, int port)
        {
            TcpClient tcpClient = new TcpClient();
            try
            {
                tcpClient.Connect(url, port);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static bool PingHost(string nameOrAddress)
        {
            bool pingable = false;
            Ping pinger = null;

            try
            {
                pinger = new Ping();
                PingReply reply = pinger.Send(nameOrAddress);
                pingable = reply.Status == IPStatus.Success;
            }
            catch (PingException)
            {
                // Discard PingExceptions and return false;
            }
            finally
            {
                if (pinger != null)
                {
                    pinger.Dispose();
                }
            }

            return pingable;
        }

        public static string GetEventNameAttribute(Type type)
        {
            var attribute = (EventNameAttribute)Attribute.GetCustomAttribute(type, typeof(EventNameAttribute));
            return attribute != null ? attribute?.Name : nameof(Type);
        }

        public static bool IsJson(string input)
        {
            input = input.Trim();
            if ((input.StartsWith("{") && input.EndsWith("}")) || // Đối tượng JSON
                (input.StartsWith("[") && input.EndsWith("]")))   // Mảng JSON
            {
                try
                {
                    using (JsonDocument doc = JsonDocument.Parse(input))
                    {
                        return true;
                    }
                }
                catch (JsonException)
                {
                    return false;
                }
            }
            return false;
        }

        // Hàm đệ quy để chuyển đổi các key
        public static void FlattenJson(string prefix, JsonElement element, Dictionary<string, string> dictionary)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Object:
                    foreach (JsonProperty property in element.EnumerateObject())
                    {
                        var key = string.IsNullOrEmpty(prefix) ? property.Name : $"{prefix}:{property.Name}";
                        FlattenJson(key, property.Value, dictionary);
                    }
                    break;
                case JsonValueKind.Array:
                    int index = 0;
                    foreach (JsonElement arrayElement in element.EnumerateArray())
                    {
                        var key = $"{prefix}:{index}";
                        FlattenJson(key, arrayElement, dictionary);
                        index++;
                    }
                    break;
                default:
                    dictionary[prefix] = element.ToString();
                    break;
            }
        }

        public static bool IsValidPassword(string password)
        {
            // Kiểm tra độ dài mật khẩu
            if (password.Length < 8) return false;

            // Kiểm tra các tiêu chí khác
            bool hasUpperCase = password.Any(char.IsUpper);
            bool hasLowerCase = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecialChar = password.Any(ch => !char.IsLetterOrDigit(ch));

            return hasUpperCase && hasLowerCase && hasDigit && hasSpecialChar;
        }

        // Chuyển đổi DataTable thành List<Dictionary<string, string>>
        public static List<Dictionary<string, string>> ConvertDataTableToListOfDictionaries(DataTable table, List<string> keys)
        {
            var listOfDictionaries = new List<Dictionary<string, string>>();

            foreach (DataRow row in table.Rows)
            {
                var dict = new Dictionary<string, string>();

                foreach (DataColumn column in table.Columns)
                {
                    if (keys.Contains(column.ColumnName))
                    {
                        dict[column.ColumnName] = row[column].ToString();
                    }
                }

                listOfDictionaries.Add(dict);
            }

            return listOfDictionaries;
        }

        public static DataTable ConvertToDataTable<T>(IList<T> data)
        {
            DataTable dataTable = new DataTable(typeof(T).Name);
            PropertyInfo[] properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            // Tạo các cột trong DataTable dựa trên các thuộc tính
            foreach (PropertyInfo property in properties)
            {
                // Kiểm tra nếu thuộc tính có attribute DataColumn
                var attribute = property.GetCustomAttribute<DataColumnAttribute>();
                string columnName = attribute?.ColumnName ?? property.Name;

                // Thêm cột vào DataTable với tên từ attribute hoặc tên thuộc tính
                dataTable.Columns.Add(columnName, Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType);
            }

            // Thêm dữ liệu vào DataTable
            foreach (T item in data)
            {
                var values = new object[properties.Length];
                for (int i = 0; i < properties.Length; i++)
                {
                    values[i] = properties[i].GetValue(item, null);
                }
                dataTable.Rows.Add(values);
            }

            return dataTable;
        }

        public static string GetHoDem(string hoTen)
        {
            if (string.IsNullOrEmpty(hoTen))
                return string.Empty;

            // Chuẩn hóa chuỗi
            var ten = hoTen.Trim();
            while (ten.Contains("  "))
            {
                ten = ten.Replace("  ", " ");
            }

            // Tìm phần cuối cùng (tên)
            var parts = ten.Split(' ');
            if (parts.Length <= 1)
                return string.Empty;

            // Ghép lại phần Họ + Đệm
            return string.Join(" ", parts.Take(parts.Length - 1));
        }

        public static string GetTen(string hoTen)
        {
            if (string.IsNullOrEmpty(hoTen))
                return string.Empty;

            var ten = hoTen.Trim();
            while (ten.Contains("  "))
            {
                ten = ten.Replace("  ", " ");
            }

            // Tách phần cuối cùng là tên
            var parts = ten.Split(' ');
            return parts.LastOrDefault() ?? string.Empty;
        }

        public static string RemoveDiacritics(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            // Normalize the text to decompose the diacritics
            string normalizedString = text.Normalize(NormalizationForm.FormD);
            StringBuilder stringBuilder = new StringBuilder();

            foreach (var c in normalizedString)
            {
                UnicodeCategory unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                // Only include characters that are not diacritics
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            // Normalize back to FormC
            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }

        public static string Base64DecodeTA(string base64EncodedData)
        {
            try
            {
                var base64EncodedBytes = System.Text.Encoding.UTF8.GetBytes(base64EncodedData);
                return System.Convert.ToBase64String(base64EncodedBytes);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        public static string EncryptTA(string toEncrypt)
        {
            bool useHashing = true;
            byte[] keyArray;
            byte[] toEncryptArray = UTF8Encoding.UTF8.GetBytes(toEncrypt);
            string key = "43d900f17088444d1bf8f3abf0424804";
            if (useHashing)
            {
                MD5CryptoServiceProvider hashmd5 = new MD5CryptoServiceProvider();
                keyArray = hashmd5.ComputeHash(UTF8Encoding.UTF8.GetBytes(key));
            }
            else
                keyArray = UTF8Encoding.UTF8.GetBytes(key);

            TripleDESCryptoServiceProvider tdes = new TripleDESCryptoServiceProvider();
            tdes.Key = keyArray;
            tdes.Mode = CipherMode.ECB;
            tdes.Padding = PaddingMode.PKCS7;

            ICryptoTransform cTransform = tdes.CreateEncryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

            return Convert.ToBase64String(resultArray, 0, resultArray.Length);
        }
    }

    public class TokenRequest
    {
        public string Token { get; set; }
        public string Password { get; set; }
    }

    public class TokenInfo
    {
        public Guid ObjectId { get; set; }
        public int Level { get; set; }
        public long Tick { get; set; }
        public DateTime DateTimeExpired { get; set; }
    }

    public static class Encrypt
    {
        #region Encrypt Function

        public static string ComputeSHA512Hash(string input)
        {
            using (SHA512 sha512 = SHA512.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = sha512.ComputeHash(inputBytes);

                // Chuyển đổi byte array thành chuỗi hex
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hashBytes)
                {
                    sb.Append(b.ToString("x2")); // Chuyển đổi thành chuỗi hex
                }
                return sb.ToString();
            }
        }

        public static string ComputeSHA256Hash(string input)
        {
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = sha256.ComputeHash(inputBytes);

                // Chuyển đổi byte array thành chuỗi hex
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hashBytes)
                {
                    sb.Append(b.ToString("x2")); // Chuyển đổi thành chuỗi hex
                }
                return sb.ToString();
            }
        }
        #endregion
    }

    public static class Security
    {
        #region Check sum
        public static string GetHashFromFile(string fileName, HashAlgorithm algorithm)
        {
            using (var stream = new BufferedStream(File.OpenRead(fileName), 100000))
            {
                return BitConverter.ToString(algorithm.ComputeHash(stream)).Replace("-", string.Empty);
            }
        }
        public static bool VerifyHashFromFile(string fileName, HashAlgorithm algorithm, string hashInput)
        {
            bool verify = false;
            string hashResult = "";

            using (var stream = new BufferedStream(File.OpenRead(fileName), 100000))
            {
                hashResult = BitConverter.ToString(algorithm.ComputeHash(stream)).Replace("-", string.Empty);
                if (hashResult.SequenceEqual(hashInput)) verify = true;
            }

            return verify;
        }
        #endregion
    }

    public static class TokenHelpers
    {
        #region basic token

        /// <summary>
        /// Tạo token theo key
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string CreateBasicToken(string key)
        {
            try
            {
                string token = string.Empty;

                byte[] keyData = Encoding.UTF8.GetBytes(key);

                // Token chứa mã đối tượng tải về
                if (keyData != null) token = Convert.ToBase64String(keyData.ToArray());
                //Safe URl
                token = Base64UrlEncoder.Encode(token);
                return token;
            }
            catch (Exception)
            {
                throw;
            }

        }

        /// <summary>
        /// Lấy key theo token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public static string GetKeyFromBasicToken(string token)
        {
            try
            {
                //Safe URl
                token = Base64UrlEncoder.Decode(token);
                string key = string.Empty;

                if (IsBase64(token))
                {
                    byte[] dataToken = Convert.FromBase64String(token);

                    if (dataToken != null) key = Encoding.UTF8.GetString(dataToken);
                }
                return key;
            }
            catch (Exception)
            {

                throw;
            }
        }
        #endregion

        #region token download

        #endregion

        public static bool IsBase64(this string base64String)
        {
            if (base64String == null || base64String.Length == 0 || base64String.Length % 4 != 0
               || base64String.Contains(" ") || base64String.Contains("\t") || base64String.Contains("\r") || base64String.Contains("\n"))
                return false;

            try
            {
                Convert.FromBase64String(base64String);
                return true;
            }
            catch (Exception)
            {
                // Handle the exception
            }
            return false;
        }

    }

    public static class Base64Convert
    {
        public static string ConvertStreamToBase64(Stream stream)
        {
            byte[] bytes;
            using (var memoryStream = new MemoryStream())
            {
                stream.CopyTo(memoryStream);
                bytes = memoryStream.ToArray();
            }

            string base64 = Convert.ToBase64String(bytes);
            return base64;
        }

        public static string ConvertMemoryStreamToBase64(MemoryStream memoryStream)
        {
            byte[] bytes = memoryStream.ToArray();

            string base64 = Convert.ToBase64String(bytes);
            return base64;
        }

        public static MemoryStream ConvertBase64ToMemoryStream(string base64Content)
        {
            byte[] bytes = Convert.FromBase64String(base64Content);

            MemoryStream ms = new MemoryStream(bytes);
            return ms;
        }
    }

    public static class Sha256Convert
    {
        public static string ConvertMemoryStreamToSHA256(MemoryStream memoryStream)
        {
            StringBuilder hash = new StringBuilder();
            using (SHA256 sHA256 = SHA256.Create())
            {
                memoryStream.Position = 0;
                byte[] hashValue = sHA256.ComputeHash(memoryStream);
                foreach (byte elemt in hashValue)
                {
                    hash.Append(elemt.ToString("x2"));
                }
                return hash.ToString();
            }
        }
    }

    public static class ElasticClientUtils
    {
        public static ElasticClient GetElasticClient(ElasticConfig config)
        {
            //var elasticUri = Utils.GetConfig("ElasticConfiguration:Uri");
            //var elasticUser = Utils.GetConfig("ElasticConfiguration:BasicUserName");
            //var elasticPassword = Utils.GetConfig("ElasticConfiguration:BasicPassword");
            //var elasticCertificateFingerprint = Utils.GetConfig("ElasticConfiguration:CertificateFingerprint");
            //var elasticDefaultIndex = Utils.GetConfig("ElasticConfiguration:DefaultIndex");

            //var uris = new[]
            //    {
            //        new Uri("http://localhost:9200"),
            //        new Uri("http://localhost:9201"),
            //        new Uri("http://localhost:9202"),
            //    };

            //var connectionPool = new SniffingConnectionPool(uris);

            var settings = new ConnectionSettings(new Uri(config.Uri))
                    .BasicAuthentication(config.BasicUserName, config.BasicPassword)
                    .CertificateFingerprint(config.CertificateFingerprint)
                    .PrettyJson()
                    .DefaultIndex(config.DefaultIndex)
                    .RequestTimeout(TimeSpan.FromSeconds(300));

            var client = new ElasticClient(settings);
            return client;
        }
    }

    public class CongThucDiem
    {
        // Hàm tính toán công thức
        public static string TinhCongThuc(string CongThucTinh, Dictionary<string, object> dictionary)
        {
            try
            {
                // Thay thế các số thập phân dạng a.b thành ab/10
                CongThucTinh = ConvertDecimalToFraction(CongThucTinh);

                // Loại bỏ dấu ngoặc và tách biểu thức
                var tokens = TokenizeExpression(CongThucTinh);

                // Tính toán giá trị của biểu thức theo thứ tự ưu tiên
                double result = EvaluateExpression(tokens, dictionary);

                // Làm tròn kết quả đến 2 chữ số thập phân
                return Math.Round(result, 2).ToString();
            }
            catch (Exception ex)
            {
                // Log lỗi nếu có và trả về "-1"
                Console.WriteLine($"Error in calculating formula: {ex.Message}");
                return "-1";
            }
        }

        // Hàm thay thế các số thập phân với phân số tương đương
        private static string ConvertDecimalToFraction(string expression)
        {
            // Sử dụng biểu thức chính quy để tìm và thay thế các số thập phân
            var regex = new System.Text.RegularExpressions.Regex(@"\d+\.\d+");

            return regex.Replace(expression, match =>
            {
                string decimalValue = match.Value;
                string[] parts = decimalValue.Split('.');
                return $"{parts[0]}{parts[1]}/10"; // chuyển 0.4 thành 4/10
            });
        }

        // Hàm tách biểu thức thành các phần tử (token)
        private static List<string> TokenizeExpression(string expression)
        {
            List<string> tokens = new List<string>();
            int start = 0;

            for (int i = 0; i < expression.Length; i++)
            {
                char c = expression[i];

                // Nếu gặp toán tử hoặc dấu ngoặc, tách phần trước toán tử ra
                if ("+-*/()".Contains(c))
                {
                    if (i > start)
                        tokens.Add(expression.Substring(start, i - start).Trim());

                    tokens.Add(c.ToString());
                    start = i + 1;
                }
            }

            // Thêm phần còn lại của biểu thức
            if (start < expression.Length)
                tokens.Add(expression.Substring(start).Trim());

            return tokens;
        }

        // Hàm tính toán giá trị biểu thức
        private static double EvaluateExpression(List<string> tokens, Dictionary<string, object> dictionary)
        {
            Stack<double> values = new Stack<double>();
            Stack<string> operators = new Stack<string>();

            for (int i = 0; i < tokens.Count; i++)
            {
                string token = tokens[i].Trim();

                // Nếu là số hoặc tên biến, thêm vào stack giá trị
                if (double.TryParse(token, out double value))
                {
                    values.Push(value);
                }
                else if (dictionary.ContainsKey(token)) // Nếu là biến, lấy giá trị từ dictionary
                {
                    // Kiểm tra và chuyển đổi kiểu dữ liệu, nếu là string thì chuyển sang double
                    double finalValue = Convert.ToDouble(dictionary[token]);
                    values.Push(finalValue);
                }
                else if (token == "(")
                {
                    operators.Push(token);  // Thêm dấu ngoặc mở vào stack
                }
                else if (token == ")")
                {
                    // Tính toán cho đến khi gặp dấu ngoặc mở
                    while (operators.Peek() != "(")
                    {
                        values.Push(ApplyOperator(operators.Pop(), values.Pop(), values.Pop()));
                    }
                    operators.Pop();  // Loại bỏ dấu ngoặc mở
                }
                else if (IsOperator(token))
                {
                    // Nếu gặp toán tử, kiểm tra độ ưu tiên và tính toán
                    while (operators.Count > 0 && Precedence(operators.Peek()) >= Precedence(token))
                    {
                        values.Push(ApplyOperator(operators.Pop(), values.Pop(), values.Pop()));
                    }
                    operators.Push(token);
                }
            }

            // Tính toán cho đến khi hết các toán tử
            while (operators.Count > 0)
            {
                values.Push(ApplyOperator(operators.Pop(), values.Pop(), values.Pop()));
            }

            return values.Pop();
        }

        // Kiểm tra xem chuỗi có phải là toán tử không
        private static bool IsOperator(string token)
        {
            return token == "+" || token == "-" || token == "*" || token == "/";
        }

        // Xác định độ ưu tiên của toán tử
        private static int Precedence(string operatorToken)
        {
            if (operatorToken == "*" || operatorToken == "/") return 2;
            if (operatorToken == "+" || operatorToken == "-") return 1;
            return 0;
        }

        // Áp dụng toán tử vào 2 toán hạng
        private static double ApplyOperator(string operatorToken, double value2, double value1)
        {
            switch (operatorToken)
            {
                case "+":
                    return value1 + value2;
                case "-":
                    return value1 - value2;
                case "*":
                    return value1 * value2;
                case "/":
                    if (value2 == 0)
                        throw new DivideByZeroException("Cannot divide by zero.");
                    return value1 / value2;
                default:
                    throw new InvalidOperationException("Invalid operator.");
            }
        }
    }


    public class ChuanHoaChuoi
    {
        public static string NormalizeString(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            // Thay thế ký tự 'đ' và 'Đ' trước khi xử lý chuẩn hóa
            input = input.Replace("đ", "d").Replace("Đ", "d");

            // Loại bỏ dấu tiếng Việt
            string normalized = input.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();
            foreach (var c in normalized)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }
            normalized = stringBuilder.ToString();

            // Loại bỏ khoảng trắng, ký tự đặc biệt và chuyển thành chữ thường
            normalized = Regex.Replace(normalized, @"\s+", ""); // Loại bỏ khoảng trắng
            normalized = Regex.Replace(normalized, @"\W+", ""); // Loại bỏ ký tự không phải chữ cái/số
            normalized = normalized.ToLower(); // Chuyển chữ hoa thành chữ thường

            return normalized;
        }

    }

}

public static class OfficeUtils
{
    ///// <summary>
    ///// Tạo file docx từ file docx và metadata
    ///// </summary>
    ///// <param name="content">Stream file</param>
    ///// <param name="list">Danh sách meta data</param>
    //public static void GenDocxFromDocxAndMetaData(ref MemoryStream content, List<KeyValueModel> list)
    //{
    //    Document document = new Document();
    //    document.LoadFromStream(content, Spire.Doc.FileFormat.Auto);
    //    list.ForEach(x => document.Replace(x.Key, x.Value, false, true));
    //    MemoryStream convertData = new MemoryStream(0);
    //    document.SaveToStream(convertData, FileFormat.Docx);
    //    content = convertData;
    //}

    ///// <summary>
    ///// Tạo file docx từ file docx và metadata
    ///// </summary>
    ///// <param name="pathFileSource">Đường dẫn lưu file</param>
    ///// <param name="list">Danh sách meta data</param>
    ///// <returns>Stream of file</returns>
    //public static MemoryStream GenDocxFromDocxAndMetaData(string pathFileSource, List<KeyValueModel> list)
    //{
    //    Document document = new Document();
    //    document.LoadFromFile(pathFileSource);
    //    list.ForEach(x => document.Replace(x.Key, x.Value, false, true));
    //    MemoryStream convertData = new MemoryStream(0);
    //    document.SaveToStream(convertData, FileFormat.Docx);
    //    return convertData;
    //}

    ///// <summary>
    ///// Tạo file docx từ file docx và metadata
    ///// </summary>
    ///// <param name="pathFileSource">Đường dẫn lưu file nguồn</param>
    ///// <param name="pathFileSave">Đường dẫn lưu file đích</param>
    ///// <param name="list">Danh sách meta data</param>
    ///// <returns></returns>
    //public static void GenDocxFromDocxAndMetaDataAndSaveToFile(string pathFileSource, string pathFileSave, List<KeyValueModel> list)
    //{
    //    Document document = new Document();
    //    document.LoadFromFile(pathFileSource);
    //    list.ForEach(x => document.Replace(x.Key, x.Value, false, true));
    //    MemoryStream convertData = new MemoryStream(0);
    //    document.SaveToFile(pathFileSave, FileFormat.Docx);
    //}

    public static MemoryStream GeneratePdfFromWordBase64MetaData(MemoryStream content, List<KeyValueModel> listDataReplace, List<TableDataModel> listTableData = null)
    {
        // Check PDF/A
        using (Document document = new Document())
        {
            document.LoadFromStream(content, FileFormat.Auto);

            if (listDataReplace != null && listDataReplace.Any())
            {
                listDataReplace.ForEach(x => document.Replace(x.Key, x.Value, false, true));
            }

            if (listTableData != null && listTableData.Any())
            {
                // Lấy section đầu tiên (lưu ý xử lý với tài liệu có nhiều section)
                // Thêm section trong word
                // Vào tab Layout (hoặc Page Layout trên một số phiên bản).
                // Chọn Breaks → trong nhóm Section Breaks, chọn một trong các loại sau:
                //Log.Information($"Số lượng section trong tài liệu: {doc.Sections.Count}");

                var tableMap = listTableData.ToDictionary(t => t.TableName, t => t);

                foreach (Section section in document.Sections)
                {
                    // Duyệt từng bảng trong Section để tìm bảng cần xử lý
                    foreach (Table targetTable in section.Tables)
                    {
                        if (targetTable.Rows.Count == 0) continue;
                        // Lấy dòng cuối cùng
                        TableRow templateRow = targetTable.Rows[targetTable.Rows.Count - 1];
                        int templateRowIndex = targetTable.Rows.Count - 1;

                        if (templateRow != null)
                        {
                            // Lấy text cell đầu tiên của dòng template
                            TableCell firstCell = templateRow.Cells[0];
                            string text = firstCell.Paragraphs.Count > 0 ? firstCell.Paragraphs[0].Text : "";
                            string tableKey = tableMap.Keys.FirstOrDefault(k => text.StartsWith(k));

                            TableDataModel targetTableData = null;


                            foreach (var tbData in listTableData)
                            {
                                // Nếu text bắt đầu bằng tên bảng, lấy dữ liệu của bảng đó
                                if (text.StartsWith(tbData.TableName))
                                {
                                    targetTableData = tbData;
                                    break;
                                }
                            }

                            if (targetTableData != null)
                            {
                                // Thưc hiện điền dữ liệu vào bảng
                                int numCols = templateRow.Cells.Count;
                                var dataRows = targetTableData.TableData;
                                if (dataRows == null || dataRows.Count == 0)
                                {
                                    // Nếu không có dữ liệu, xoá dòng template
                                    targetTable.Rows.Remove(templateRow);
                                    continue;
                                }
                                foreach (var rowData in dataRows)
                                {
                                    TableRow newRow = (TableRow)templateRow.Clone();

                                    for (int c = 0; c < newRow.Cells.Count; c++)
                                    {
                                        foreach (Paragraph para in newRow.Cells[c].Paragraphs)
                                        {
                                            string cellText = para.Text;

                                            // Xử lý đặc biệt cho cell đầu tiên có {#ten_bang#}{index}
                                            cellText = Regex.Replace(cellText, targetTableData.TableName, ""); // Xoá phần tên bảng

                                            // Thay thế các placeholder còn lại như {index}, {name}, ...
                                            foreach (var key in rowData.Keys)
                                            {
                                                cellText = cellText.Replace(key, rowData[key]);
                                            }

                                            para.Text = cellText;
                                        }
                                    }

                                    targetTable.Rows.Insert(templateRowIndex, newRow);
                                    templateRowIndex++;
                                }

                                // Xoá dòng template sau khi chèn
                                targetTable.Rows.Remove(templateRow);
                            }
                        }
                    }
                }
            }

            ToPdfParameterList toPdf = new ToPdfParameterList()
            {
                UsePSCoversion = true
            };

            toPdf.PdfConformanceLevel = Spire.Pdf.PdfConformanceLevel.Pdf_A3A;

            using (MemoryStream convertData = new MemoryStream())
            {
                document.SaveToStream(convertData, toPdf);
                return new MemoryStream(convertData.ToArray());
            }
        }
    }

    public static FileBase64Response GeneratePdfFromWordBase64MetaData(PdfConvertFromWordBase64Model data)
    {
        using (MemoryStream contents = new MemoryStream(Convert.FromBase64String(data.FileBase64)))
        {
            var ct = GeneratePdfFromWordBase64MetaData(contents, data.Metadatas, data.TableDatas);

            byte[] bt = ct.ToArray();

            string fileBase64 = Convert.ToBase64String(bt);

            data.FileName = Path.GetFileNameWithoutExtension(data.FileName) + ".pdf";

            contents.Close();

            return new FileBase64Response()
            {
                FileBase64 = fileBase64,
                FileName = data.FileName
            };
        }
    }

    private static Table GetTableByIndex(Document document, int tableIndex)
    {
        int count = 0;
        foreach (Section section in document.Sections)
        {
            foreach (Table t in section.Tables)
            {
                if (count == tableIndex)
                    return t;
                count++;
            }
        }
        return null;
    }

    private static void FillRowWithData(TableRow row, List<string> data, TableRow templateRow, Document doc)
    {
        for (int i = 0; i < row.Cells.Count && i < data.Count; i++)
        {
            var cell = row.Cells[i];
            cell.Paragraphs.Clear();

            Paragraph para = cell.AddParagraph();

            var templatePara = templateRow.Cells[i].Paragraphs.Count > 0
                ? templateRow.Cells[i].Paragraphs[0]
                : null;

            if (templatePara != null)
            {
                para.Format.HorizontalAlignment = templatePara.Format.HorizontalAlignment;
                para.Format.AfterSpacing = templatePara.Format.AfterSpacing;
                para.Format.BeforeSpacing = templatePara.Format.BeforeSpacing;
                para.Format.LineSpacing = templatePara.Format.LineSpacing;
                para.Format.LineSpacingRule = templatePara.Format.LineSpacingRule;

                TextRange tr = para.AppendText(data[i]);
                if (templatePara.ChildObjects.Count > 0 && templatePara.ChildObjects[0] is TextRange sampleText)
                {
                    tr.CharacterFormat.FontName = sampleText.CharacterFormat.FontName;
                    tr.CharacterFormat.FontSize = sampleText.CharacterFormat.FontSize;
                    tr.CharacterFormat.Bold = sampleText.CharacterFormat.Bold;
                    tr.CharacterFormat.Italic = sampleText.CharacterFormat.Italic;
                    tr.CharacterFormat.TextColor = sampleText.CharacterFormat.TextColor;
                }
            }
            else
            {
                para.AppendText(data[i]);
            }
        }
    }

    public static string GenerateBase64DocxFromString(string content)
    {
        Document document = new Document();
        Section section = document.AddSection();
        Paragraph paragraph = section.AddParagraph();
        paragraph.AppendText(content);

        using (var stream = new MemoryStream())
        {
            document.SaveToStream(stream, FileFormat.Docx);
            return Convert.ToBase64String(stream.ToArray());
        }
    }


    public static MemoryStream GenerateDocxFromWordBase64MetaData(MemoryStream content, List<KeyValueModel> listDataReplace, List<TableDataModel> listTableData = null)
    {
        using (Document document = new Document())
        {
            document.LoadFromStream(content, FileFormat.Auto);

            // Thay thế text thường
            if (listDataReplace != null && listDataReplace.Any())
            {
                listDataReplace.ForEach(x => document.Replace(x.Key, x.Value, false, true));
            }

            // Xử lý bảng dữ liệu
            if (listTableData != null && listTableData.Any())
            {
                var tableMap = listTableData.ToDictionary(t => t.TableName, t => t);

                foreach (Section section in document.Sections)
                {
                    foreach (Table targetTable in section.Tables)
                    {
                        if (targetTable.Rows.Count == 0) continue;

                        TableRow templateRow = targetTable.Rows[targetTable.Rows.Count - 1];
                        int templateRowIndex = targetTable.Rows.Count - 1;

                        if (templateRow != null)
                        {
                            TableCell firstCell = templateRow.Cells[0];
                            string text = firstCell.Paragraphs.Count > 0 ? firstCell.Paragraphs[0].Text : "";

                            TableDataModel targetTableData = null;

                            foreach (var tbData in listTableData)
                            {
                                if (text.StartsWith(tbData.TableName))
                                {
                                    targetTableData = tbData;
                                    break;
                                }
                            }

                            if (targetTableData != null)
                            {
                                int numCols = templateRow.Cells.Count;
                                var dataRows = targetTableData.TableData;

                                if (dataRows == null || dataRows.Count == 0)
                                {
                                    targetTable.Rows.Remove(templateRow);
                                    continue;
                                }

                                foreach (var rowData in dataRows)
                                {
                                    TableRow newRow = (TableRow)templateRow.Clone();

                                    for (int c = 0; c < newRow.Cells.Count; c++)
                                    {
                                        foreach (Paragraph para in newRow.Cells[c].Paragraphs)
                                        {
                                            string cellText = para.Text;
                                            cellText = Regex.Replace(cellText, targetTableData.TableName, "");

                                            foreach (var key in rowData.Keys)
                                            {
                                                cellText = cellText.Replace(key, rowData[key]);
                                            }

                                            para.Text = cellText;
                                        }
                                    }

                                    targetTable.Rows.Insert(templateRowIndex, newRow);
                                    templateRowIndex++;
                                }

                                targetTable.Rows.Remove(templateRow);
                            }
                        }
                    }
                }
            }

            // Trả về MemoryStream file Docx
            using (MemoryStream resultStream = new MemoryStream())
            {
                document.SaveToStream(resultStream, FileFormat.Docx);
                return new MemoryStream(resultStream.ToArray());
            }
        }
    }

    public static FileBase64Response GenerateDocxFromWordBase64MetaData(DocxConvertFromWordBase64Model data)
    {
        using (MemoryStream contents = new MemoryStream(Convert.FromBase64String(data.FileBase64)))
        {
            var ct = GenerateDocxFromWordBase64MetaData(contents, data.Metadatas, data.TableDatas);

            byte[] bt = ct.ToArray();

            string fileBase64 = Convert.ToBase64String(bt);

            data.FileName = Path.GetFileNameWithoutExtension(data.FileName) + ".docx";

            contents.Close();

            return new FileBase64Response()
            {
                FileBase64 = fileBase64,
                FileName = data.FileName
            };
        }
    }
}


