using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Shared
{
    public class JsonStringLocalizer : IStringLocalizer
    {
        private readonly IMemoryCacheService _cache;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public JsonStringLocalizer(IMemoryCacheService cache, IWebHostEnvironment webHostEnvironment)
        {
            _cache = cache;
            _webHostEnvironment = webHostEnvironment;
        }

        public LocalizedString this[string name]
        {
            get
            {
                var value = GetString(name);
                return new LocalizedString(name, value ?? name, value == null);
            }
        }

        public LocalizedString this[string name, params object[] arguments]
        {
            get
            {
                var actualValue = this[name];
                return !actualValue.ResourceNotFound
                    ? new LocalizedString(name, string.Format(actualValue.Value, arguments), false)
                    : actualValue;
            }
        }

        public IEnumerable<LocalizedString> GetAllStrings(bool includeParentCultures)
        {
            var allData = GetAllJsonData().Result;

            using (JsonDocument document = JsonDocument.Parse(allData))
            {
                JsonElement root = document.RootElement;

                foreach (JsonProperty property in root.EnumerateObject())
                {
                    string key = property.Name;
                    string value = property.Value.GetString();

                    yield return new LocalizedString(key, value, false);
                }
            }
        }

        private string GetString(string key)
        {
            return GetValueFromJSON(key);
        }

        private async Task<string> GetAllJsonData()
        {
            var filePath = Path.Combine(_webHostEnvironment.WebRootPath, $"Localization/{Thread.CurrentThread.CurrentCulture.Name}.json");
            return await _cache.GetOrCreate(filePath, () =>
            {
                using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    using (StreamReader streamReader = new StreamReader(stream))
                    {
                        return Task.FromResult(streamReader.ReadToEnd());
                    }
                }
            });
        }

        private string GetValueFromJSON(string propertyName)
        {
            if (propertyName == null)
            {
                return default;
            }
            var allData = GetAllJsonData().Result;
            using (JsonDocument document = JsonDocument.Parse(allData))
            {
                JsonElement root = document.RootElement;
                if (root.TryGetProperty(propertyName, out JsonElement propertyValue))
                {
                    return propertyValue.GetString();
                }
            }
            return default;
        }
    }
}
