using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Localization;
using System;

namespace Core.Shared
{
    public class JsonStringLocalizerFactory : IStringLocalizerFactory
    {
        private readonly IMemoryCacheService _cache;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public JsonStringLocalizerFactory(IMemoryCacheService cache, IWebHostEnvironment webHostEnvironment)
        {
            _cache = cache;
            _webHostEnvironment = webHostEnvironment;
        }

        public IStringLocalizer Create(Type resourceSource) =>
            new JsonStringLocalizer(_cache, _webHostEnvironment);

        public IStringLocalizer Create(string baseName, string location) =>
           new JsonStringLocalizer(_cache, _webHostEnvironment);
    }
}
