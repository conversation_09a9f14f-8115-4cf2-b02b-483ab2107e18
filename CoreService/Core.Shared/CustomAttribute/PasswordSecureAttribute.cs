using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;

namespace Core.Shared.CustomAttribute
{
    public class PasswordSecureAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
                return ValidationResult.Success;

            var localizer = validationContext.GetService<IStringLocalizer<Resources>>();

            if (!Utils.IsValidPassword(value.ToString()))
            {
                return new ValidationResult(localizer[$"{ErrorMessage}"]);
            }

            return ValidationResult.Success;
        }
    }
}