using Microsoft.AspNetCore.Http;
using Nest;
using System;
using System.Security.Claims;

namespace Core.Shared.ContextAccessor
{
    public class HttpContextAccessorWrapper : IContextAccessor
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private string _traceId;

        public HttpContextAccessorWrapper(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
            _traceId = Guid.NewGuid().ToString();
        }

        public string CorrelationId
        {
            get => _httpContextAccessor.HttpContext?.Request.Headers["X-CorrelationId"];
            set { }
        }

        public string TraceId
        {
            get
            {
                return _traceId;
            }
            set { }
        }

        public int? UserId
        {
            get
            {
                var obj = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier);
                int rs = 0;
                if (obj != null && !string.IsNullOrEmpty(obj.Value))
                    int.TryParse(obj.Value, out rs);
                if (rs == 0)
                    return null;
                return rs;
            }
            set { }
        }

        public string UserName
        {
            get => _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Name)?.Value;
            set { }
        }

        public string MaCB { get; set; } = "";

        public int? GiaoVienId { get; set; }

        public string Language
        {
            get
            {
                var language = _httpContextAccessor.HttpContext?.Request.Headers["X-Language"];
                return string.IsNullOrEmpty(language) ? LanguageConstant.VI : language;
            }
            set { }
        }

        public SystemLogModel SystemLog { get; set; } = new SystemLogModel();
    }
}
