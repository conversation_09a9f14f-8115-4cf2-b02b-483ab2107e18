using System;
using System.Text.Json.Serialization;
using Core.Shared;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Core.DataLog
{
    [BsonIgnoreExtraElements]
    public class ForgotPasswordLog
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("trace_id")]
        public string TraceId { get; set; }

        [BsonElement("user_id")]
        public int UserId { get; set; }

        [BsonElement("user_name")]
        public string UserName { get; set; }

        [BsonElement("user_email")]
        public string UserEmail { get; set; }

        [BsonElement("token")]
        public string Token { get; set; }

        //[BsonElement("secret_key")]
        //public string SecretKey { get; set; }

        [BsonElement("is_updated_password")]
        public bool IsUpdatedPassword { get; set; }

        [BsonElement("created_date")]
        [BsonRepresentation(BsonType.DateTime)]
        public DateTime CreatedDate { get; set; }

        [BsonElement("expire_time")]
        [BsonRepresentation(BsonType.DateTime)]
        public DateTime ExpireTime { get; set; }
    }
}
