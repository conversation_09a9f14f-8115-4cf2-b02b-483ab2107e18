using System.ComponentModel.DataAnnotations;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using Core.Shared;

namespace Core.Data
{
    public class BaseTableDefault
    {
        [Key]
        [Column("id", Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column("order", Order = 100)]
        public int Order { get; set; } = 0;

        [Column("is_active", Order = 101)]
        public bool IsActive { get; set; } = true;

        [Column("description", Order = 102)]
        public string Description { get; set; }

        [Column("created_date", Order = 103)]
        public DateTime? CreatedDate { get; set; } = DateTime.Now;

        [Column("created_user_id", Order = 104)]
        public int? CreatedUserId { get; set; } = UserConstants.AdministratorId;

        [Column("modified_date", Order = 105)]
        public DateTime? ModifiedDate { get; set; }

        [Column("modified_user_id", Order = 106)]
        public int? ModifiedUserId { get; set; }
    }

    public class BaseTableWithIdentityNumber : BaseTableDefault
    {
        [Column("identity_number", Order = 99)]
        public long IdentityNumber { get; set; }
    }
}
