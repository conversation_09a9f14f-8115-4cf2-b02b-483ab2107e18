using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using Core.Shared;

namespace Core.Data
{
    [Table("log_send_mail")]
    public class SendMailLogEntity
    {
        [Key]
        [Column("id", Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column("trace_id")]
        public string TraceId { get; set; }

        [Column("to_email")]
        public string ToEmail { get; set; }

        [Column("email_temp_code")]
        public string EmailTemplateCode { get; set; }

        [Column("email_temp_name")]
        public string EmailTemplateName { get; set; }

        [Column("subject")]
        public string Subject { get; set; }
        /// <summary>
        /// Enum: SendMailStatus
        /// </summary>

        [Column("status")]
        public int SendMailStatus { get; set; }
        /// <summary>
        /// MessageId nhận về sau khi gọi FluentEmail gửi mail
        /// </summary>

        [Column("message_id")]
        public string MessageId { get; set; }
        /// <summary>
        /// ErrorMesssage nhận về sau khi gọi FluentEmail gửi mail (nếu có lỗi sẽ có dữ liệu)
        /// </summary>
        [Column("error_messsage")]
        public string ErrorMesssage { get; set; }

        [Column("created_date")]
        public DateTime CreatedDate { get; set; }
    }
}
