using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace Core.Data
{
    [Table("sys_email_template")]
    public class EmailTemplate : BaseTableDefault
    {
        public EmailTemplate()
        {
        }

        [Column("code")]
        public string Code { get; set; }

        [Column("name")]
        public string Name { get; set; }

        [Column("from_email")]
        public string FromEmail { get; set; }

        [Column("from_user")]
        public string FromUser { get; set; }

        [Column("is_high_priority")]
        public bool IsHighPriority { get; set; }

        [Column("cc")]
        public string CCJson
        {
            get
            {
                return CC == null ? null : JsonSerializer.Serialize(CC);
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    CC = null;
                else
                    CC = JsonSerializer.Deserialize<List<string>>(value);
            }
        }

        [NotMapped]
        public List<string> CC { get; set; }

        [Column("bcc")]
        public string BCCJson
        {
            get
            {
                return BCC == null ? null : JsonSerializer.Serialize(BCC);
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    BCC = null;
                else
                    BCC = JsonSerializer.Deserialize<List<string>>(value);
            }
        }

        [NotMapped]
        public List<string> BCC { get; set; }

        [Column("subject")]
        public string Subject { get; set; }

        [Column("template")]
        public string Template { get; set; }
    }
}
