using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXepHangTotNghiep")]
    public class SvXepHangTotNghiep
    {
        
        public SvXepHangTotNghiep()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_hang")]
        public int IdXepHang { get; set; }

        [Column("Tu_diem")]
        public float TuDiem { get; set; }

        [Column("Tu_diem_thang10")]
        public float TuDiemThang10 { get; set; }

        [Column("Den_diem")]
        public float DenDiem { get; set; }

        [Column("Den_diem_thang10")]
        public float DenDiemThang10 { get; set; }

        [Column("Xep_hang"), <PERSON><PERSON><PERSON>th(20)]
        public string XepHang { get; set; }

        [Column("Xep_hang_en"), MaxLength(50)]
        public string XepHangEn { get; set; }

        [Column("Ma_xep_hang"), MaxLength(20)]
        public string MaXepHang { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }
    }
}
