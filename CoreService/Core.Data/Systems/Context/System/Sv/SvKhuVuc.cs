using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Core.Data
{
    [Table("svKhuVuc")]
    public class SvKhuVuc
    {

        public SvKhuVuc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_kv")]
        public int IdKhuVuc { get; set; }

        [Column("Ma_kv"), MaxLength(10)]
        public string Ma<PERSON>huVuc { get; set; }

        [<PERSON>umn("Ten_kv"), MaxLength(50)]
        public string <PERSON><PERSON>huVuc { get; set; }

        [Column("Diem_cong_kv"), Precision(18, 2)]
        public decimal? DiemCongKv { get; set; }


    }
}
