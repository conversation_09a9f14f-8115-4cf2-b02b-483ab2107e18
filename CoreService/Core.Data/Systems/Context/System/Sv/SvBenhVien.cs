
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("svBenhVien")]
    public class SvBenhVien
    {
        public SvBenhVien()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_benh_vien")]
        public int IdBenhVien { get; set; }

        [Column("Ma_benh_vien"), MaxLength(50)]
        public string MaBenhVien { get; set; }

        [Column("Ten_benh_vien"), MaxLength(255)]
        public string TenBenhVien { get; set; }

        [Column("Tuyen_bv_truoc_1_1_2025"), MaxLength(255)]
        public string TuyenBvTruoc2025 { get; set; }

        [Column("Dang_ky_kcb_ban_dau")]
        public string DangKyKcbBanDau { get; set; }

        [Column("Dia_chi")]
        public string <PERSON>a<PERSON><PERSON> { get; set; }

        [Column("Ghi_chu")]
        public string <PERSON><PERSON><PERSON><PERSON> { get; set; }
    }
}
