using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svChiTieuTuyenSinh")]
    public class SvChiTieuTuyenSinh
    {

        public SvChiTieuTuyenSinh()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_chi_tieu_tuyen_sinh")]
        public int IdChiTieuTuyenSinh { get; set; }

        [Column("Nam_tuyen_sinh")]
        public int NamTuyenSinh { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("ID_nganh")]
        public int IdNganh { get; set; }

        [Column("Chi_tieu_tuyen_sinh")]
        public int ChiTieuTuyenSinh { get; set; }

        [Column("Diem_san_xet_tuyen")]
        public Decimal DiemSanXetTuyen { get; set; }

        [Column("Chi_tieu_tuyen_sinh1")]
        public int ChiTieuTuyenSinh1 { get; set; }

    }
}
