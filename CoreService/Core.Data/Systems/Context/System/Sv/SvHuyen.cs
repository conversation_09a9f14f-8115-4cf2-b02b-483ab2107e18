using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svHuyen")]
    public class SvHuyen
    {

        public SvHuyen()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [<PERSON>umn("ID_huyen"), MaxLength(5)]
        public string IdHuyen { get; set; }

        [Column("ID_tinh")]
        public string IdTinh { get; set; }

        [Column("Ten_huyen"), <PERSON><PERSON>ength(50)]
        public string TenHuyen { get; set; }

        [Column("Ten_huyen_en"), <PERSON><PERSON>ength(50)]
        public string TenHuyenEn { get; set; }

        [<PERSON>umn("ID_huyen_cu"), MaxLength(5)]
        public string IdHuyenCu { get; set; }

        [Column("ID_huyen_cu1"), <PERSON><PERSON><PERSON><PERSON>(5)]
        public string IdHuyenCu1 { get; set; }

        [Column("Ten_huyen_cu"), Max<PERSON>ength(50)]
        public string TenHuyenCu { get; set; }



    }
}
