using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDiemCongThuc")]
    public class SvDiemCongThuc
    {

        public SvDiemCongThuc()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_cong_thuc")]
        public int IdCongThuc { get; set; }

        [Column("Ten_cong_thuc"), MaxLength(200)]
        public string TenCongThuc { get; set; }

        [Column("Cong_thuc_tinh_diem_TBCHP")]
        public string CongThucTinhDiemTBCHP { get; set; }

        [Column("Cong_thuc_tinh_diem_TBCBP")]
        public string CongThucTinhDiemTBCBP { get; set; }


    }
}
