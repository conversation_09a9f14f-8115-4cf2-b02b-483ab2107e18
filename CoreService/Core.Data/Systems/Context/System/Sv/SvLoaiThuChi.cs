using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLoaiThuChi")]
    public class SvLoaiThuChi
    {

        public SvLoaiThuChi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_thu_chi")]
        public int IdThuChi { get; set; }

        [Column("Ten_thu_chi"), MaxLength(100)]
        public string TenThuChi { get; set; }

        [Column("Thu_chi")]
        public bool? ThuChi { get; set; }

        [Column("So_tien")]
        public long? SoTien { get; set; }

        [Column("Hoc_lai")]
        public bool? HocLai { get; set; }

        [Column("Thi_lai")]
        public bool? ThiLai { get; set; }


        [Column("Hoc_phi")]
        public bool? HocPhi { get; set; }


        [Column("Kinh_phi_DT")]
        public bool? KinhPhiDt { get; set; }

        [Column("Khoan_thu_ktx")]
        public bool? KhoanThuKtx { get; set; }

        [Column("Khoan_thu_tien_phong")]
        public bool? KhoanThuTienPhong { get; set; }

        [Column("Khoan_tien_cuoc")]
        public bool? KhoanTienCuoc { get; set; }

        [Column("Ma_thu_chi")]
        public string MaThuChi { get; set; }

        [Column("Bao_hiem")]
        public bool? BaoHiem { get; set; }


    }
}
