using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDoiTuong")]
    public class SvDoiTuong
    {

        public SvDoiTuong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_dt")]
        public int IdDoiTuong { get; set; }

        [Column("Ma_dt"), MaxLength(5)]
        public string MaDoiTuong { get; set; }

        [Column("Ten_dt"), MaxLength(50)]
        public string TenDoiTuong { get; set; }

        [Column("Phan_tram_mien_giam")]
        public int PhanTramMienGiam { get; set; }


    }
}
