using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLoaiKhenThuong")]
    public class SvLoaiKhenThuong
    {

        public SvLoaiKhenThuong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_loai_kt")]
        public int IdLoaiKhenThuong { get; set; }

        [Column("Id_cap")]
        public int IdCap { get; set; }

        [Column("Lo<PERSON>_khen_thuong"), MaxLength(100)]
        public string <PERSON><PERSON><PERSON><PERSON>huong { get; set; }

        [Column("Diem_thuong")]
        public float DiemThuong { get; set; }


    }
}
