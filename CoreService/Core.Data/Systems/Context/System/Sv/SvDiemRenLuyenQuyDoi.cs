using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDiemRenLuyenQuyDoi")]
    public class SvDiemRenLuyenQuyDoi
    {

        public SvDiemRenLuyenQuyDoi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_Xep_loai")]
        public int IdXepLoai { get; set; }

        [Column("Xep_loai"), MaxLength(50)]
        public string XepLoai { get; set; }

        [Column("Tu_diem")]
        public float TuDiem { get; set; }

        [Column("Den_diem")]
        public float DenDiem { get; set; }

        [Column("Diem_cong10")]
        public float DiemCong10 { get; set; }

        [Column("Diem_cong4")]
        public float DiemCong4 { get; set; }


    }
}
