using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svNganHangThanhToanOnline")]
    public class SvNganHangThanhToanOnline
    {
        public SvNganHangThanhToanOnline()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_ngan_hang")]
        public int IdNganHang { get; set; }

        [Column("Ma_ngan_hang"), MaxLength(50)]
        public string <PERSON>NganHang { get; set; }

        [Column("Ten_ngan_hang"), MaxLength(200)]
        public string TenNganHang { get; set; }

        [Column("Cong_thanh_toan"), MaxLength(200)]
        public string CongThanhToan { get; set; }

        [Column("Active")]
        public bool Active { get; set; }

        [Column("qrCode")]
        public bool QrCode { get; set; }
    }
}
