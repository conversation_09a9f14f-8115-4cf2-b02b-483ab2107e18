using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDanhSach")]
    public class SvDanhSach
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_sv")]
        public int IdSv { get; set; }

        [Column("ID_lop")]
        public int IdLop { get; set; }

        [Column("Da_tot_nghiep")]
        public bool DaTotNghiep { get; set; }

        [Column("Ngoai_ngan_sach")]
        public bool NgoaiNganSach { get; set; }

        [Column("Khoi_nganh")]
        public int KhoiNganh { get; set; }

        [Column("Mat_khau"), MaxLength(50)]
        public string MatKhau { get; set; }

        [Column("Active")]
        public bool Active { get; set; }

        [Column("Mat_khau_phu_huynh"), MaxLength(50)]
        public string MatKhauPhuHuynh { get; set; }
    }
}
