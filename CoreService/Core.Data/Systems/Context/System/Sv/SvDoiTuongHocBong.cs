using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDoiTuongHocBong")]
    public class SvDoiTuongHocBong
    {

        public SvDoiTuongHocBong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_dt_hb")]
        public int IdDoiTuongHocBong { get; set; }

        [Column("Ma_dt_hb"), MaxLength(5)]
        public string MaDoiTuongHocBong { get; set; }

        [Column("Ten_dt_hb"), MaxLength(50)]
        public string DoiTuongHocBong { get; set; }

        [Column("Sotien_trocap")]
        public int SoTienTroCap { get; set; }

        [Column("Phantram_trocap")]
        public int PhanTramTroCap { get; set; }


    }
}
