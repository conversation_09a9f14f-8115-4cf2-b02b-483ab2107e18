using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Core.Data
{
    [Table("svChuongTrinhDaoTao")]
    public class SvChuongTrinhDaoTao
    {
        public SvChuongTrinhDaoTao()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_dt")]
        public int IdDt { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("ID_khoa")]
        public int IdKhoa { get; set; }

        [Column("Khoa_hoc")]
        public int KhoaHoc { get; set; }

        [Column("ID_chuyen_nganh")]
        public int IdChuyenNganh { get; set; }

        [Column("So_tin_chi")]
        public float SoTinChi { get; set; }

        [Column("So_ky_hoc")]
        public int SoKyHoc { get; set; }

        [Column("So")]
        public int So { get; set; }

        [Column("So_tin_chi_dinh_muc")]
        public int SoTinChiDinhMuc { get; set; }

        [Column("So_quyet_dinh_ctdt"), MaxLength(500)]
        public string SoQuyetDinhCtdt { get; set; }

        [Column("Chi_tiet_quyet_dinh_ctdt")]
        public string ChiTietQuyetDinhCtdt { get; set; }

        [Column("So_nam_dao_tao")]
        public float SoNamDaoTao { get; set; }
    }
}
