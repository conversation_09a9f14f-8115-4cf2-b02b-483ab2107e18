using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svNganh")]
    public class SvNganh
    {
        public SvNganh()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_nganh")]
        public int IdNganh { get; set; }

        [Column("Ma_nganh"), MaxLength(20)]
        public string MaNganh { get; set; }

        [Column("Ten_nganh"), Max<PERSON>ength(200)]
        public string TenNganh { get; set; }

        [Column("Ten_nganh_en"), Max<PERSON>ength(200)]
        public string TenNganhEn { get; set; }

        [Column("Su_pham")]
        public bool SuPham { get; set; }

        [Column("Ten_loai_van_bang"), <PERSON><PERSON><PERSON><PERSON>(100)]
        public string TenLoaiVanBang { get; set; }
    }


}

