using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("svPhuongAn")]
    public class SvPhuongAn
    {
        public SvPhuongAn()
        {
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_phuong_an")]
        public int IdPhuongAn { get; set; }

        [Column("Ma_phuong_an"), MaxLength(50)]
        public string MaPhuongAn { get; set; }

        [Column("Ten_phuong_an"), MaxLength(255)]
        public string TenPhuongAn { get; set; }

        [Column("Noi_dung")]
        public string NoiDung { get; set; }
    }
}
