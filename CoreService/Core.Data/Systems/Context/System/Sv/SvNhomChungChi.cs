using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svNhomChungChi")]
    public class SvNhomChungChi
    {

        public SvNhomChungChi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_nhom_chung_chi")]
        public int IdNhomChungChi { get; set; }

        [Column("Ky_hieu_nhom")]
        [MaxLength(20)]
        public string KyHieuNhom { get; set; }

        [Column("Nhom_chung_chi")]
        [MaxLength(200)]
        public string NhomChungChi { get; set; }
    }
}
