using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svXepHangNamDaoTao")]
    public class SvXepHangNamDaoTao
    {
        
        public SvXepHangNamDaoTao()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_xep_hang")]
        public int IdXepHang { get; set; }

        [Column("Nam_thu")]
        public int NamThu { get; set; }

        [Column("Tu_tin_chi")]
        public int TuTinChi { get; set; }

        [Column("Den_tin_chi")]
        public int DenTinChi { get; set; }

        [Column("Nam_thu_en"), MaxLength(50)]
        public string NamThuEn { get; set; }

    }
}
