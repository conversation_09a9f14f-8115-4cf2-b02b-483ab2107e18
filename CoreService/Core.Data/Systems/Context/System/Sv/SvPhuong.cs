using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svPhuong")]
    public class SvPhuong
    {

        public SvPhuong()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_phuong")]
        public int IdPhuong { get; set; }

        [Column("Ten_phuong"), MaxLength(100)]
        public string TenPhuong { get; set; }



    }
}
