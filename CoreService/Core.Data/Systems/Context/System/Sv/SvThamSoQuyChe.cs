using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svThamSoQuyChe")]
    public class SvThamSoQuyChe
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_tham_so_qc")]
        public int IdThamSoQC { get; set; }

        [Column("Quy_che")]
        public int QuyChe { get; set; }

        [Column("Ma_tham_so"), MaxLength(50)]
        public string MaThamSo { get; set; }

        [Column("Nhom_quy_che"), MaxLength(200)]
        public string NhomQuyChe { get; set; }

        [Column("Ten_tham_so"), MaxLength(500)]
        public string TenThamSo { get; set; }

        [Column("Gia_tri")]
        public float GiaTri { get; set; }

        [Column("UserName"), MaxLength(50)]
        public string UserName { get; set; }

        [Column("DateModify")]
        public DateTime? DateModify { get; set; }

        [Column("Active")]
        public bool Active { get; set; }
    }
}
