using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svVung")]
    public class SvVung
    {

        public SvVung()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_vung")]
        public int IdVung { get; set; }

        [<PERSON>umn("Ky_hieu"), MaxLength(10)]
        public string <PERSON>y<PERSON><PERSON> { get; set; }

        [Column("Ten_vung")]
        public string <PERSON>Vung { get; set; }

        [Column("Ghi_chu")]
        public string <PERSON>hi<PERSON>hu { get; set; }
    }
}
