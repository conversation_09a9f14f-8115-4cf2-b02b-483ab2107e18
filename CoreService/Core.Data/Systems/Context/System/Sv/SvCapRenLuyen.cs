using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svCapRenLuyen")]
    public class SvCapRenLuyen
    {

        public SvCapRenLuyen()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_cap_rl")]
        public int IdCapRenLuyen { get; set; }

        [Column("Ky_hieu"), MaxLength(10)]
        public string <PERSON>yHieu { get; set; }

        [Column("Ten_cap"), MaxLength(200)]
        public string TenCap { get; set; }

        [Column("Diem")]
        public int Diem { get; set; }


    }
}
