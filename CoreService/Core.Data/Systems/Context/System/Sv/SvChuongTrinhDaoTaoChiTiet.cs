using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Core.Data
{
    [Table("svChuongTrinhDaoTaoChiTiet")]
    public class SvChuongTrinhDaoTaoChiTiet
    {
        public SvChuongTrinhDaoTaoChiTiet()
        {

        }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_dt_mon")]
        public int IdDtMon { get; set; }

        [Column("ID_dt")]
        public int IdDt { get; set; }

        [Column("ID_mon")]
        public int IdMon { get; set; }

        [Column("Ky_thu")]
        public int KyThu { get; set; }

        [Column("So_hoc_trinh")]
        public float? SoHocTrinh { get; set; }

        [Column("Ly_thuyet")]
        public int LyThuyet { get; set; }

        [Column("Thuc_hanh")]
        public int ThucHanh { get; set; }

        [Column("Bai_tap")]
        public int BaiTap { get; set; }

        [Column("Bai_tap_lon")]
        public int BaiTapLon { get; set; }

        [Column("Thuc_tap")]
        public int ThucTap { get; set; }

        [Column("Tu_chon")]
        public bool TuChon { get; set; }

        [Column("STT_mon")]
        public int SttMon { get; set; }

        [Column("He_so")]
        public float HeSo { get; set; }

        [Column("Kien_thuc")]
        public int KienThuc { get; set; }

        [Column("Khong_tinh_TBCHT")]
        public bool KhongTinhTbcht { get; set; }

        [Column("Nhom_tu_chon")]
        public int NhomTuChon { get; set; }

        [Column("Tu_hoc")]
        public int? TuHoc { get; set; }

        [Column("So_tin_chi_tien_quyet")]
        public int? SoTinChiTienQuyet { get; set; }

        [Column("Ma_khoa_phu_trach"), MaxLength(50)]
        public string MaKhoaPhuTrach { get; set; }

        [Column("Mon_thuc_hanh")]
        public bool MonThucHanh { get; set; }

        [Column("Mon_tot_nghiep")]
        public bool MonTotNghiep { get; set; }

        [Column("Mon_dk_tn")]
        public bool? MonDkTn { get; set; }

        [Column("Mon_khoa_luan")]
        public bool? MonKhoaLuan { get; set; }

        [Column("So_hoc_trinh_thuc_hanh")]
        public float? SoHocTrinhThucHanh { get; set; }

        [Column("Mon_ly_thuyet_thuc_hanh")]
        public bool? MonLyThuyetThucHanh { get; set; }

        [Column("ID_cong_thuc")]
        public int IdCongThuc { get; set; }

        [Column("ModifyDate")]
        public DateTime? ModifyDate { get; set; }

        [Column("ModifyUserName"), MaxLength(50)]
        public string ModifyUserName { get; set; }

        [Column("Mien_hoc_phi")]
        public bool MienHocPhi { get; set; }

        [Column("Thang_nhap_diem")]
        public int ThangNhapDiem { get; set; }

        [Column("Diem_TBCHP_dat")]
        public decimal? DiemTbchpDat { get; set; }

        [Column("Diem_thi_dat")]
        public decimal? DiemThiDat { get; set; }

        [Column("ID_mon_cha")]
        public int IdMonCha { get; set; }

        [Column("URL_de_cuong_chi_tiet")]
        public string UrlDeCuongChiTiet { get; set; }
    }
}
