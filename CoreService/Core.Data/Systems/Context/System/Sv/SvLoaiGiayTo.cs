using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLoaiGiayTo")]
    public class SvLoaiGiayTo
    {

        public SvLoaiGiayTo()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_giay_to")]
        public int IdGiayTo { get; set; }

        [Column("Ma_giay_to"), MaxLength(5)]
        public string MaGiayTo { get; set; }

        [Column("Ten_giay_to"), MaxLength(100)]
        public string TenGiayTo { get; set; }

        [Column("STT")]
        public int Stt { get; set; }

        [Column("Bat_buoc")]
        public bool BatBuoc { get; set; }

        [Column("Mac_dinh")]
        public bool MacDinh { get; set; }

        [Column("Nhom")]
        public int Nhom { get; set; }

        [Column("Ghi_chu"), MaxLength(150)]
        public string <PERSON>hi<PERSON>hu { get; set; }

        [Column("ID_he")]
        public int IdHe { get; set; }

        [Column("ID_phong")]
        public int IdPhong { get; set; }

        [Column("Tuyen_sinh")]
        public bool TuyenSinh { get; set; }


    }
}
