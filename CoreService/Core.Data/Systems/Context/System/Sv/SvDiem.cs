using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svDiem")]
    public class SvDiem
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_diem")]
        public int IdDiem { get; set; }

        [Column("ID_dv"), MaxLength(20)]
        public string IdDv { get; set; }

        [Column("ID_sv")]
        public int IdSv { get; set; }

        [Column("ID_mon")]
        public int IdMon { get; set; }

        [Column("ID_dt")]
        public int IdDt { get; set; }

        [Column("Hoc_ky")]
        public int HocKy { get; set; }

        [Column("Nam_hoc"), MaxLength(10)]
        public string NamHoc { get; set; }

        [Column("Locked")]
        public int? Locked { get; set; }

        [Column("CreateDate")]
        public DateTime? CreateDate { get; set; }

        [Column("ModifyDate")]
        public DateTime? ModifyDate { get; set; }

        [Column("CreateUserName")]
        public string CreateUserName { get; set; }

        [Column("ModifyUserName")]
        public string ModifyUserName { get; set; }

        [Column("Diem_thuong_mon")]
        public decimal? DiemThuongMon { get; set; }
    }
}
