using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svLoaiChungChiDanhSachMon")]
    public class SvLoaiChungChiDanhSachMon
    {

        public SvLoaiChungChiDanhSachMon()
        {

        }

        [Column("ID_chung_chi")]
        public int IdChungChi { get; set; }

        [Column("ID_mon")]
        public int IdMon { get; set; }

        [Column("ID_dt")]
        public int IdDt { get; set; }
    }
}
