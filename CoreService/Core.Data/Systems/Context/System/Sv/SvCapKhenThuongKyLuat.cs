using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("svCapKhenThuongKyLuat")]
    public class SvCapKhenThuongKyLuat
    {

        public SvCapKhenThuongKyLuat()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_cap")]
        public int IdCap { get; set; }

        [Column("Ma_cap"), MaxLength(20)]
        public string MaCap { get; set; }

        [Column("Ten_cap"), MaxLength(50)]
        public string TenCap { get; set; }



    }
}
