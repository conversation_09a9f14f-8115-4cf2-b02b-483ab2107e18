using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Core.Data
{
    [Table("htThamSoHeThong")]
    public class HtThamSoHeThong
    {
        public HtThamSoHeThong() 
        {

        }

        [Key]
        [Column("ID_tham_so"), <PERSON><PERSON>ength(200)]
        public string IdThamSo { get; set; }

        [Column("Ten_tham_so"), MaxLength(50)]
        public string TenThamSo { get; set; }

        [Column("ID_ph")]
        public int IdPh { get; set; }

        [Column("Gia_tri"), Max<PERSON>ength(200)]
        public string GiaTri { get; set; }

        [<PERSON>umn("Ghi_chu"), Max<PERSON>ength(200)]
        public string Ghi<PERSON>hu { get; set; }

        [Column("Active")]
        public bool Active { get; set; }

        [Column("Nhom_tham_so"), <PERSON><PERSON>ength(200)]
        public string NhomThamSo { get; set; }

        [Column("UserName"), <PERSON><PERSON>ength(50)]
        public string UserName { get; set; }

        [Column("DateModify"), <PERSON><PERSON><PERSON><PERSON>(50)]
        public DateTime? DateModify { get; set; }
    }
}
