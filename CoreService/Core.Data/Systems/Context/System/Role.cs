using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("htVaiTro")]
    public class Role
    {
        public Role()
        {
        }

        [Key]
        [Column("ID_vai_tro")]
        public int Id { get; set; }

        [Column("Ten_vai_tro")]
        public string Name { get; set; }

        [Column("Mo_ta")]
        public string Description { get; set; }
    }
}
