using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Core.Data
{
    [Table("tkbGiaoAn")]
    public class TkbGiaoAn
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_giao_an")]
        public int IdGiaoAn { get; set; }

        [Column("ID_cb")]
        public int IdCb { get; set; }

        [Column("ID_lop")]
        public int IdLop { get; set; }

        [Column("ID_mon")]
        public int IdMon { get; set; }

        [Column("Tieu_de"), MaxLength(200)]
        public string TieuDe { get; set; }

        [Column("Mo_ta")]
        public string MoTa { get; set; }

        [Column("URL_giao_an"), MaxLength(500)]
        public string UrlGiaoAn { get; set; }
    }
}
