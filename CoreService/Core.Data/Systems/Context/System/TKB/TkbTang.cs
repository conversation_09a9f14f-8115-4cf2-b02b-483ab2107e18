using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbTang")]
    public class TkbTang
    {

        public TkbTang()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_TANG")]
        public int IdTang { get; set; }

        [Column("Ma_tang"), MaxLength(5)]
        public string MaTang { get; set; }

        [Column("Ten_tang"), Max<PERSON>ength(50)]
        public string TenTang { get; set; }

    }
}
