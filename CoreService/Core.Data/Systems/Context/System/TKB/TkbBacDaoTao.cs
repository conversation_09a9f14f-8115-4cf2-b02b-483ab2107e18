using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace Core.Data
{
    [Table("tkbBacDaoTao")]
    public class TkbBacDaoTao
    {
        public TkbBacDaoTao()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_bac_dao_tao")]
        public int IdBacDaoTao { get; set; }

        [Column("Ma_bac_dao_tao"), MaxLength(10)]
        public string MaBacDaoTao { get; set; }

        [Column("Bac_dao_tao"), MaxLength(100)]
        public string BacDaoTao { get; set; }
    }
}

