using System;
using System.Collections.Generic;

namespace Core.Data
{
    public partial class TkbLopTinChi
    {
        public int IdLopTc { get; set; }
        public int IdLopLt { get; set; }
        public int IdMonTc { get; set; }
        public int SttLop { get; set; }
        public int SoSvMin { get; set; }
        public int SoSvMax { get; set; }
        public DateTime TuNgay { get; set; }
        public DateTime DenNgay { get; set; }
        public int CaHoc { get; set; }
        public int SoTietTuan { get; set; }
        public int IdPhong { get; set; }
        public int IdCb { get; set; }
        public bool HuyLop { get; set; }
        public string LyDo { get; set; }
        public string NhomDangKy { get; set; }
        public DateTime? NgayThi { get; set; }
        public bool Bt { get; set; }
        public int DonGiaTiet { get; set; }
        public int SoTienLopTinChi { get; set; }
        public int DonGiaNopSv { get; set; }
    }
}
