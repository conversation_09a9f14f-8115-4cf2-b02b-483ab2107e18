using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Data
{
    [Table("tkbHocVi")]
    public class TkbHocVi
    {
        public TkbHocVi()
        {

        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("ID_hoc_vi")]
        public int IdHocVi { get; set; }

        [Column("Ma_hoc_vi"), MaxLength(10)]
        public string MaHocVi { get; set; }

        [Column("Hoc_vi"), MaxLength(100)]
        public string HocVi { get; set; }

        [Column("He_so_bu_khoa_hoc"), Precision(18, 2)]
        public decimal HeSoBuKhoaHoc { get; set; }


    }
}

