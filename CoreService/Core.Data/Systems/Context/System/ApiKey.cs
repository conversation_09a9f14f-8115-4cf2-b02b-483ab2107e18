using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("sys_api_key")]
    public class ApiKey : BaseTableDefault
    {
        public ApiKey()
        {
        }

        [Column("user_id")]
        public int UserId { get; set; }

        [Column("key")]
        public string Key { get; set; }

        [Column("valid_from")]
        public DateTime ValidFrom { get; set; }
        
        [Column("valid_to")]
        public DateTime ValidTo { get; set; }
    }
}
