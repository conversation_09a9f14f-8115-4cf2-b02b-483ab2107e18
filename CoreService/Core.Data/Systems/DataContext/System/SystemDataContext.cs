using Core.Data.Signing;
using Microsoft.EntityFrameworkCore;

namespace Core.Data
{
    public partial class SystemDataContext : DbContext
    {
        public SystemDataContext()
        {
        }

        public SystemDataContext(DbContextOptions<SystemDataContext> options)
            : base(options)
        {
        }

        #region Ký số

        public virtual DbSet<SgVisnamTaiKhoanKetNoi> SgVisnamTaiKhoanKetNois { get; set; }
        public virtual DbSet<SgChungThuSo> SgChungThuSos { get; set; }
        public virtual DbSet<SgMauChuKy> SgMauChuKys { get; set; }

        #endregion

        public virtual DbSet<Role> Roles { get; set; }
        public virtual DbSet<Permission> Permissions { get; set; }
        public virtual DbSet<RoleMapPermission> RoleMapPermissions { get; set; }
        public virtual DbSet<UserMapRole> UserMapRoles { get; set; }
        public virtual DbSet<HtPhanHe> HtPhanHes { get; set; }
        public virtual DbSet<HtUser> HtUsers { get; set; }
        public virtual DbSet<FileAttachment> FileAttachments { get; set; }
        public virtual DbSet<ApiKey> ApiKeys { get; set; }

        public virtual DbSet<SendMailLogEntity> SendMailLogs { get; set; }
        public virtual DbSet<SystemLogEntity> SystemLogs { get; set; }
        public virtual DbSet<ForgotPasswordLogEntity> ForgotPasswordLogs { get; set; }

        public virtual DbSet<SystemApplication> SystemApplication { get; set; }

        public virtual DbSet<HtUsersAccessHe> HtUsersAccessHes { get; set; }

        public virtual DbSet<EmailTemplate> EmailTemplates { get; set; }

        public virtual DbSet<SvHe> SvHes { get; set; }
        public virtual DbSet<SvKhoa> SvKhoas { get; set; }
        public virtual DbSet<SvLop> SvLops { get; set; }
        public virtual DbSet<TKBGiaoVien> TKBGiaoViens { get; set; }
        public virtual DbSet<HtThamSoHeThong> HtThamSoHeThongs { get; set; }

        public virtual DbSet<SvTonGiao> SvTonGiaos { get; set; }
        public virtual DbSet<SvQuocTich> SvQuocTichs { get; set; }
        public virtual DbSet<TkbChucDanh> TkbChucDanhs { get; set; }
        public virtual DbSet<SvGioiTinh> SvGioiTinhs { get; set; }
        public virtual DbSet<TkbHocVi> TkbHocVis { get; set; }
        public virtual DbSet<TkbHocHam> TkbHocHams { get; set; }
        public virtual DbSet<SvDanToc> SvDanTocs { get; set; }
        public virtual DbSet<SvNganh> SvNganhs { get; set; }
        public virtual DbSet<SvHuyen> SvHuyens { get; set; }
        public virtual DbSet<SvChuyenNganh> SvChuyenNganhs { get; set; }
        public virtual DbSet<SvTinh> SvTinhs { get; set; }
        public virtual DbSet<SvKhuVuc> SvKhuVucs { get; set; }
        public virtual DbSet<SvNhomDoiTuong> SvNhomDoiTuongs { get; set; }
        public virtual DbSet<SvDoiTuong> SvDoiTuongs { get; set; }
        public virtual DbSet<SvDoiTuongHocBong> SvDoiTuongHocBongs { get; set; }
        public virtual DbSet<SvCapKhenThuongKyLuat> SvCapKhenThuongKyLuats { get; set; }
        public virtual DbSet<SvLoaiKhenThuong> SvLoaiKhenThuongs { get; set; }
        public virtual DbSet<SvHanhVi> SvHanhVis { get; set; }
        public virtual DbSet<SvXuLy> SvXuLys { get; set; }
        public virtual DbSet<SvLoaiRenLuyen> SvLoaiRenLuyens { get; set; }
        public virtual DbSet<TkbPhongHoc> TkbPhongHocs { get; set; }
        public virtual DbSet<TkbToaNha> TkbToaNhas { get; set; }
        public virtual DbSet<TkbTang> TkbTangs { get; set; }
        public virtual DbSet<TkbCoSoDaoTao> TkbCoSoDaoTaos { get; set; }
        public virtual DbSet<TkbLoaiPhong> TkbLoaiPhongs { get; set; }
        public virtual DbSet<SvHinhThucHoc> SvHinhThucHocs { get; set; }
        public virtual DbSet<TkbChucVu> TkbChucVus { get; set; }
        public virtual DbSet<TkbHocKyDangKy> TkbHocKyDangKys { get; set; }
        public virtual DbSet<SvXepLoaiRenLuyen> SvXepLoaiRenLuyens { get; set; }
        public virtual DbSet<SvLoaiGiayTo> SvLoaiGiayTos { get; set; }
        public virtual DbSet<SvXepLoaiHocBong> SvXepLoaiHocBongs { get; set; }
        public virtual DbSet<SvPhuong> SvPhuongs { get; set; }
        public virtual DbSet<SvXa> SvXas { get; set; }
        public virtual DbSet<SvThanhPhanMon> SvThanhPhanMons { get; set; }
        public virtual DbSet<SvXepLoaiHocTapThangDiem10> SvXepLoaiHocTapThangDiem10s { get; set; }
        public virtual DbSet<SvXepHangNamDaoTao> SvXepHangNamDaoTaos { get; set; }
        public virtual DbSet<SvXepHangHocLuc> SvXepHangHocLucs { get; set; }
        public virtual DbSet<SvXepLoaiHocTap> SvXepLoaiHocTaps { get; set; }
        public virtual DbSet<SvXepHangTotNghiep> SvXepHangTotNghieps { get; set; }
        public virtual DbSet<SvXepHangTotNghiepThangDiem10> SvXepHangTotNghiepThangDiem10s { get; set; }
        public virtual DbSet<SvLoaiChungChi> SvLoaiChungChis { get; set; }
        public virtual DbSet<SvXepLoaiChungChi> SvXepLoaiChungChis { get; set; }
        public virtual DbSet<SvDiemRenLuyenQuyDoi> SvDiemRenLuyenQuyDois { get; set; }
        public virtual DbSet<SvLoaiQuyetDinh> SvLoaiQuyetDinhs { get; set; }
        public virtual DbSet<TkbBoMon> TkbBoMons { get; set; }
        public virtual DbSet<SvDoiTuongHocPhi> SvDoiTuongHocPhis { get; set; }
        public virtual DbSet<SvLoaiThuChi> SvLoaiThuChis { get; set; }
        public virtual DbSet<SvHinhThucThi> SvHinhThucThis { get; set; }
        public virtual DbSet<SvNoiThucTap> SvNoiThucTaps { get; set; }
        public virtual DbSet<SvThanhPhanMonTheoHe> SvThanhPhanMonTheoHes { get; set; }
        public virtual DbSet<SvDiemQuyDoi> SvDiemQuyDois { get; set; }
        public virtual DbSet<SvChuongTrinhDaoTaoKienThuc> SvChuongTrinhDaoTaoKienThucs { get; set; }
        public virtual DbSet<SvCapRenLuyen> SvCapRenLuyens { get; set; }
        public virtual DbSet<SvMonHoc> SvMonHocs { get; set; }
        public virtual DbSet<HtPhong> HtPhongs { get; set; }
        public virtual DbSet<SvChuongTrinhDaoTao> SvChuongTrinhDaoTaos { get; set; }
        public virtual DbSet<SvChuongTrinhDaoTaoChiTiet> SvChuongTrinhDaoTaoChiTiets { get; set; }
        public virtual DbSet<SvChuongTrinhDaoTaoNhomTuChon> SvChuongTrinhDaoTaoNhomTuChons { get; set; }
        public virtual DbSet<SvDiemCongThuc> SvDiemCongThucs { get; set; }
        public virtual DbSet<SvDanhSach> SvDanhSachs { get; set; }
        public virtual DbSet<SvDiem> SvDiems { get; set; }
        public virtual DbSet<SvMonHocTuongDuong> SvMonHocTuongDuongs { get; set; }
        public virtual DbSet<SvChuongTrinhDaoTaoRangBuoc> SvChuongTrinhDaoTaoRangBuocs { get; set; }
        public virtual DbSet<SvLoaiChungChiDanhSachMon> SvLoaiChungChiDanhSachMons { get; set; }
        public virtual DbSet<SvLoaiRangBuoc> SvLoaiRangBuocs { get; set; }
        public virtual DbSet<SvChiTieuTuyenSinh> SvChiTieuTuyenSinhs { get; set; }
        public virtual DbSet<SvBenhVien> SvBenhViens { get; set; }
        public virtual DbSet<SvPhuongAn> SvPhuongAns { get; set; }
        public virtual DbSet<SvPhuongThucDong> SvPhuongThucDongs { get; set; }
        public virtual DbSet<SvNhomChungChi> SvNhomChungChis { get; set; }
        public virtual DbSet<TkbGiaoAn> TkbGiaoAns { get; set; }
        public virtual DbSet<SvThamSoQuyChe> SvThamSoQuyChes { get; set; }
        public virtual DbSet<SvMucHuongBhyt> SvMucHuongBhyts { get; set; }
        public virtual DbSet<SvVung> SvVungs { get; set; }
        public virtual DbSet<TkbBacDaoTao> TkbBacDaoTaos { get; set; }
        public virtual DbSet<TkbLopTinChi> TkbLopTinChis { get; set; }
        public virtual DbSet<SvNganHangThanhToanOnline> SvNganHangThanhToanOnlines { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<HtUser>(entity =>
            {
                entity.HasKey(e => e.UserId);

                entity.ToTable("htUsers");

                entity.Property(e => e.UserId).HasColumnName("UserID");

                entity.Property(e => e.AdsPathLdap)
                    .HasMaxLength(200)
                    .HasColumnName("adsPathLDAP");

                entity.Property(e => e.DienThoai)
                    .HasMaxLength(100)
                    .HasColumnName("Dien_thoai");

                entity.Property(e => e.Email).HasMaxLength(100);

                entity.Property(e => e.FullName)
                    .IsRequired()
                    .HasMaxLength(30);

                entity.Property(e => e.IdBomon).HasColumnName("ID_Bomon");

                entity.Property(e => e.IdKhoa).HasColumnName("ID_khoa");

                entity.Property(e => e.IdPhong).HasColumnName("ID_phong");

                entity.Property(e => e.IdPhongBan).HasColumnName("ID_phong_ban");

                entity.Property(e => e.IdPhongBanAccessList)
                    .HasMaxLength(1000)
                    .HasColumnName("ID_phong_ban_access_list");

                entity.Property(e => e.LinkBusinessService)
                    .IsRequired()
                    .HasMaxLength(500)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.LinkHoaDon)
                    .IsRequired()
                    .HasMaxLength(500)
                    .HasColumnName("Link_hoa_don")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.LinkPortalService)
                    .IsRequired()
                    .HasMaxLength(500)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.LinkPublishService)
                    .IsRequired()
                    .HasMaxLength(500)
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.MaCanBoUser)
                    .HasMaxLength(50)
                    .HasColumnName("Ma_can_bo_user");

                entity.Property(e => e.Mac)
                    .HasMaxLength(50)
                    .HasColumnName("MAC");

                entity.Property(e => e.MatKhauHoaDon)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Mat_khau_hoa_don")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.MatKhauServiceHoaDon)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Mat_khau_service_hoa_don")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.MayTram)
                    .HasMaxLength(50)
                    .HasColumnName("May_tram")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.PassWord)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.PatternVnpt)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("PatternVNPT")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.SerialVnpt)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("SerialVNPT")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.TenDangNhapHoaDon)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Ten_dang_nhap_hoa_don")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.TenDangNhapServiceHoaDon)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("Ten_dang_nhap_service_hoa_don")
                    .HasDefaultValueSql("('')");

                entity.Property(e => e.UserName)
                    .IsRequired()
                    .HasMaxLength(255);

                entity.Property(e => e.UserNameLdap)
                    .HasMaxLength(10)
                    .HasColumnName("UserNameLDAP");
            });

            modelBuilder.Entity<TkbLopTinChi>(entity =>
            {
                entity.HasKey(e => e.IdLopTc);

                entity.ToTable("tkbLopTinChi");

                entity.Property(e => e.IdLopTc).HasColumnName("ID_lop_tc");

                entity.Property(e => e.Bt).HasColumnName("BT");

                entity.Property(e => e.CaHoc).HasColumnName("Ca_hoc");

                entity.Property(e => e.DenNgay)
                    .HasColumnType("datetime")
                    .HasColumnName("Den_ngay");

                entity.Property(e => e.DonGiaNopSv).HasColumnName("Don_gia_nop_sv");

                entity.Property(e => e.DonGiaTiet).HasColumnName("Don_gia_tiet");

                entity.Property(e => e.HuyLop).HasColumnName("Huy_lop");

                entity.Property(e => e.IdCb).HasColumnName("ID_cb");

                entity.Property(e => e.IdLopLt).HasColumnName("ID_lop_lt");

                entity.Property(e => e.IdMonTc).HasColumnName("ID_mon_tc");

                entity.Property(e => e.IdPhong).HasColumnName("ID_phong");

                entity.Property(e => e.LyDo)
                    .HasMaxLength(200)
                    .HasColumnName("Ly_do");

                entity.Property(e => e.NgayThi)
                    .HasColumnType("datetime")
                    .HasColumnName("Ngay_thi");

                entity.Property(e => e.NhomDangKy)
                    .HasMaxLength(50)
                    .HasColumnName("Nhom_dang_ky");

                entity.Property(e => e.SoSvMax).HasColumnName("So_sv_max");

                entity.Property(e => e.SoSvMin).HasColumnName("So_sv_min");

                entity.Property(e => e.SoTienLopTinChi).HasColumnName("So_tien_lop_tin_chi");

                entity.Property(e => e.SoTietTuan).HasColumnName("So_tiet_tuan");

                entity.Property(e => e.SttLop).HasColumnName("STT_lop");

                entity.Property(e => e.TuNgay)
                    .HasColumnType("datetime")
                    .HasColumnName("Tu_ngay");
            });

            modelBuilder.Entity<SvChuongTrinhDaoTaoChiTiet>()
                .HasKey(e => new { e.IdDt, e.IdMon });

            modelBuilder.Entity<SvDiem>()
               .HasKey(e => new { e.IdDv, e.IdSv, e.IdMon, e.IdDt });

            modelBuilder.Entity<SvMonHocTuongDuong>()
             .HasKey(e => new { e.IdMon1, e.IdMon });
            
            modelBuilder.Entity<SvLoaiChungChiDanhSachMon>()
             .HasKey(e => new { e.IdChungChi, e.IdDt, e.IdMon });

            modelBuilder.Entity<SvChuongTrinhDaoTaoNhomTuChon>()
          .HasKey(e => new { e.IdDt, e.NhomTuChon });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
