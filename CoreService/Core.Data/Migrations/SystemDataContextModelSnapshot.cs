// <auto-generated />
using System;
using Core.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Core.Data.Migrations
{
    [DbContext(typeof(SystemDataContext))]
    partial class SystemDataContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Core.Data.ApiKey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date")
                        .HasColumnOrder(103);

                    b.Property<int?>("CreatedUserId")
                        .HasColumnType("int")
                        .HasColumnName("created_user_id")
                        .HasColumnOrder(104);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description")
                        .HasColumnOrder(102);

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active")
                        .HasColumnOrder(101);

                    b.Property<string>("Key")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("key");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("modified_date")
                        .HasColumnOrder(105);

                    b.Property<int?>("ModifiedUserId")
                        .HasColumnType("int")
                        .HasColumnName("modified_user_id")
                        .HasColumnOrder(106);

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order")
                        .HasColumnOrder(100);

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("ValidFrom")
                        .HasColumnType("datetime2")
                        .HasColumnName("valid_from");

                    b.Property<DateTime>("ValidTo")
                        .HasColumnType("datetime2")
                        .HasColumnName("valid_to");

                    b.HasKey("Id");

                    b.ToTable("sys_api_key");
                });

            modelBuilder.Entity("Core.Data.EmailTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("BCCJson")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("bcc");

                    b.Property<string>("CCJson")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cc");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date")
                        .HasColumnOrder(103);

                    b.Property<int?>("CreatedUserId")
                        .HasColumnType("int")
                        .HasColumnName("created_user_id")
                        .HasColumnOrder(104);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description")
                        .HasColumnOrder(102);

                    b.Property<string>("FromEmail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("from_email");

                    b.Property<string>("FromUser")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("from_user");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active")
                        .HasColumnOrder(101);

                    b.Property<bool>("IsHighPriority")
                        .HasColumnType("bit")
                        .HasColumnName("is_high_priority");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("modified_date")
                        .HasColumnOrder(105);

                    b.Property<int?>("ModifiedUserId")
                        .HasColumnType("int")
                        .HasColumnName("modified_user_id")
                        .HasColumnOrder(106);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order")
                        .HasColumnOrder(100);

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("subject");

                    b.Property<string>("Template")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("template");

                    b.HasKey("Id");

                    b.ToTable("sys_email_template");
                });

            modelBuilder.Entity("Core.Data.FileAttachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BucketName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreateDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreateUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FilePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("Length")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifyUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ObjectName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Source")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("sys_file_attachment");
                });

            modelBuilder.Entity("Core.Data.ForgotPasswordLogEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date");

                    b.Property<DateTime>("ExpireTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("expire_time");

                    b.Property<bool>("IsUpdatedPassword")
                        .HasColumnType("bit")
                        .HasColumnName("is_updated_password");

                    b.Property<DateTime>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("modifited_date");

                    b.Property<string>("Token")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("token");

                    b.Property<string>("TraceId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("trace_id");

                    b.Property<string>("UserEmail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("user_email");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("user_name");

                    b.HasKey("Id");

                    b.ToTable("log_forgot_password");
                });

            modelBuilder.Entity("Core.Data.HtPhanHe", b =>
                {
                    b.Property<int>("IdPh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_ph");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdPh"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<bool>("Hiden")
                        .HasColumnType("bit")
                        .HasColumnName("Hiden");

                    b.Property<string>("HinhAnhPhanHe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Hinh_anh_phan_he");

                    b.Property<string>("KyHieuPhanHe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ky_hieu_phan_he");

                    b.Property<string>("MaPhanHe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ma_phan_he");

                    b.Property<string>("MieuTa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Mieu_ta");

                    b.Property<string>("PhanHe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Phan_he");

                    b.Property<int>("STT")
                        .HasColumnType("int")
                        .HasColumnName("STT");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Url");

                    b.HasKey("IdPh");

                    b.ToTable("htPhanHe");
                });

            modelBuilder.Entity("Core.Data.HtPhong", b =>
                {
                    b.Property<int>("IdPhong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_phong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdPhong"));

                    b.Property<string>("MaPhong")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_phong");

                    b.Property<string>("Phong")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("phong");

                    b.HasKey("IdPhong");

                    b.ToTable("htPhong");
                });

            modelBuilder.Entity("Core.Data.HtThamSoHeThong", b =>
                {
                    b.Property<string>("IdThamSo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("ID_tham_so");

                    b.Property<bool>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<DateTime?>("DateModify")
                        .HasMaxLength(50)
                        .HasColumnType("datetime2")
                        .HasColumnName("DateModify");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("GiaTri")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Gia_tri");

                    b.Property<int>("IdPh")
                        .HasColumnType("int")
                        .HasColumnName("ID_ph");

                    b.Property<string>("NhomThamSo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Nhom_tham_so");

                    b.Property<string>("TenThamSo")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_tham_so");

                    b.Property<string>("UserName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("UserName");

                    b.HasKey("IdThamSo");

                    b.ToTable("htThamSoHeThong");
                });

            modelBuilder.Entity("Core.Data.HtUser", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("UserID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserId"));

                    b.Property<int>("Active")
                        .HasColumnType("int");

                    b.Property<string>("AdsPathLdap")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("adsPathLDAP");

                    b.Property<string>("DienThoai")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Dien_thoai");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<int>("IdBomon")
                        .HasColumnType("int")
                        .HasColumnName("ID_Bomon");

                    b.Property<int>("IdKhoa")
                        .HasColumnType("int")
                        .HasColumnName("ID_khoa");

                    b.Property<int>("IdPhong")
                        .HasColumnType("int")
                        .HasColumnName("ID_phong");

                    b.Property<int?>("IdPhongBan")
                        .HasColumnType("int")
                        .HasColumnName("ID_phong_ban");

                    b.Property<string>("IdPhongBanAccessList")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ID_phong_ban_access_list");

                    b.Property<string>("LinkBusinessService")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("LinkHoaDon")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Link_hoa_don")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("LinkPortalService")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("LinkPublishService")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("MaCanBoUser")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_can_bo_user");

                    b.Property<string>("Mac")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("MAC");

                    b.Property<string>("MatKhauHoaDon")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Mat_khau_hoa_don")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("MatKhauServiceHoaDon")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Mat_khau_service_hoa_don")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("MayTram")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("May_tram")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("PassWord")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PatternVnpt")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("PatternVNPT")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("SerialVnpt")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("SerialVNPT")
                        .HasDefaultValueSql("('')");

                    b.Property<bool>("Supervisor")
                        .HasColumnType("bit");

                    b.Property<string>("TenDangNhapHoaDon")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_dang_nhap_hoa_don")
                        .HasDefaultValueSql("('')");

                    b.Property<string>("TenDangNhapServiceHoaDon")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_dang_nhap_service_hoa_don")
                        .HasDefaultValueSql("('')");

                    b.Property<int>("UserGroup")
                        .HasColumnType("int");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UserNameLdap")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("UserNameLDAP");

                    b.HasKey("UserId");

                    b.ToTable("htUsers", (string)null);
                });

            modelBuilder.Entity("Core.Data.HtUsersAccessHe", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("IdChuyenNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_chuyen_nganh");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdKhoa")
                        .HasColumnType("int")
                        .HasColumnName("ID_khoa");

                    b.Property<string>("IdLop")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ID_lop");

                    b.Property<int>("IdNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    b.Property<int>("KhoaHoc")
                        .HasColumnType("int")
                        .HasColumnName("Khoa_hoc");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserID");

                    b.HasKey("Id");

                    b.ToTable("htUsersAccessHe");
                });

            modelBuilder.Entity("Core.Data.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date")
                        .HasColumnOrder(103);

                    b.Property<int?>("CreatedUserId")
                        .HasColumnType("int")
                        .HasColumnName("created_user_id")
                        .HasColumnOrder(104);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description")
                        .HasColumnOrder(102);

                    b.Property<string>("GroupName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("group_name");

                    b.Property<int>("IdPhanHe")
                        .HasColumnType("int")
                        .HasColumnName("id_phan_he");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active")
                        .HasColumnOrder(101);

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("modified_date")
                        .HasColumnOrder(105);

                    b.Property<int?>("ModifiedUserId")
                        .HasColumnType("int")
                        .HasColumnName("modified_user_id")
                        .HasColumnOrder(106);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order")
                        .HasColumnOrder(100);

                    b.HasKey("Id");

                    b.ToTable("sys_permission");
                });

            modelBuilder.Entity("Core.Data.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_vai_tro");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Mo_ta");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ten_vai_tro");

                    b.HasKey("Id");

                    b.ToTable("htVaiTro");
                });

            modelBuilder.Entity("Core.Data.RoleMapPermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("PermissionId")
                        .HasColumnType("int")
                        .HasColumnName("permission_id");

                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("role_id");

                    b.HasKey("Id");

                    b.ToTable("sys_role_map_permission");
                });

            modelBuilder.Entity("Core.Data.SendMailLogEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date");

                    b.Property<string>("EmailTemplateCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("email_temp_code");

                    b.Property<string>("EmailTemplateName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("email_temp_name");

                    b.Property<string>("ErrorMesssage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("error_messsage");

                    b.Property<string>("MessageId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("message_id");

                    b.Property<int>("SendMailStatus")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("subject");

                    b.Property<string>("ToEmail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("to_email");

                    b.Property<string>("TraceId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("trace_id");

                    b.HasKey("Id");

                    b.ToTable("log_send_mail");
                });

            modelBuilder.Entity("Core.Data.Signing.SgChungThuSo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CACertificateBase64")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ca_certificate_base64");

                    b.Property<string>("CertificateBase64")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("certificate_base64");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date")
                        .HasColumnOrder(103);

                    b.Property<int?>("CreatedUserId")
                        .HasColumnType("int")
                        .HasColumnName("created_user_id")
                        .HasColumnOrder(104);

                    b.Property<string>("CreatedUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("created_user_name")
                        .HasColumnOrder(105);

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active")
                        .HasColumnOrder(101);

                    b.Property<string>("Issuer")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issuer");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("modified_date")
                        .HasColumnOrder(106);

                    b.Property<int?>("ModifiedUserId")
                        .HasColumnType("int")
                        .HasColumnName("modified_user_id")
                        .HasColumnOrder(107);

                    b.Property<string>("ModifiedUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modified_user_name")
                        .HasColumnOrder(108);

                    b.Property<DateTime>("NotAfter")
                        .HasColumnType("datetime2")
                        .HasColumnName("not_after");

                    b.Property<DateTime>("NotBefore")
                        .HasColumnType("datetime2")
                        .HasColumnName("not_before");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order")
                        .HasColumnOrder(100);

                    b.Property<int?>("ReferenceId")
                        .HasColumnType("int")
                        .HasColumnName("reference_id");

                    b.Property<string>("RootCertificateBase64")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("root_certificate_base64");

                    b.Property<string>("SerialNumber")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("serial_number");

                    b.Property<short>("Source")
                        .HasColumnType("smallint")
                        .HasColumnName("source");

                    b.Property<string>("SubjectName")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("subject_name");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.ToTable("sgChungThuSo");
                });

            modelBuilder.Entity("Core.Data.Signing.SgMauChuKy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CauHinhHienThiChuKyJson")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cau_hinh_hien_thi_chu_ky_json");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date")
                        .HasColumnOrder(103);

                    b.Property<int?>("CreatedUserId")
                        .HasColumnType("int")
                        .HasColumnName("created_user_id")
                        .HasColumnOrder(104);

                    b.Property<string>("CreatedUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("created_user_name")
                        .HasColumnOrder(105);

                    b.Property<string>("ImageBase64")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("image_base64");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active")
                        .HasColumnOrder(101);

                    b.Property<short>("LoaiKySuDung")
                        .HasColumnType("smallint");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("modified_date")
                        .HasColumnOrder(106);

                    b.Property<int?>("ModifiedUserId")
                        .HasColumnType("int")
                        .HasColumnName("modified_user_id")
                        .HasColumnOrder(107);

                    b.Property<string>("ModifiedUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modified_user_name")
                        .HasColumnOrder(108);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order")
                        .HasColumnOrder(100);

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.ToTable("sgMauChuKy");
                });

            modelBuilder.Entity("Core.Data.Signing.SgVisnamTaiKhoanKetNoi", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date")
                        .HasColumnOrder(103);

                    b.Property<int?>("CreatedUserId")
                        .HasColumnType("int")
                        .HasColumnName("created_user_id")
                        .HasColumnOrder(104);

                    b.Property<string>("CreatedUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("created_user_name")
                        .HasColumnOrder(105);

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active")
                        .HasColumnOrder(101);

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("key");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("modified_date")
                        .HasColumnOrder(106);

                    b.Property<int?>("ModifiedUserId")
                        .HasColumnType("int")
                        .HasColumnName("modified_user_id")
                        .HasColumnOrder(107);

                    b.Property<string>("ModifiedUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modified_user_name")
                        .HasColumnOrder(108);

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order")
                        .HasColumnOrder(100);

                    b.Property<string>("Secret")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("secret");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.ToTable("sgVisnamTaiKhoanKetNoi");
                });

            modelBuilder.Entity("Core.Data.SvBenhVien", b =>
                {
                    b.Property<int>("IdBenhVien")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_benh_vien");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdBenhVien"));

                    b.Property<string>("DangKyKcbBanDau")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Dang_ky_kcb_ban_dau");

                    b.Property<string>("DiaChi")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Dia_chi");

                    b.Property<string>("GhiChu")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("MaBenhVien")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_benh_vien");

                    b.Property<string>("TenBenhVien")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Ten_benh_vien");

                    b.Property<string>("TuyenBvTruoc2025")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Tuyen_bv_truoc_1_1_2025");

                    b.HasKey("IdBenhVien");

                    b.ToTable("svBenhVien");
                });

            modelBuilder.Entity("Core.Data.SvCapKhenThuongKyLuat", b =>
                {
                    b.Property<int>("IdCap")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_cap");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdCap"));

                    b.Property<string>("MaCap")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_cap");

                    b.Property<string>("TenCap")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_cap");

                    b.HasKey("IdCap");

                    b.ToTable("svCapKhenThuongKyLuat");
                });

            modelBuilder.Entity("Core.Data.SvCapRenLuyen", b =>
                {
                    b.Property<int>("IdCapRenLuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_cap_rl");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdCapRenLuyen"));

                    b.Property<int>("Diem")
                        .HasColumnType("int")
                        .HasColumnName("Diem");

                    b.Property<string>("KyHieu")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ky_hieu");

                    b.Property<string>("TenCap")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_cap");

                    b.HasKey("IdCapRenLuyen");

                    b.ToTable("svCapRenLuyen");
                });

            modelBuilder.Entity("Core.Data.SvChiTieuTuyenSinh", b =>
                {
                    b.Property<int>("IdChiTieuTuyenSinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_chi_tieu_tuyen_sinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdChiTieuTuyenSinh"));

                    b.Property<int>("ChiTieuTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Chi_tieu_tuyen_sinh");

                    b.Property<int>("ChiTieuTuyenSinh1")
                        .HasColumnType("int")
                        .HasColumnName("Chi_tieu_tuyen_sinh1");

                    b.Property<decimal>("DiemSanXetTuyen")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem_san_xet_tuyen");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    b.Property<int>("NamTuyenSinh")
                        .HasColumnType("int")
                        .HasColumnName("Nam_tuyen_sinh");

                    b.HasKey("IdChiTieuTuyenSinh");

                    b.ToTable("svChiTieuTuyenSinh");
                });

            modelBuilder.Entity("Core.Data.SvChuongTrinhDaoTao", b =>
                {
                    b.Property<int>("IdDt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dt");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDt"));

                    b.Property<string>("ChiTietQuyetDinhCtdt")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Chi_tiet_quyet_dinh_ctdt");

                    b.Property<int>("IdChuyenNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_chuyen_nganh");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdKhoa")
                        .HasColumnType("int")
                        .HasColumnName("ID_khoa");

                    b.Property<int>("KhoaHoc")
                        .HasColumnType("int")
                        .HasColumnName("Khoa_hoc");

                    b.Property<int>("So")
                        .HasColumnType("int")
                        .HasColumnName("So");

                    b.Property<int>("SoKyHoc")
                        .HasColumnType("int")
                        .HasColumnName("So_ky_hoc");

                    b.Property<float>("SoNamDaoTao")
                        .HasColumnType("real")
                        .HasColumnName("So_nam_dao_tao");

                    b.Property<string>("SoQuyetDinhCtdt")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("So_quyet_dinh_ctdt");

                    b.Property<float>("SoTinChi")
                        .HasColumnType("real")
                        .HasColumnName("So_tin_chi");

                    b.Property<int>("SoTinChiDinhMuc")
                        .HasColumnType("int")
                        .HasColumnName("So_tin_chi_dinh_muc");

                    b.HasKey("IdDt");

                    b.ToTable("svChuongTrinhDaoTao");
                });

            modelBuilder.Entity("Core.Data.SvChuongTrinhDaoTaoChiTiet", b =>
                {
                    b.Property<int>("IdDt")
                        .HasColumnType("int")
                        .HasColumnName("ID_dt");

                    b.Property<int>("IdMon")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon");

                    b.Property<int>("BaiTap")
                        .HasColumnType("int")
                        .HasColumnName("Bai_tap");

                    b.Property<int>("BaiTapLon")
                        .HasColumnType("int")
                        .HasColumnName("Bai_tap_lon");

                    b.Property<decimal?>("DiemTbchpDat")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem_TBCHP_dat");

                    b.Property<decimal?>("DiemThiDat")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem_thi_dat");

                    b.Property<float>("HeSo")
                        .HasColumnType("real")
                        .HasColumnName("He_so");

                    b.Property<int>("IdCongThuc")
                        .HasColumnType("int")
                        .HasColumnName("ID_cong_thuc");

                    b.Property<int>("IdDtMon")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dt_mon");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDtMon"));

                    b.Property<int>("IdMonCha")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_cha");

                    b.Property<bool>("KhongTinhTbcht")
                        .HasColumnType("bit")
                        .HasColumnName("Khong_tinh_TBCHT");

                    b.Property<int>("KienThuc")
                        .HasColumnType("int")
                        .HasColumnName("Kien_thuc");

                    b.Property<int>("KyThu")
                        .HasColumnType("int")
                        .HasColumnName("Ky_thu");

                    b.Property<int>("LyThuyet")
                        .HasColumnType("int")
                        .HasColumnName("Ly_thuyet");

                    b.Property<string>("MaKhoaPhuTrach")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_khoa_phu_trach");

                    b.Property<bool>("MienHocPhi")
                        .HasColumnType("bit")
                        .HasColumnName("Mien_hoc_phi");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifyDate");

                    b.Property<string>("ModifyUserName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ModifyUserName");

                    b.Property<bool?>("MonDkTn")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_dk_tn");

                    b.Property<bool?>("MonKhoaLuan")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_khoa_luan");

                    b.Property<bool?>("MonLyThuyetThucHanh")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_ly_thuyet_thuc_hanh");

                    b.Property<bool>("MonThucHanh")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_thuc_hanh");

                    b.Property<bool>("MonTotNghiep")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_tot_nghiep");

                    b.Property<int>("NhomTuChon")
                        .HasColumnType("int")
                        .HasColumnName("Nhom_tu_chon");

                    b.Property<float?>("SoHocTrinh")
                        .HasColumnType("real")
                        .HasColumnName("So_hoc_trinh");

                    b.Property<float?>("SoHocTrinhThucHanh")
                        .HasColumnType("real")
                        .HasColumnName("So_hoc_trinh_thuc_hanh");

                    b.Property<int?>("SoTinChiTienQuyet")
                        .HasColumnType("int")
                        .HasColumnName("So_tin_chi_tien_quyet");

                    b.Property<int>("SttMon")
                        .HasColumnType("int")
                        .HasColumnName("STT_mon");

                    b.Property<int>("ThangNhapDiem")
                        .HasColumnType("int")
                        .HasColumnName("Thang_nhap_diem");

                    b.Property<int>("ThucHanh")
                        .HasColumnType("int")
                        .HasColumnName("Thuc_hanh");

                    b.Property<int>("ThucTap")
                        .HasColumnType("int")
                        .HasColumnName("Thuc_tap");

                    b.Property<bool>("TuChon")
                        .HasColumnType("bit")
                        .HasColumnName("Tu_chon");

                    b.Property<int?>("TuHoc")
                        .HasColumnType("int")
                        .HasColumnName("Tu_hoc");

                    b.Property<string>("UrlDeCuongChiTiet")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("URL_de_cuong_chi_tiet");

                    b.HasKey("IdDt", "IdMon");

                    b.ToTable("svChuongTrinhDaoTaoChiTiet");
                });

            modelBuilder.Entity("Core.Data.SvChuongTrinhDaoTaoKienThuc", b =>
                {
                    b.Property<int>("IdKienThuc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_kien_thuc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdKienThuc"));

                    b.Property<bool>("MonChuyenNganh")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_chuyen_nganh");

                    b.Property<string>("TenKienThuc")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ten_kien_thuc");

                    b.Property<string>("TenKienThucEn")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("ten_kien_thuc_en");

                    b.HasKey("IdKienThuc");

                    b.ToTable("svChuongTrinhDaoTaoKienThuc");
                });

            modelBuilder.Entity("Core.Data.SvChuongTrinhDaoTaoNhomTuChon", b =>
                {
                    b.Property<int>("IdDt")
                        .HasColumnType("int")
                        .HasColumnName("ID_dt");

                    b.Property<int>("NhomTuChon")
                        .HasColumnType("int")
                        .HasColumnName("Nhom_tu_chon");

                    b.Property<int>("SoMonDangKy")
                        .HasColumnType("int")
                        .HasColumnName("So_mon_dang_ky");

                    b.Property<int>("SoMonTuChon")
                        .HasColumnType("int")
                        .HasColumnName("So_mon_tu_chon");

                    b.HasKey("IdDt", "NhomTuChon");

                    b.ToTable("svChuongTrinhDaoTaoNhomTuChon");
                });

            modelBuilder.Entity("Core.Data.SvChuongTrinhDaoTaoRangBuoc", b =>
                {
                    b.Property<int>("IdRb")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_rb");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdRb"));

                    b.Property<int>("IdDt")
                        .HasColumnType("int")
                        .HasColumnName("ID_dt");

                    b.Property<int>("IdMon")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon");

                    b.Property<int>("IdMonRb")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_rb");

                    b.Property<int>("LoaiRangBuoc")
                        .HasColumnType("int")
                        .HasColumnName("Loai_rang_buoc");

                    b.HasKey("IdRb");

                    b.ToTable("svChuongTrinhDaoTaoRangBuoc");
                });

            modelBuilder.Entity("Core.Data.SvChuyenNganh", b =>
                {
                    b.Property<int>("IdChuyenNganh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_chuyen_nganh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdChuyenNganh"));

                    b.Property<string>("ChuyenNganh")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("chuyen_nganh");

                    b.Property<string>("ChuyenNganhEn")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("chuyen_nganh_en");

                    b.Property<int>("IdNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    b.Property<string>("MaChuyenNganh")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_chuyen_nganh");

                    b.HasKey("IdChuyenNganh");

                    b.ToTable("svChuyenNganh");
                });

            modelBuilder.Entity("Core.Data.SvDanToc", b =>
                {
                    b.Property<int>("IdDanToc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dan_toc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDanToc"));

                    b.Property<string>("DanToc")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Dan_toc");

                    b.Property<string>("DanTocEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Dan_toc_en");

                    b.Property<string>("MaDanToc")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_dan_toc");

                    b.HasKey("IdDanToc");

                    b.ToTable("svDanToc");
                });

            modelBuilder.Entity("Core.Data.SvDanhSach", b =>
                {
                    b.Property<int>("IdSv")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_sv");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdSv"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<bool>("DaTotNghiep")
                        .HasColumnType("bit")
                        .HasColumnName("Da_tot_nghiep");

                    b.Property<int>("IdLop")
                        .HasColumnType("int")
                        .HasColumnName("ID_lop");

                    b.Property<int>("KhoiNganh")
                        .HasColumnType("int")
                        .HasColumnName("Khoi_nganh");

                    b.Property<string>("MatKhau")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Mat_khau");

                    b.Property<string>("MatKhauPhuHuynh")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Mat_khau_phu_huynh");

                    b.Property<bool>("NgoaiNganSach")
                        .HasColumnType("bit")
                        .HasColumnName("Ngoai_ngan_sach");

                    b.HasKey("IdSv");

                    b.ToTable("svDanhSach");
                });

            modelBuilder.Entity("Core.Data.SvDiem", b =>
                {
                    b.Property<string>("IdDv")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("ID_dv");

                    b.Property<int>("IdSv")
                        .HasColumnType("int")
                        .HasColumnName("ID_sv");

                    b.Property<int>("IdMon")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon");

                    b.Property<int>("IdDt")
                        .HasColumnType("int")
                        .HasColumnName("ID_dt");

                    b.Property<DateTime?>("CreateDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreateDate");

                    b.Property<string>("CreateUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreateUserName");

                    b.Property<decimal?>("DiemThuongMon")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem_thuong_mon");

                    b.Property<int>("HocKy")
                        .HasColumnType("int")
                        .HasColumnName("Hoc_ky");

                    b.Property<int>("IdDiem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_diem");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDiem"));

                    b.Property<int?>("Locked")
                        .HasColumnType("int")
                        .HasColumnName("Locked");

                    b.Property<DateTime?>("ModifyDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifyDate");

                    b.Property<string>("ModifyUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifyUserName");

                    b.Property<string>("NamHoc")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Nam_hoc");

                    b.HasKey("IdDv", "IdSv", "IdMon", "IdDt");

                    b.ToTable("svDiem");
                });

            modelBuilder.Entity("Core.Data.SvDiemCongThuc", b =>
                {
                    b.Property<int>("IdCongThuc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_cong_thuc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdCongThuc"));

                    b.Property<string>("CongThucTinhDiemTBCBP")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Cong_thuc_tinh_diem_TBCBP");

                    b.Property<string>("CongThucTinhDiemTBCHP")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Cong_thuc_tinh_diem_TBCHP");

                    b.Property<string>("TenCongThuc")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_cong_thuc");

                    b.HasKey("IdCongThuc");

                    b.ToTable("svDiemCongThuc");
                });

            modelBuilder.Entity("Core.Data.SvDiemQuyDoi", b =>
                {
                    b.Property<int>("IdXepLoai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepLoai"));

                    b.Property<float>("DenDiem")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem");

                    b.Property<int>("DenHocKy")
                        .HasColumnType("int")
                        .HasColumnName("Den_hoc_ky");

                    b.Property<string>("DenNamHoc")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Den_nam_hoc");

                    b.Property<string>("DiemChu")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)")
                        .HasColumnName("Diem_chu");

                    b.Property<float>("DiemSo")
                        .HasColumnType("real")
                        .HasColumnName("Diem_so");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<bool>("TichLuy")
                        .HasColumnType("bit")
                        .HasColumnName("Tich_luy");

                    b.Property<float>("TuDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem");

                    b.Property<int>("TuHocKy")
                        .HasColumnType("int")
                        .HasColumnName("Tu_hoc_ky");

                    b.Property<string>("TuNamHoc")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Tu_nam_hoc");

                    b.Property<string>("XepLoai")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_loai");

                    b.HasKey("IdXepLoai");

                    b.ToTable("svDiemQuyDoi");
                });

            modelBuilder.Entity("Core.Data.SvDiemRenLuyenQuyDoi", b =>
                {
                    b.Property<int>("IdXepLoai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_Xep_loai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepLoai"));

                    b.Property<float>("DenDiem")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem");

                    b.Property<float>("DiemCong10")
                        .HasColumnType("real")
                        .HasColumnName("Diem_cong10");

                    b.Property<float>("DiemCong4")
                        .HasColumnType("real")
                        .HasColumnName("Diem_cong4");

                    b.Property<float>("TuDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem");

                    b.Property<string>("XepLoai")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_loai");

                    b.HasKey("IdXepLoai");

                    b.ToTable("svDiemRenLuyenQuyDoi");
                });

            modelBuilder.Entity("Core.Data.SvDoiTuong", b =>
                {
                    b.Property<int>("IdDoiTuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dt");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDoiTuong"));

                    b.Property<string>("MaDoiTuong")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_dt");

                    b.Property<int>("PhanTramMienGiam")
                        .HasColumnType("int")
                        .HasColumnName("Phan_tram_mien_giam");

                    b.Property<string>("TenDoiTuong")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_dt");

                    b.HasKey("IdDoiTuong");

                    b.ToTable("svDoiTuong");
                });

            modelBuilder.Entity("Core.Data.SvDoiTuongHocBong", b =>
                {
                    b.Property<int>("IdDoiTuongHocBong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_dt_hb");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDoiTuongHocBong"));

                    b.Property<string>("DoiTuongHocBong")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_dt_hb");

                    b.Property<string>("MaDoiTuongHocBong")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_dt_hb");

                    b.Property<int>("PhanTramTroCap")
                        .HasColumnType("int")
                        .HasColumnName("Phantram_trocap");

                    b.Property<int>("SoTienTroCap")
                        .HasColumnType("int")
                        .HasColumnName("Sotien_trocap");

                    b.HasKey("IdDoiTuongHocBong");

                    b.ToTable("svDoiTuongHocBong");
                });

            modelBuilder.Entity("Core.Data.SvDoiTuongHocPhi", b =>
                {
                    b.Property<int>("IdDoiTuongHocPhi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_doi_tuong_hoc_phi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdDoiTuongHocPhi"));

                    b.Property<string>("DoiTuongHocPhi")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_doi_tuong_hoc_phi");

                    b.HasKey("IdDoiTuongHocPhi");

                    b.ToTable("svDoiTuongHocPhi");
                });

            modelBuilder.Entity("Core.Data.SvGioiTinh", b =>
                {
                    b.Property<int>("IdGioiTinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_gioi_tinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdGioiTinh"));

                    b.Property<string>("GioiTinh")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("Gioi_tinh");

                    b.HasKey("IdGioiTinh");

                    b.ToTable("svGioiTinh");
                });

            modelBuilder.Entity("Core.Data.SvHanhVi", b =>
                {
                    b.Property<int>("IdHanhVi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_hanh_vi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHanhVi"));

                    b.Property<string>("HanhVi")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Hanh_vi");

                    b.Property<string>("MaHanhVi")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_hanh_vi");

                    b.HasKey("IdHanhVi");

                    b.ToTable("svHanhVi");
                });

            modelBuilder.Entity("Core.Data.SvHe", b =>
                {
                    b.Property<int>("IdHe")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHe"));

                    b.Property<string>("HinhThucDaoTao")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Hinh_thuc_dao_tao");

                    b.Property<string>("HinhThucDaoTaoEn")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Hinh_thuc_dao_tao_en");

                    b.Property<string>("MaHe")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_he");

                    b.Property<int?>("QuyChe")
                        .HasColumnType("int")
                        .HasColumnName("Quy_che");

                    b.Property<string>("TenBacDaoTao")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_bac_dao_tao");

                    b.Property<string>("TenBacDaoTaoEn")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_bac_dao_tao_en");

                    b.Property<string>("TenHe")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_he");

                    b.Property<string>("TenHeEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_he_en");

                    b.HasKey("IdHe");

                    b.ToTable("svHe");
                });

            modelBuilder.Entity("Core.Data.SvHinhThucHoc", b =>
                {
                    b.Property<int>("IdHinhThucHoc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_hinh_thuc_hoc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHinhThucHoc"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("MaHinhThucHoc")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_hinh_thuc_hoc");

                    b.Property<string>("TenHinhThucHoc")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_hinh_thuc_hoc");

                    b.HasKey("IdHinhThucHoc");

                    b.ToTable("svHinhThucHoc");
                });

            modelBuilder.Entity("Core.Data.SvHinhThucThi", b =>
                {
                    b.Property<int>("IdHinhThucThi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_hinh_thuc_thi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHinhThucThi"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("ghi_chu");

                    b.Property<string>("HinhThucThi")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ten_hinh_thuc_thi");

                    b.Property<bool>("KhongKiemTraTrungLich")
                        .HasColumnType("bit")
                        .HasColumnName("Khong_kiem_tra_trung_lich");

                    b.Property<string>("MaHinhThucThi")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_hinh_thuc_thi");

                    b.HasKey("IdHinhThucThi");

                    b.ToTable("svHinhThucThi");
                });

            modelBuilder.Entity("Core.Data.SvHuyen", b =>
                {
                    b.Property<string>("IdHuyen")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_huyen");

                    b.Property<string>("IdHuyenCu")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_huyen_cu");

                    b.Property<string>("IdHuyenCu1")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_huyen_cu1");

                    b.Property<string>("IdTinh")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ID_tinh");

                    b.Property<string>("TenHuyen")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_huyen");

                    b.Property<string>("TenHuyenCu")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_huyen_cu");

                    b.Property<string>("TenHuyenEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_huyen_en");

                    b.HasKey("IdHuyen");

                    b.ToTable("svHuyen");
                });

            modelBuilder.Entity("Core.Data.SvKhoa", b =>
                {
                    b.Property<int>("IdKhoa")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_khoa");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdKhoa"));

                    b.Property<string>("MaKhoa")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_khoa");

                    b.Property<string>("TenKhoa")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_khoa");

                    b.Property<string>("TenKhoaEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_khoa_en");

                    b.HasKey("IdKhoa");

                    b.ToTable("svKhoa");
                });

            modelBuilder.Entity("Core.Data.SvKhuVuc", b =>
                {
                    b.Property<int>("IdKhuVuc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_kv");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdKhuVuc"));

                    b.Property<decimal?>("DiemCongKv")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Diem_cong_kv");

                    b.Property<string>("MaKhuVuc")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_kv");

                    b.Property<string>("TenKhuVuc")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_kv");

                    b.HasKey("IdKhuVuc");

                    b.ToTable("svKhuVuc");
                });

            modelBuilder.Entity("Core.Data.SvLoaiChungChi", b =>
                {
                    b.Property<int>("IdChungChi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_chung_chi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdChungChi"));

                    b.Property<int?>("CapDoChungChi")
                        .HasColumnType("int")
                        .HasColumnName("Cap_do_chung_chi");

                    b.Property<float>("IdNhomChungChi")
                        .HasColumnType("real")
                        .HasColumnName("ID_nhom_chung_chi");

                    b.Property<string>("KyHieu")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ky_hieu");

                    b.Property<string>("LoaiChungChi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Loai_chung_chi");

                    b.HasKey("IdChungChi");

                    b.ToTable("svLoaiChungChi");
                });

            modelBuilder.Entity("Core.Data.SvLoaiChungChiDanhSachMon", b =>
                {
                    b.Property<int>("IdChungChi")
                        .HasColumnType("int")
                        .HasColumnName("ID_chung_chi");

                    b.Property<int>("IdDt")
                        .HasColumnType("int")
                        .HasColumnName("ID_dt");

                    b.Property<int>("IdMon")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon");

                    b.HasKey("IdChungChi", "IdDt", "IdMon");

                    b.ToTable("svLoaiChungChiDanhSachMon");
                });

            modelBuilder.Entity("Core.Data.SvLoaiGiayTo", b =>
                {
                    b.Property<int>("IdGiayTo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_giay_to");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdGiayTo"));

                    b.Property<bool>("BatBuoc")
                        .HasColumnType("bit")
                        .HasColumnName("Bat_buoc");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Ghi_chu");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdPhong")
                        .HasColumnType("int")
                        .HasColumnName("ID_phong");

                    b.Property<string>("MaGiayTo")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_giay_to");

                    b.Property<bool>("MacDinh")
                        .HasColumnType("bit")
                        .HasColumnName("Mac_dinh");

                    b.Property<int>("Nhom")
                        .HasColumnType("int")
                        .HasColumnName("Nhom");

                    b.Property<int>("Stt")
                        .HasColumnType("int")
                        .HasColumnName("STT");

                    b.Property<string>("TenGiayTo")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_giay_to");

                    b.Property<bool>("TuyenSinh")
                        .HasColumnType("bit")
                        .HasColumnName("Tuyen_sinh");

                    b.HasKey("IdGiayTo");

                    b.ToTable("svLoaiGiayTo");
                });

            modelBuilder.Entity("Core.Data.SvLoaiKhenThuong", b =>
                {
                    b.Property<int>("IdLoaiKhenThuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_kt");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdLoaiKhenThuong"));

                    b.Property<float>("DiemThuong")
                        .HasColumnType("real")
                        .HasColumnName("Diem_thuong");

                    b.Property<int>("IdCap")
                        .HasColumnType("int")
                        .HasColumnName("Id_cap");

                    b.Property<string>("LoaiKhenThuong")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Loai_khen_thuong");

                    b.HasKey("IdLoaiKhenThuong");

                    b.ToTable("svLoaiKhenThuong");
                });

            modelBuilder.Entity("Core.Data.SvLoaiQuyetDinh", b =>
                {
                    b.Property<int>("IdLoaiQd")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_qd");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdLoaiQd"));

                    b.Property<bool>("ChuyenLop")
                        .HasColumnType("bit")
                        .HasColumnName("Chuyen_lop");

                    b.Property<bool>("ChuyenTruongDi")
                        .HasColumnType("bit")
                        .HasColumnName("Chuyen_truong_di");

                    b.Property<bool>("ChuyentruongDen")
                        .HasColumnType("bit")
                        .HasColumnName("Chuyen_truong_den");

                    b.Property<bool>("HocTiep")
                        .HasColumnType("bit")
                        .HasColumnName("Hoc_tiep");

                    b.Property<string>("MaQd")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_QD");

                    b.Property<bool>("NgungHoc")
                        .HasColumnType("bit")
                        .HasColumnName("Ngung_hoc");

                    b.Property<string>("TenLoaiQd")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_loai_QD");

                    b.Property<bool>("ThoiHoc")
                        .HasColumnType("bit")
                        .HasColumnName("Thoi_hoc");

                    b.Property<bool>("ThoiHocQuyChe")
                        .HasColumnType("bit")
                        .HasColumnName("Thoi_hoc_quy_che");

                    b.Property<bool>("XoaTenkhoiLop")
                        .HasColumnType("bit")
                        .HasColumnName("Xoa_ten_khoi_lop");

                    b.HasKey("IdLoaiQd");

                    b.ToTable("svLoaiQuyetDinh");
                });

            modelBuilder.Entity("Core.Data.SvLoaiRangBuoc", b =>
                {
                    b.Property<int>("LoaiRangBuoc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Loai_rang_buoc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LoaiRangBuoc"));

                    b.Property<string>("TenRangBuoc")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_rang_buoc");

                    b.HasKey("LoaiRangBuoc");

                    b.ToTable("svLoaiRangBuoc");
                });

            modelBuilder.Entity("Core.Data.SvLoaiRenLuyen", b =>
                {
                    b.Property<int>("IdLoaiRenLuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_rl");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdLoaiRenLuyen"));

                    b.Property<int>("Diem")
                        .HasColumnType("int")
                        .HasColumnName("Diem");

                    b.Property<bool>("DiemTru")
                        .HasColumnType("bit")
                        .HasColumnName("Diem_tru");

                    b.Property<bool>("HienThi")
                        .HasColumnType("bit")
                        .HasColumnName("Hien_thi");

                    b.Property<bool>("HocTap")
                        .HasColumnType("bit")
                        .HasColumnName("Hoc_tap");

                    b.Property<int>("IdCapRenLuyen")
                        .HasColumnType("int")
                        .HasColumnName("ID_cap_rl");

                    b.Property<string>("KyHieu")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ky_hieu");

                    b.Property<string>("TenLoai")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_loai");

                    b.Property<bool>("TinhDiem")
                        .HasColumnType("bit")
                        .HasColumnName("Tinh_diem");

                    b.HasKey("IdLoaiRenLuyen");

                    b.ToTable("svLoaiRenLuyen");
                });

            modelBuilder.Entity("Core.Data.SvLoaiThuChi", b =>
                {
                    b.Property<int>("IdThuChi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_thu_chi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdThuChi"));

                    b.Property<bool?>("BaoHiem")
                        .HasColumnType("bit")
                        .HasColumnName("Bao_hiem");

                    b.Property<bool?>("HocLai")
                        .HasColumnType("bit")
                        .HasColumnName("Hoc_lai");

                    b.Property<bool?>("HocPhi")
                        .HasColumnType("bit")
                        .HasColumnName("Hoc_phi");

                    b.Property<bool?>("KhoanThuKtx")
                        .HasColumnType("bit")
                        .HasColumnName("Khoan_thu_ktx");

                    b.Property<bool?>("KhoanThuTienPhong")
                        .HasColumnType("bit")
                        .HasColumnName("Khoan_thu_tien_phong");

                    b.Property<bool?>("KhoanTienCuoc")
                        .HasColumnType("bit")
                        .HasColumnName("Khoan_tien_cuoc");

                    b.Property<bool?>("KinhPhiDt")
                        .HasColumnType("bit")
                        .HasColumnName("Kinh_phi_DT");

                    b.Property<string>("MaThuChi")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ma_thu_chi");

                    b.Property<long?>("SoTien")
                        .HasColumnType("bigint")
                        .HasColumnName("So_tien");

                    b.Property<string>("TenThuChi")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_thu_chi");

                    b.Property<bool?>("ThiLai")
                        .HasColumnType("bit")
                        .HasColumnName("Thi_lai");

                    b.Property<bool?>("ThuChi")
                        .HasColumnType("bit")
                        .HasColumnName("Thu_chi");

                    b.HasKey("IdThuChi");

                    b.ToTable("svLoaiThuChi");
                });

            modelBuilder.Entity("Core.Data.SvLop", b =>
                {
                    b.Property<int>("IdLop")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_lop");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdLop"));

                    b.Property<int?>("CaHoc")
                        .HasColumnType("int")
                        .HasColumnName("Ca_hoc");

                    b.Property<int>("IdChuyenNganh")
                        .HasColumnType("int")
                        .HasColumnName("ID_chuyen_nganh");

                    b.Property<int>("IdDt")
                        .HasColumnType("int")
                        .HasColumnName("ID_dt");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("IdKhoa")
                        .HasColumnType("int")
                        .HasColumnName("ID_khoa");

                    b.Property<int?>("IdPhong")
                        .HasColumnType("int")
                        .HasColumnName("ID_phong");

                    b.Property<int>("KhoaHoc")
                        .HasColumnType("int")
                        .HasColumnName("Khoa_hoc");

                    b.Property<string>("NienKhoa")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("Nien_khoa");

                    b.Property<bool>("RaTruong")
                        .HasColumnType("bit")
                        .HasColumnName("Ra_truong");

                    b.Property<int>("SoSv")
                        .HasColumnType("int")
                        .HasColumnName("So_sv");

                    b.Property<string>("TenLop")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_lop");

                    b.HasKey("IdLop");

                    b.ToTable("svLop");
                });

            modelBuilder.Entity("Core.Data.SvMonHoc", b =>
                {
                    b.Property<int>("IdMonHoc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_mon");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdMonHoc"));

                    b.Property<bool>("ChatLuongCao")
                        .HasColumnType("bit")
                        .HasColumnName("Chat_luong_cao");

                    b.Property<bool>("HocPhanTH")
                        .HasColumnType("bit")
                        .HasColumnName("HP_thuc_hanh");

                    b.Property<int>("IdBoMon")
                        .HasColumnType("int")
                        .HasColumnName("ID_bm");

                    b.Property<int>("IdHeDt")
                        .HasColumnType("int")
                        .HasColumnName("ID_he_dt");

                    b.Property<int>("IdNhomHp")
                        .HasColumnType("int")
                        .HasColumnName("ID_nhom_hp");

                    b.Property<string>("KyHieu")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Ky_hieu");

                    b.Property<string>("KyHieuCu")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ky_hieu_cu");

                    b.Property<bool>("MonChungChi")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_chung_chi");

                    b.Property<bool>("MonNn")
                        .HasColumnType("bit")
                        .HasColumnName("Mon_NN");

                    b.Property<string>("TenMon")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_mon");

                    b.Property<string>("TenTiengAnh")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_tieng_anh");

                    b.Property<string>("TenTiengPhap")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_tieng_phap");

                    b.Property<string>("TenVietTat")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_viet_tat");

                    b.HasKey("IdMonHoc");

                    b.ToTable("svMonHoc");
                });

            modelBuilder.Entity("Core.Data.SvMonHocTuongDuong", b =>
                {
                    b.Property<int>("IdMon1")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon1");

                    b.Property<int>("IdMon")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon");

                    b.Property<int>("IdMonTuongDuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_tuong_duong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdMonTuongDuong"));

                    b.Property<float?>("SoTinChi")
                        .HasColumnType("real")
                        .HasColumnName("So_tin_chi");

                    b.Property<float?>("SoTinChi1")
                        .HasColumnType("real")
                        .HasColumnName("So_tin_chi1");

                    b.HasKey("IdMon1", "IdMon");

                    b.ToTable("svMonHocTuongDuong");
                });

            modelBuilder.Entity("Core.Data.SvMucHuongBhyt", b =>
                {
                    b.Property<int>("IdMucHuongBhyt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_muc_huong_bhyt");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdMucHuongBhyt"));

                    b.Property<string>("DoiTuongMucHuong")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Doi_tuong_muc_huong");

                    b.Property<string>("KyHieu")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ky_hieu");

                    b.Property<int>("MucHuong")
                        .HasColumnType("int")
                        .HasColumnName("Muc_huong");

                    b.HasKey("IdMucHuongBhyt");

                    b.ToTable("svMucHuongBhyt");
                });

            modelBuilder.Entity("Core.Data.SvNganh", b =>
                {
                    b.Property<int>("IdNganh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nganh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNganh"));

                    b.Property<string>("MaNganh")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_nganh");

                    b.Property<bool>("SuPham")
                        .HasColumnType("bit")
                        .HasColumnName("Su_pham");

                    b.Property<string>("TenLoaiVanBang")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_loai_van_bang");

                    b.Property<string>("TenNganh")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_nganh");

                    b.Property<string>("TenNganhEn")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_nganh_en");

                    b.HasKey("IdNganh");

                    b.ToTable("svNganh");
                });

            modelBuilder.Entity("Core.Data.SvNhomChungChi", b =>
                {
                    b.Property<int>("IdNhomChungChi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nhom_chung_chi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNhomChungChi"));

                    b.Property<string>("KyHieuNhom")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ky_hieu_nhom");

                    b.Property<string>("NhomChungChi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Nhom_chung_chi");

                    b.HasKey("IdNhomChungChi");

                    b.ToTable("svNhomChungChi");
                });

            modelBuilder.Entity("Core.Data.SvNhomDoiTuong", b =>
                {
                    b.Property<int>("IdNhomDoiTuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nhom_doi_tuong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNhomDoiTuong"));

                    b.Property<string>("MaNhom")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_nhom");

                    b.Property<string>("TenNhom")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_nhom");

                    b.Property<string>("TenNhomEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_nhom_en");

                    b.HasKey("IdNhomDoiTuong");

                    b.ToTable("svNhomDoiTuong");
                });

            modelBuilder.Entity("Core.Data.SvNoiThucTap", b =>
                {
                    b.Property<int>("IdNoiThucTap")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_noi_thuc_tap");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNoiThucTap"));

                    b.Property<string>("DiaChithucTap")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)")
                        .HasColumnName("Dia_chi_thuc_tap");

                    b.Property<string>("MaNoiThucTap")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_noi_thuc_tap");

                    b.Property<string>("NoiThucTap")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("noi_thuc_tap");

                    b.HasKey("IdNoiThucTap");

                    b.ToTable("svNoiThucTap");
                });

            modelBuilder.Entity("Core.Data.SvPhuong", b =>
                {
                    b.Property<int>("IdPhuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdPhuong"));

                    b.Property<string>("TenPhuong")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ten_phuong");

                    b.HasKey("IdPhuong");

                    b.ToTable("svPhuong");
                });

            modelBuilder.Entity("Core.Data.SvPhuongAn", b =>
                {
                    b.Property<int>("IdPhuongAn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_an");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdPhuongAn"));

                    b.Property<string>("MaPhuongAn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_phuong_an");

                    b.Property<string>("NoiDung")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Noi_dung");

                    b.Property<string>("TenPhuongAn")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Ten_phuong_an");

                    b.HasKey("IdPhuongAn");

                    b.ToTable("svPhuongAn");
                });

            modelBuilder.Entity("Core.Data.SvPhuongThucDong", b =>
                {
                    b.Property<int>("IdPhuongThucDong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_phuong_thuc_dong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdPhuongThucDong"));

                    b.Property<string>("GhiChu")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("PhuongThucDong")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Phuong_thuc_dong");

                    b.HasKey("IdPhuongThucDong");

                    b.ToTable("svPhuongThucDong");
                });

            modelBuilder.Entity("Core.Data.SvQuocTich", b =>
                {
                    b.Property<int>("IdQuocTich")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_quoc_tich");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdQuocTich"));

                    b.Property<string>("MaQuocTich")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_quoc_tich");

                    b.Property<string>("QuocTich")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Quoc_tich");

                    b.HasKey("IdQuocTich");

                    b.ToTable("svQuocTich");
                });

            modelBuilder.Entity("Core.Data.SvThamSoQuyChe", b =>
                {
                    b.Property<int>("IdThamSoQC")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_tham_so_qc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdThamSoQC"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<DateTime?>("DateModify")
                        .HasColumnType("datetime2")
                        .HasColumnName("DateModify");

                    b.Property<float>("GiaTri")
                        .HasColumnType("real")
                        .HasColumnName("Gia_tri");

                    b.Property<string>("MaThamSo")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_tham_so");

                    b.Property<string>("NhomQuyChe")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Nhom_quy_che");

                    b.Property<int>("QuyChe")
                        .HasColumnType("int")
                        .HasColumnName("Quy_che");

                    b.Property<string>("TenThamSo")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Ten_tham_so");

                    b.Property<string>("UserName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("UserName");

                    b.HasKey("IdThamSoQC");

                    b.ToTable("svThamSoQuyChe");
                });

            modelBuilder.Entity("Core.Data.SvThanhPhanMon", b =>
                {
                    b.Property<int>("IdThanhPhan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_thanh_phan");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdThanhPhan"));

                    b.Property<int?>("ChonMacDinh")
                        .HasColumnType("int")
                        .HasColumnName("Chon_mac_dinh");

                    b.Property<int?>("ChuyenCan")
                        .HasColumnType("int")
                        .HasColumnName("Chuyen_can");

                    b.Property<string>("KyHieu")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ky_hieu");

                    b.Property<string>("KyHieuNhom")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ky_hieu_nhom");

                    b.Property<int?>("NhomThanhPhan")
                        .HasColumnType("int")
                        .HasColumnName("Nhom_thanh_phan");

                    b.Property<int>("Stt")
                        .HasColumnType("int")
                        .HasColumnName("STT");

                    b.Property<string>("TenThanhPhan")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_thanh_phan");

                    b.Property<bool>("ThucHanh")
                        .HasColumnType("bit")
                        .HasColumnName("Thuc_hanh");

                    b.Property<int>("TyLe")
                        .HasColumnType("int")
                        .HasColumnName("Ty_le");

                    b.Property<int?>("TyLeNhom")
                        .HasColumnType("int")
                        .HasColumnName("Ty_le_nhom");

                    b.HasKey("IdThanhPhan");

                    b.ToTable("svThanhPhanMon");
                });

            modelBuilder.Entity("Core.Data.SvThanhPhanMonTheoHe", b =>
                {
                    b.Property<int>("IdThanhPhan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_thanh_phan");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdThanhPhan"));

                    b.Property<bool>("ChonMacDinh")
                        .HasColumnType("bit")
                        .HasColumnName("Chon_mac_dinh");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<int>("NhomThanhPhan")
                        .HasColumnType("int")
                        .HasColumnName("Nhom_thanh_phan");

                    b.Property<int>("Stt")
                        .HasColumnType("int")
                        .HasColumnName("STT");

                    b.Property<int>("TyLe")
                        .HasColumnType("int")
                        .HasColumnName("Ty_le");

                    b.Property<int>("TyLeNhom")
                        .HasColumnType("int")
                        .HasColumnName("Ty_le_nhom");

                    b.HasKey("IdThanhPhan");

                    b.ToTable("svThanhPhanMonTheoHe");
                });

            modelBuilder.Entity("Core.Data.SvTinh", b =>
                {
                    b.Property<string>("IdTinh")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("ID_tinh");

                    b.Property<string>("TenTinh")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_tinh");

                    b.Property<string>("TenTinhEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_tinh_en");

                    b.HasKey("IdTinh");

                    b.ToTable("svTinh");
                });

            modelBuilder.Entity("Core.Data.SvTonGiao", b =>
                {
                    b.Property<int>("IdTonGiao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_ton_giao");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTonGiao"));

                    b.Property<string>("MaTonGiao")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_ton_giao");

                    b.Property<string>("TonGiao")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ton_giao");

                    b.Property<string>("TonGiaoEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ton_giao_en");

                    b.HasKey("IdTonGiao");

                    b.ToTable("svTonGiao");
                });

            modelBuilder.Entity("Core.Data.SvVung", b =>
                {
                    b.Property<int>("IdVung")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_vung");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdVung"));

                    b.Property<string>("GhiChu")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ghi_chu");

                    b.Property<string>("KyHieu")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ky_hieu");

                    b.Property<string>("TenVung")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ten_vung");

                    b.HasKey("IdVung");

                    b.ToTable("svVung");
                });

            modelBuilder.Entity("Core.Data.SvXa", b =>
                {
                    b.Property<string>("IdXa")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)")
                        .HasColumnName("ID_xa");

                    b.Property<string>("IdHuyen")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)")
                        .HasColumnName("ID_huyen");

                    b.Property<string>("TenXa")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("Ten_xa");

                    b.Property<string>("TenXaEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_xa_en");

                    b.HasKey("IdXa");

                    b.ToTable("svXa");
                });

            modelBuilder.Entity("Core.Data.SvXepHangHocLuc", b =>
                {
                    b.Property<int>("IdXepHang")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_hang");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepHang"));

                    b.Property<float>("DenDiem")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<float>("TuDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem");

                    b.Property<string>("XepHang")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Xep_hang");

                    b.Property<string>("XepHangEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_hang_en");

                    b.HasKey("IdXepHang");

                    b.ToTable("svXepHangHocLuc");
                });

            modelBuilder.Entity("Core.Data.SvXepHangNamDaoTao", b =>
                {
                    b.Property<int>("IdXepHang")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_hang");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepHang"));

                    b.Property<int>("DenTinChi")
                        .HasColumnType("int")
                        .HasColumnName("Den_tin_chi");

                    b.Property<int>("NamThu")
                        .HasColumnType("int")
                        .HasColumnName("Nam_thu");

                    b.Property<string>("NamThuEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nam_thu_en");

                    b.Property<int>("TuTinChi")
                        .HasColumnType("int")
                        .HasColumnName("Tu_tin_chi");

                    b.HasKey("IdXepHang");

                    b.ToTable("svXepHangNamDaoTao");
                });

            modelBuilder.Entity("Core.Data.SvXepHangTotNghiep", b =>
                {
                    b.Property<int>("IdXepHang")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_hang");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepHang"));

                    b.Property<float>("DenDiem")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem");

                    b.Property<float>("DenDiemThang10")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem_thang10");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<string>("MaXepHang")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_xep_hang");

                    b.Property<float>("TuDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem");

                    b.Property<float>("TuDiemThang10")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem_thang10");

                    b.Property<string>("XepHang")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Xep_hang");

                    b.Property<string>("XepHangEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_hang_en");

                    b.HasKey("IdXepHang");

                    b.ToTable("svXepHangTotNghiep");
                });

            modelBuilder.Entity("Core.Data.SvXepHangTotNghiepThangDiem10", b =>
                {
                    b.Property<int>("IdXepHang")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_Xep_hang");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepHang"));

                    b.Property<float>("DenDiem")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<string>("MaXepHang")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ma_xep_hang");

                    b.Property<float>("TuDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem");

                    b.Property<string>("XepHang")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_hang");

                    b.Property<string>("XepHangEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_hang_en");

                    b.HasKey("IdXepHang");

                    b.ToTable("svXepHangTotNghiepThangDiem10");
                });

            modelBuilder.Entity("Core.Data.SvXepLoaiChungChi", b =>
                {
                    b.Property<int>("IdXepHang")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_hang");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepHang"));

                    b.Property<float>("DenDiem")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem");

                    b.Property<float>("TuDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem");

                    b.Property<string>("XepHang")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Xep_hang");

                    b.Property<string>("XepHangEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_hang_en");

                    b.HasKey("IdXepHang");

                    b.ToTable("svXepLoaiChungChi");
                });

            modelBuilder.Entity("Core.Data.SvXepLoaiHocBong", b =>
                {
                    b.Property<int>("IdXepLoaiHb")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai_hb");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepLoaiHb"));

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<string>("MaXepLoai")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_xep_loai");

                    b.Property<float>("SoTien")
                        .HasColumnType("real")
                        .HasColumnName("So_tien");

                    b.Property<string>("TenXepLoai")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_xep_loai");

                    b.Property<float>("TuDiemHt")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem_ht");

                    b.Property<float>("TuDiemHt4")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem_ht4");

                    b.Property<float>("TuDiemRl")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem_rl");

                    b.HasKey("IdXepLoaiHb");

                    b.ToTable("svXepLoaiHocBong");
                });

            modelBuilder.Entity("Core.Data.SvXepLoaiHocTap", b =>
                {
                    b.Property<int>("IdXepLoai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepLoai"));

                    b.Property<float>("DenDiem")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<string>("MaXepLoai")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_xep_loai");

                    b.Property<float>("TuDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem");

                    b.Property<string>("XepLoai")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_loai");

                    b.Property<string>("XepLoaiEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_loai_en");

                    b.HasKey("IdXepLoai");

                    b.ToTable("svXepLoaiHocTap");
                });

            modelBuilder.Entity("Core.Data.SvXepLoaiHocTapThangDiem10", b =>
                {
                    b.Property<int>("IdXepLoai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepLoai"));

                    b.Property<float>("DenDiem")
                        .HasColumnType("real")
                        .HasColumnName("Den_diem");

                    b.Property<int>("IdHe")
                        .HasColumnType("int")
                        .HasColumnName("ID_he");

                    b.Property<string>("MaXepLoai")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_xep_loai");

                    b.Property<float>("TuDiem")
                        .HasColumnType("real")
                        .HasColumnName("Tu_diem");

                    b.Property<string>("XepLoai")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_loai");

                    b.Property<string>("XepLoaiEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Xep_loai_en");

                    b.HasKey("IdXepLoai");

                    b.ToTable("svXepLoaiHocTapThangDiem10");
                });

            modelBuilder.Entity("Core.Data.SvXepLoaiRenLuyen", b =>
                {
                    b.Property<int>("IdXepLoai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xep_loai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXepLoai"));

                    b.Property<int>("DenDiem")
                        .HasColumnType("int")
                        .HasColumnName("Den_diem");

                    b.Property<float>("HeSo")
                        .HasColumnType("real")
                        .HasColumnName("He_so");

                    b.Property<int>("TuDiem")
                        .HasColumnType("int")
                        .HasColumnName("Tu_diem");

                    b.Property<string>("XepLoai")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("xep_loai");

                    b.Property<string>("XepLoaiEn")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("xep_loai_en");

                    b.HasKey("IdXepLoai");

                    b.ToTable("svXepLoaiRenLuyen");
                });

            modelBuilder.Entity("Core.Data.SvXuLy", b =>
                {
                    b.Property<int>("IdXuLy")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_xu_ly");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdXuLy"));

                    b.Property<float>("DiemPhat")
                        .HasColumnType("real")
                        .HasColumnName("Diem_phat");

                    b.Property<int>("IdCap")
                        .HasColumnType("int")
                        .HasColumnName("ID_cap");

                    b.Property<int>("MucXuLy")
                        .HasColumnType("int")
                        .HasColumnName("Muc_xu_ly");

                    b.Property<int>("SoThang")
                        .HasColumnType("int")
                        .HasColumnName("So_thang");

                    b.Property<string>("XuLy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("xu_ly");

                    b.HasKey("IdXuLy");

                    b.ToTable("svXuLy");
                });

            modelBuilder.Entity("Core.Data.SystemApplication", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("code");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date")
                        .HasColumnOrder(103);

                    b.Property<int?>("CreatedUserId")
                        .HasColumnType("int")
                        .HasColumnName("created_user_id")
                        .HasColumnOrder(104);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description")
                        .HasColumnOrder(102);

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active")
                        .HasColumnOrder(101);

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("modified_date")
                        .HasColumnOrder(105);

                    b.Property<int?>("ModifiedUserId")
                        .HasColumnType("int")
                        .HasColumnName("modified_user_id")
                        .HasColumnOrder(106);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order")
                        .HasColumnOrder(100);

                    b.HasKey("Id");

                    b.ToTable("system_appliation");
                });

            modelBuilder.Entity("Core.Data.SystemLogEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasColumnOrder(1);

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ActionCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("action_code");

                    b.Property<string>("ActionName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("action_name");

                    b.Property<string>("Browser")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("browser");

                    b.Property<string>("ClientIP")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("client_ip");

                    b.Property<string>("ClientInfo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("client_info");

                    b.Property<string>("CorrelationId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("correlation_id");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_date");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("DeviceId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("device_id");

                    b.Property<string>("LocationJson")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("location_json");

                    b.Property<string>("MetaDataJson")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("meta_data_json");

                    b.Property<string>("ObjectCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("object_code");

                    b.Property<string>("ObjectId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("object_id");

                    b.Property<string>("Os")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("os");

                    b.Property<string>("RequestMethod")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("request_method");

                    b.Property<string>("RequestPath")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("request_path");

                    b.Property<long>("TimeExecution")
                        .HasColumnType("bigint")
                        .HasColumnName("time_execution");

                    b.Property<string>("TraceId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("trace_id");

                    b.Property<string>("UserAgent")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("user_agent");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("user_id");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("user_name");

                    b.HasKey("Id");

                    b.ToTable("log_action");
                });

            modelBuilder.Entity("Core.Data.TKBGiaoVien", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_cb");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("BmChinhID")
                        .HasColumnType("int")
                        .HasColumnName("ID_bm_chinh");

                    b.Property<string>("CMTND")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CMTND");

                    b.Property<string>("ChiNhanhNganHang")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Chi_nhanh_ngan_hang");

                    b.Property<string>("ChuTaiKhoan")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Chu_tai_khoan");

                    b.Property<int>("ChucDanhID")
                        .HasColumnType("int")
                        .HasColumnName("ID_chuc_danh");

                    b.Property<int>("ChucVuID")
                        .HasColumnType("int")
                        .HasColumnName("ID_chuc_vu");

                    b.Property<string>("ChuyenMonDaoTao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Chuyen_mon_dao_tao");

                    b.Property<string>("ChuyenNganhGiangDay")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Chuyen_nganh_giang_day");

                    b.Property<bool?>("CoHuu")
                        .HasColumnType("bit")
                        .HasColumnName("Co_huu");

                    b.Property<bool?>("DaNopHD")
                        .HasColumnType("bit")
                        .HasColumnName("Da_Nop_HD");

                    b.Property<int>("DanTocID")
                        .HasColumnType("int")
                        .HasColumnName("ID_dan_toc");

                    b.Property<string>("DiaChiLienHe")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Dia_chi_lien_he");

                    b.Property<string>("DonViCongTac")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Don_vi_cong_tac");

                    b.Property<int>("DonViCongTacID")
                        .HasColumnType("int")
                        .HasColumnName("ID_don_vi_cong_tac");

                    b.Property<int?>("DonViQuanLyID")
                        .HasColumnType("int")
                        .HasColumnName("ID_don_vi_quan_ly");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Email");

                    b.Property<int?>("GioiTinhID")
                        .HasColumnType("int")
                        .HasColumnName("ID_gioi_tinh");

                    b.Property<float>("HeSoLuong")
                        .HasColumnType("real")
                        .HasColumnName("He_so_luong");

                    b.Property<string>("HoTen")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ho_ten");

                    b.Property<int>("HocHamID")
                        .HasColumnType("int")
                        .HasColumnName("ID_hoc_ham");

                    b.Property<int>("HocViID")
                        .HasColumnType("int")
                        .HasColumnName("ID_hoc_vi");

                    b.Property<int>("KhoaID")
                        .HasColumnType("int")
                        .HasColumnName("ID_khoa");

                    b.Property<bool?>("KhongHoatDong")
                        .HasColumnType("bit")
                        .HasColumnName("Not_active");

                    b.Property<bool?>("KiemGiang")
                        .HasColumnType("bit")
                        .HasColumnName("Kiem_giang");

                    b.Property<string>("MaCB")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ma_cb");

                    b.Property<string>("MaSoThue")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ma_so_thue");

                    b.Property<string>("MatKhau")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Mat_khau");

                    b.Property<DateTime?>("NgayCapCMND")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_cap_CMND");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ngay_sinh");

                    b.Property<string>("NoiCapCMND")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Noi_cap_CMND");

                    b.Property<string>("PhanLoai")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Phan_loai");

                    b.Property<int>("QuocTichID")
                        .HasColumnType("int")
                        .HasColumnName("ID_quoc_tich");

                    b.Property<string>("SoDienThoai")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("So_dien_thoai");

                    b.Property<string>("SoSoBaoHiem")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("So_so_bao_hiem");

                    b.Property<string>("SoTaiKhoan")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("So_tai_khoan");

                    b.Property<string>("Ten")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ten");

                    b.Property<string>("TenDangNhap")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ten_dang_nhap");

                    b.Property<string>("TenNganHang")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ten_ngan_hang");

                    b.Property<string>("TenTinhNganHang")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Ten_tinh_ngan_hang");

                    b.Property<string>("ThamNienGiangDay")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Tham_nien_giang_day");

                    b.Property<bool>("ThinhGiang")
                        .HasColumnType("bit")
                        .HasColumnName("Thinh_giang");

                    b.Property<int>("TonGiaoID")
                        .HasColumnType("int")
                        .HasColumnName("ID_ton_giao");

                    b.HasKey("Id");

                    b.ToTable("tkbGiaoVien");
                });

            modelBuilder.Entity("Core.Data.TkbBacDaoTao", b =>
                {
                    b.Property<int>("IdBacDaoTao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_bac_dao_tao");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdBacDaoTao"));

                    b.Property<string>("BacDaoTao")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Bac_dao_tao");

                    b.Property<string>("MaBacDaoTao")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_bac_dao_tao");

                    b.HasKey("IdBacDaoTao");

                    b.ToTable("tkbBacDaoTao");
                });

            modelBuilder.Entity("Core.Data.TkbBoMon", b =>
                {
                    b.Property<int>("IdBoMon")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_bm");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdBoMon"));

                    b.Property<string>("BoMon")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Bo_mon");

                    b.Property<string>("MaBoMon")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_bo_mon");

                    b.Property<int?>("SoNhom")
                        .HasColumnType("int")
                        .HasColumnName("So_nhom");

                    b.HasKey("IdBoMon");

                    b.ToTable("tkbBoMon");
                });

            modelBuilder.Entity("Core.Data.TkbChucDanh", b =>
                {
                    b.Property<int>("IdChucDanh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_chuc_danh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdChucDanh"));

                    b.Property<string>("ChucDanh")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Chuc_danh");

                    b.Property<string>("MaChucDanh")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_chuc_danh");

                    b.HasKey("IdChucDanh");

                    b.ToTable("tkbChucDanh");
                });

            modelBuilder.Entity("Core.Data.TkbChucVu", b =>
                {
                    b.Property<int>("IdChucVu")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_chuc_vu");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdChucVu"));

                    b.Property<string>("ChucVu")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("chuc_vu");

                    b.Property<string>("MaChucVu")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_chuc_vu");

                    b.HasKey("IdChucVu");

                    b.ToTable("tkbChucVu");
                });

            modelBuilder.Entity("Core.Data.TkbCoSoDaoTao", b =>
                {
                    b.Property<int>("IdCoSo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_co_so");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdCoSo"));

                    b.Property<bool>("DayNgoaiTruong")
                        .HasColumnType("bit")
                        .HasColumnName("Day_ngoai_truong");

                    b.Property<bool>("GdCongViec")
                        .HasColumnType("bit")
                        .HasColumnName("gdCongViec");

                    b.Property<string>("MaCoSo")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Ma_co_so");

                    b.Property<string>("TenCoSo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ten_co_so");

                    b.HasKey("IdCoSo");

                    b.ToTable("tkbCoSoDaoTao");
                });

            modelBuilder.Entity("Core.Data.TkbGiaoAn", b =>
                {
                    b.Property<int>("IdGiaoAn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_giao_an");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdGiaoAn"));

                    b.Property<int>("IdCb")
                        .HasColumnType("int")
                        .HasColumnName("ID_cb");

                    b.Property<int>("IdLop")
                        .HasColumnType("int")
                        .HasColumnName("ID_lop");

                    b.Property<int>("IdMon")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon");

                    b.Property<string>("MoTa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Mo_ta");

                    b.Property<string>("TieuDe")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Tieu_de");

                    b.Property<string>("UrlGiaoAn")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("URL_giao_an");

                    b.HasKey("IdGiaoAn");

                    b.ToTable("tkbGiaoAn");
                });

            modelBuilder.Entity("Core.Data.TkbHocHam", b =>
                {
                    b.Property<int>("IdHocHam")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_hoc_ham");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHocHam"));

                    b.Property<string>("HocHam")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Hoc_ham");

                    b.Property<string>("MaHocHam")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_hoc_ham");

                    b.HasKey("IdHocHam");

                    b.ToTable("tkbHocHam");
                });

            modelBuilder.Entity("Core.Data.TkbHocKyDangKy", b =>
                {
                    b.Property<int>("KyDangKy")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Ky_dang_ky");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("KyDangKy"));

                    b.Property<bool>("ChonDangKy")
                        .HasColumnType("bit")
                        .HasColumnName("Chon_dang_ky");

                    b.Property<DateTime>("DenNgay")
                        .HasColumnType("datetime2")
                        .HasColumnName("Den_ngay");

                    b.Property<int>("Dot")
                        .HasColumnType("int")
                        .HasColumnName("Dot");

                    b.Property<int>("HocKy")
                        .HasColumnType("int")
                        .HasColumnName("Hoc_ky");

                    b.Property<bool>("KhoaTkb")
                        .HasColumnType("bit")
                        .HasColumnName("Khoa_TKB");

                    b.Property<string>("NamHoc")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Nam_hoc");

                    b.Property<DateTime>("TuNgay")
                        .HasColumnType("datetime2")
                        .HasColumnName("Tu_ngay");

                    b.HasKey("KyDangKy");

                    b.ToTable("tkbHocKyDangKy");
                });

            modelBuilder.Entity("Core.Data.TkbHocVi", b =>
                {
                    b.Property<int>("IdHocVi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_hoc_vi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdHocVi"));

                    b.Property<decimal>("HeSoBuKhoaHoc")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("He_so_bu_khoa_hoc");

                    b.Property<string>("HocVi")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Hoc_vi");

                    b.Property<string>("MaHocVi")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_hoc_vi");

                    b.HasKey("IdHocVi");

                    b.ToTable("tkbHocVi");
                });

            modelBuilder.Entity("Core.Data.TkbLoaiPhong", b =>
                {
                    b.Property<int>("IdLoaiPhong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_phong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdLoaiPhong"));

                    b.Property<string>("MaLoaiPhong")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_loai");

                    b.Property<string>("TenLoaiPhong")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_loai_phong");

                    b.Property<bool>("ThucHanh")
                        .HasColumnType("bit")
                        .HasColumnName("Thuc_hanh");

                    b.HasKey("IdLoaiPhong");

                    b.ToTable("tkbLoaiPhong");
                });

            modelBuilder.Entity("Core.Data.TkbLopTinChi", b =>
                {
                    b.Property<int>("IdLopTc")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_lop_tc");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdLopTc"));

                    b.Property<bool>("Bt")
                        .HasColumnType("bit")
                        .HasColumnName("BT");

                    b.Property<int>("CaHoc")
                        .HasColumnType("int")
                        .HasColumnName("Ca_hoc");

                    b.Property<DateTime>("DenNgay")
                        .HasColumnType("datetime")
                        .HasColumnName("Den_ngay");

                    b.Property<int>("DonGiaNopSv")
                        .HasColumnType("int")
                        .HasColumnName("Don_gia_nop_sv");

                    b.Property<int>("DonGiaTiet")
                        .HasColumnType("int")
                        .HasColumnName("Don_gia_tiet");

                    b.Property<bool>("HuyLop")
                        .HasColumnType("bit")
                        .HasColumnName("Huy_lop");

                    b.Property<int>("IdCb")
                        .HasColumnType("int")
                        .HasColumnName("ID_cb");

                    b.Property<int>("IdLopLt")
                        .HasColumnType("int")
                        .HasColumnName("ID_lop_lt");

                    b.Property<int>("IdMonTc")
                        .HasColumnType("int")
                        .HasColumnName("ID_mon_tc");

                    b.Property<int>("IdPhong")
                        .HasColumnType("int")
                        .HasColumnName("ID_phong");

                    b.Property<string>("LyDo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Ly_do");

                    b.Property<DateTime?>("NgayThi")
                        .HasColumnType("datetime")
                        .HasColumnName("Ngay_thi");

                    b.Property<string>("NhomDangKy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Nhom_dang_ky");

                    b.Property<int>("SoSvMax")
                        .HasColumnType("int")
                        .HasColumnName("So_sv_max");

                    b.Property<int>("SoSvMin")
                        .HasColumnType("int")
                        .HasColumnName("So_sv_min");

                    b.Property<int>("SoTienLopTinChi")
                        .HasColumnType("int")
                        .HasColumnName("So_tien_lop_tin_chi");

                    b.Property<int>("SoTietTuan")
                        .HasColumnType("int")
                        .HasColumnName("So_tiet_tuan");

                    b.Property<int>("SttLop")
                        .HasColumnType("int")
                        .HasColumnName("STT_lop");

                    b.Property<DateTime>("TuNgay")
                        .HasColumnType("datetime")
                        .HasColumnName("Tu_ngay");

                    b.HasKey("IdLopTc");

                    b.ToTable("tkbLopTinChi", (string)null);
                });

            modelBuilder.Entity("Core.Data.TkbPhongHoc", b =>
                {
                    b.Property<int>("IdPhong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_phong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdPhong"));

                    b.Property<bool?>("AmThanh")
                        .HasColumnType("bit")
                        .HasColumnName("Am_thanh");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Ghi_chu");

                    b.Property<int?>("IdCoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_co_so");

                    b.Property<int?>("IdKhoa")
                        .HasColumnType("int")
                        .HasColumnName("ID_khoa");

                    b.Property<int?>("IdLoaiPhong")
                        .HasColumnType("int")
                        .HasColumnName("ID_loai_phong");

                    b.Property<int?>("IdNha")
                        .HasColumnType("int")
                        .HasColumnName("ID_nha");

                    b.Property<int?>("IdTang")
                        .HasColumnType("int")
                        .HasColumnName("ID_tang");

                    b.Property<int>("KhongToChucThi")
                        .HasColumnType("int")
                        .HasColumnName("Khong_ToChucThi");

                    b.Property<string>("LoaiPhong")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Loai_phong");

                    b.Property<bool?>("MayChieu")
                        .HasColumnType("bit")
                        .HasColumnName("May_chieu");

                    b.Property<bool?>("MayTinh")
                        .HasColumnType("bit")
                        .HasColumnName("May_tinh");

                    b.Property<int?>("SoBan")
                        .HasColumnType("int")
                        .HasColumnName("So_ban");

                    b.Property<string>("SoPhong")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("So_phong");

                    b.Property<int?>("SoSv")
                        .HasColumnType("int")
                        .HasColumnName("So_sv");

                    b.Property<int?>("SoSvMotBan")
                        .HasColumnType("int")
                        .HasColumnName("So_sv_mot_ban");

                    b.Property<int?>("SucChua")
                        .HasColumnType("int")
                        .HasColumnName("Suc_chua");

                    b.Property<int?>("SucChuaThi")
                        .HasColumnType("int")
                        .HasColumnName("Suc_chua_thi");

                    b.Property<string>("ThietBi")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("Thiet_bi");

                    b.Property<bool?>("TiVi")
                        .HasColumnType("bit")
                        .HasColumnName("Tivi");

                    b.Property<bool?>("TrungPhong")
                        .HasColumnType("bit")
                        .HasColumnName("Trung_phong");

                    b.HasKey("IdPhong");

                    b.ToTable("tkbPhongHoc");
                });

            modelBuilder.Entity("Core.Data.TkbTang", b =>
                {
                    b.Property<int>("IdTang")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_TANG");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdTang"));

                    b.Property<string>("MaTang")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Ma_tang");

                    b.Property<string>("TenTang")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_tang");

                    b.HasKey("IdTang");

                    b.ToTable("tkbTang");
                });

            modelBuilder.Entity("Core.Data.TkbToaNha", b =>
                {
                    b.Property<int>("IdNha")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID_nha");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IdNha"));

                    b.Property<int>("IdCoSo")
                        .HasColumnType("int")
                        .HasColumnName("ID_co_so");

                    b.Property<string>("MaNha")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Ma_nha");

                    b.Property<string>("TenNha")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Ten_nha");

                    b.HasKey("IdNha");

                    b.ToTable("tkbToaNha");
                });

            modelBuilder.Entity("Core.Data.UserMapRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("role_id");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.ToTable("sys_user_map_role");
                });
#pragma warning restore 612, 618
        }
    }
}
