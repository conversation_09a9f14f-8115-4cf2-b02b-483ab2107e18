<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Scripts\**" />
    <EmbeddedResource Remove="Scripts\**" />
    <None Remove="Scripts\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Systems\Context\System\ApiCatalog.cs" />
    <Compile Remove="Systems\Context\System\User.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Mvc" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.2" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Core.Shared\Core.Shared.csproj" />
  </ItemGroup>
</Project>