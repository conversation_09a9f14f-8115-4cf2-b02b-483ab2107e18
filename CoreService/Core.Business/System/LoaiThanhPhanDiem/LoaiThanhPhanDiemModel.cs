using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LoaiThanhPhanDiemSelectItemModel
    {
        public int IdThanhPhan { get; set; }
        public string KyHieu { get; set; }
        public string TenThanhPhan { get; set; }
        public int TyLe { get; set; }
    }

    public class LoaiThanhPhanDiemBaseModel
    {
        public int IdThanhPhan { get; set; }
        public int Stt { get; set; }
        public string KyHieu { get; set; }
        public string TenThanhPhan { get; set; }
        public int TyLe { get; set; }
        public int? ChonMacDinh { get; set; }
        public int? ChuyenCan { get; set; }
        public int? NhomThanhPhan { get; set; }
        public int? TyLeNhom { get; set; }
        public string KyHieuNhom { get; set; }
        public bool ThucHanh { get; set; }
    }


    public class LoaiThanhPhanDiemModel : LoaiThanhPhanDiemBaseModel
    {
      
    }

    public class LoaiThanhPhanDiemFilterModel : BaseQueryFilterModel
    {
        public LoaiThanhPhanDiemFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdThanhPhan";
        }
    }

    public class CreateLoaiThanhPhanDiemModel
    {
        public int Stt { get; set; }
        [MaxLength(20, ErrorMessage = "LoaiThanhPhanDiem.KyHieu.MaxLength(20)")]
        [Required(ErrorMessage = "LoaiThanhPhanDiem.KyHieu.NotRequire")]
        public string KyHieu { get; set; }

        [MaxLength(50, ErrorMessage = "LoaiThanhPhanDiem.TenThanhPhan.MaxLength(50)")]
        [Required(ErrorMessage = "LoaiThanhPhanDiem.TenThanhPhan.NotRequire")]
        public string TenThanhPhan { get; set; }

        [Required(ErrorMessage = "LoaiThanhPhanDiem.TyLe.NotRequire")]
        public int TyLe { get; set; }

        public int ChonMacDinh { get; set; }

        public int ChuyenCan { get; set; }

        public int NhomThanhPhan { get; set; }

        public int TyLeNhom { get; set; }

        [MaxLength(10, ErrorMessage = "LoaiThanhPhanDiem.KyHieuNhom.MaxLength(10)")]
        public string KyHieuNhom { get; set; }

        [Required(ErrorMessage = "LoaiThanhPhanDiem.ThucHanh.NotRequire")]
        public bool ThucHanh { get; set; }

    }

    public class CreateManyLoaiThanhPhanDiemModel
    {
        public List<CreateLoaiThanhPhanDiemModel> listLoaiThanhPhanDiemModels { get; set; }
    }

    public class UpdateLoaiThanhPhanDiemModel : CreateLoaiThanhPhanDiemModel
    {
        public void UpdateEntity(SvThanhPhanMon input)
        {
            input.KyHieu = KyHieu;
            input.TenThanhPhan = TenThanhPhan;
            input.TyLe = TyLe;
            input.ChonMacDinh = ChonMacDinh;
            input.ChuyenCan = ChuyenCan;
            input.NhomThanhPhan = NhomThanhPhan;
            input.TyLeNhom = TyLeNhom;
            input.KyHieuNhom = KyHieuNhom;
            input.ThucHanh = ThucHanh;

        }
    }
}
