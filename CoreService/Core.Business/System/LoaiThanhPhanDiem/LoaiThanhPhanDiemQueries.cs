using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;


namespace Core.Business
{
    public class GetComboboxLoaiThanhPhanDiemQuery : IRequest<List<LoaiThanhPhanDiemSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Lo<PERSON>i điểm thành phần cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiThanhPhanDiemQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiThanhPhanDiemQuery, List<LoaiThanhPhanDiemSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiThanhPhanDiemSelectItemModel>> Handle(GetComboboxLoaiThanhPhanDiemQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiThanhPhanDiemConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvThanhPhanMons.OrderBy(x => x.TenThanhPhan)
                                select new LoaiThanhPhanDiemSelectItemModel()
                                {
                                    IdThanhPhan = dt.IdThanhPhan,
                                    KyHieu = dt.KyHieu,
                                    TenThanhPhan = dt.TenThanhPhan,
                                    TyLe = dt.TyLe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenThanhPhan.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLoaiThanhPhanDiemQuery : IRequest<PaginationList<LoaiThanhPhanDiemBaseModel>>
    {
        public LoaiThanhPhanDiemFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Loại điểm thành phần có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiThanhPhanDiemQuery(LoaiThanhPhanDiemFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiThanhPhanDiemQuery, PaginationList<LoaiThanhPhanDiemBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiThanhPhanDiemBaseModel>> Handle(GetFilterLoaiThanhPhanDiemQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvThanhPhanMons
                            select new LoaiThanhPhanDiemBaseModel
                            {
                                IdThanhPhan = dt.IdThanhPhan,
                                Stt = dt.Stt,
                                KyHieu = dt.KyHieu,
                                TenThanhPhan = dt.TenThanhPhan,
                                TyLe = dt.TyLe,
                                ChonMacDinh = dt.ChonMacDinh,
                                ChuyenCan = dt.ChuyenCan,
                                NhomThanhPhan = dt.NhomThanhPhan,
                                TyLeNhom = dt.TyLeNhom,
                                KyHieuNhom = dt.KyHieuNhom,
                                ThucHanh = dt.ThucHanh

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenThanhPhan.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<LoaiThanhPhanDiemBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiThanhPhanDiemByIdQuery : IRequest<LoaiThanhPhanDiemModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Loại điểm thành phần theo id
        /// </summary>
        /// <param name="id">Id Loại điểm thành phần</param>
        public GetLoaiThanhPhanDiemByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiThanhPhanDiemByIdQuery, LoaiThanhPhanDiemModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiThanhPhanDiemModel> Handle(GetLoaiThanhPhanDiemByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiThanhPhanDiemConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvThanhPhanMons.FirstOrDefaultAsync(x => x.IdThanhPhan == id);

                    return AutoMapperUtils.AutoMap<SvThanhPhanMon, LoaiThanhPhanDiemModel>(entity);
                });
                return item;
            }
        }
    }
}
