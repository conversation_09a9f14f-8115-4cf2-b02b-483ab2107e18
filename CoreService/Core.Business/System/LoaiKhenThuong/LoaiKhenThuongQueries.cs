using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxLoaiKhenThuongQuery : IRequest<List<LoaiKhenThuongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy loại khen thưởng cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiKhenThuongQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiKhenThuongQuery, List<LoaiKhenThuongSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiKhenThuongSelectItemModel>> Handle(GetComboboxLoaiKhenThuongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiKhenThuongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvLoaiKhenThuongs.OrderBy(x => x.LoaiKhenThuong)
                                select new LoaiKhenThuongSelectItemModel()
                                {
                                    IdLoaiKhenThuong = dt.IdLoaiKhenThuong,
                                    IdCap = dt.IdCap,
                                    LoaiKhenThuong = dt.LoaiKhenThuong
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.LoaiKhenThuong.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLoaiKhenThuongQuery : IRequest<PaginationList<LoaiKhenThuongBaseModel>>
    {
        public LoaiKhenThuongFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách loại khen thưởng có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiKhenThuongQuery(LoaiKhenThuongFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiKhenThuongQuery, PaginationList<LoaiKhenThuongBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiKhenThuongBaseModel>> Handle(GetFilterLoaiKhenThuongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvLoaiKhenThuongs
                            join cap in _dataContext.SvCapKhenThuongKyLuats on dt.IdCap equals cap.IdCap
                            select new LoaiKhenThuongBaseModel
                            {
                                IdLoaiKhenThuong = dt.IdLoaiKhenThuong,
                                IdCap = dt.IdCap,
                                TenCap = cap.TenCap,
                                LoaiKhenThuong = dt.LoaiKhenThuong,
                                DiemThuong = dt.DiemThuong


                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.LoaiKhenThuong.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<LoaiKhenThuongBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiKhenThuongByIdQuery : IRequest<LoaiKhenThuongModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin loại khen thưởng theo id
        /// </summary>
        /// <param name="id">Id loại khen thưởng</param>
        public GetLoaiKhenThuongByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiKhenThuongByIdQuery, LoaiKhenThuongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiKhenThuongModel> Handle(GetLoaiKhenThuongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiKhenThuongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvLoaiKhenThuongs.FirstOrDefaultAsync(x => x.IdLoaiKhenThuong == id);

                    return AutoMapperUtils.AutoMap<SvLoaiKhenThuong, LoaiKhenThuongModel>(entity);
                });
                return item;
            }
        }
    }
}
