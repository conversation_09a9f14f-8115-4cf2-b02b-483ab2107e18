using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;

namespace Core.Business
{
    public class GetComboboxMonHocQuery : IRequest<List<MonHocSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy môn học cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxMonHocQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxMonHocQuery, List<MonHocSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<MonHocSelectItemModel>> Handle(GetComboboxMonHocQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = MonHocConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvMonHocs.OrderBy(x => x.TenMon)
                                select new MonHocSelectItemModel()
                                {
                                    IdMonHoc = dt.IdMonHoc,
                                    KyHieu = dt.KyHieu,
                                    TenMon = "(" + string.Join(",", dt.KyHieu) + ") " + dt.TenMon
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenMon.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetMonHocQuery : IRequest<PaginationList<MonHocItemModel>>
    {
        public int Count { get; set; }

        public MonHocFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy môn học cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetMonHocQuery(MonHocFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetMonHocQuery, PaginationList<MonHocItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<PaginationList<MonHocItemModel>> Handle(GetMonHocQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;
                var data = (from dt in _dataContext.SvMonHocs
                            select new MonHocItemModel()
                            {
                                IdMon = dt.IdMonHoc,
                                KyHieu = dt.KyHieu,
                                TenMon = dt.TenMon,
                                TenTiengAnh = dt.TenTiengAnh,
                                ChatLuongCao = dt.ChatLuongCao,
                                HpThucHanh = dt.HocPhanTH,
                                IdBm = dt.IdBoMon,
                                IdHeDt = dt.IdHeDt,
                                IdNhomHp = dt.IdNhomHp,
                                KyHieuCu = dt.KyHieuCu,
                                MonChungChi = dt.MonChungChi,
                                MonNN = dt.MonNn,
                                TenTiengPhap = dt.TenTiengPhap,
                                TenVietTat = dt.TenVietTat
                            });
                if (!string.IsNullOrEmpty(filter.tenMon))
                {
                    data = data.Where(x => x.TenMon.ToLower().Contains(filter.tenMon.Trim().ToLower()));
                }

                if (!string.IsNullOrEmpty(filter.tenTiengAnh))
                {
                    data = data.Where(x => x.TenTiengAnh.ToLower().Contains(filter.tenTiengAnh.Trim().ToLower()));
                }

                if (!string.IsNullOrEmpty(filter.kyHieu))
                {
                    data = data.Where(x => x.KyHieu.ToLower().Contains(filter.kyHieu.Trim().ToLower()));
                }

                if (filter.idBm >0)
                {
                    data = data.Where(x => x.IdBm == filter.idBm);
                }

                if (filter.idHeDt > 0)
                {
                    data = data.Where(x => x.IdHeDt == filter.idHeDt);
                }   

                if (filter.idNhomHp > 0)
                {
                    data = data.Where(x => x.IdNhomHp == filter.idNhomHp);
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<MonHocItemModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }
    
}
