using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxHuyenQuery : IRequest<List<HuyenSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy huyện cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHuyenQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHuyenQuery, List<HuyenSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HuyenSelectItemModel>> Handle(GetComboboxHuyenQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = HuyenConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvHuyens.OrderBy(x => x.TenHuyen)
                                select new HuyenSelectItemModel()
                                {
                                    IdHuyen = dt.IdHuyen,
                                    IdTinh = dt.IdTinh,
                                    TenHuyen = dt.TenHuyen
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenHuyen.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterHuyenQuery : IRequest<PaginationList<HuyenBaseModel>>
    {
        public HuyenFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách huyện có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterHuyenQuery(HuyenFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterHuyenQuery, PaginationList<HuyenBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<HuyenBaseModel>> Handle(GetFilterHuyenQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvHuyens
                            join tinh in _dataContext.SvTinhs on dt.IdTinh equals tinh.IdTinh
                            select new HuyenBaseModel
                            {
                                IdHuyen = dt.IdHuyen,
                                IdTinh = dt.IdTinh,
                                TenTinh = tinh.TenTinh,
                                TenHuyen = dt.TenHuyen,
                                TenHuyenEn = dt.TenHuyenEn,
                                IdHuyenCu=dt.IdHuyenCu,
                                IdHuyenCu1 = dt.IdHuyenCu1,
                                TenHuyenCu = dt.TenHuyenCu

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenHuyen.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<HuyenBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetHuyenByIdQuery : IRequest<HuyenModel>
    {
        public string Id { get; set; }

        /// <summary>
        /// Lấy thông tin huyện theo id
        /// </summary>
        /// <param name="id">Id huyện</param>
        public GetHuyenByIdQuery(string id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetHuyenByIdQuery, HuyenModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<HuyenModel> Handle(GetHuyenByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = HuyenConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvHuyens.FirstOrDefaultAsync(x => x.IdHuyen == id);

                    return AutoMapperUtils.AutoMap<SvHuyen, HuyenModel>(entity);
                });
                return item;
            }
        }
    }
}
