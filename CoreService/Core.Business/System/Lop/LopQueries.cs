using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxLopQuery : IRequest<List<LopSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy lớp cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLopQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLopQuery, List<LopSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LopSelectItemModel>> Handle(GetComboboxLopQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LopConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvLops.OrderBy(x => x.TenLop)
                                select new LopSelectItemModel()
                                {
                                    IdLop = dt.IdLop,
                                    TenLop = dt.TenLop,
                                    KhoaHoc = dt.KhoaHoc,
                                    NienKhoa = dt.NienKhoa,
                                    IdHe = dt.IdHe,
                                    CaHoc = dt.CaHoc,
                                    IdChuyenNganh = dt.IdChuyenNganh,
                                    IdDt = dt.IdDt,
                                    IdKhoa = dt.IdKhoa,
                                    IdPhong = dt.IdPhong,
                                    RaTruong = dt.RaTruong,
                                    SoSv = dt.SoSv
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenLop.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLopQuery : IRequest<PaginationList<LopBaseModel>>
    {
        public LopFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách lớp có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLopQuery(LopFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLopQuery, PaginationList<LopBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LopBaseModel>> Handle(GetFilterLopQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvLops
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            join khoa in _dataContext.SvKhoas on dt.IdKhoa equals khoa.IdKhoa
                            join cn in _dataContext.SvChuyenNganhs on dt.IdChuyenNganh equals cn.IdChuyenNganh
                            join n in _dataContext.SvNganhs on cn.IdNganh equals n.IdNganh
                            select new LopBaseModel
                            {
                                IdLop = dt.IdLop,
                                TenLop = dt.TenLop,
                                TenHe = he.TenHe,
                                TenKhoa = khoa.TenKhoa,
                                TenChuyenNganh = cn.ChuyenNganh,
                                TenNganh = n.TenNganh,
                                NienKhoa = dt.NienKhoa,
                                KhoaHoc = dt.KhoaHoc,
                                CaHoc = dt.CaHoc,
                                IdChuyenNganh = dt.IdChuyenNganh,
                                IdDt = dt.IdDt,
                                IdHe = dt.IdHe,
                                IdKhoa = dt.IdKhoa,
                                IdPhong = dt.IdPhong,
                                RaTruong = dt.RaTruong,
                                SoSv = dt.SoSv
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenLop.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<LopBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLopByIdQuery : IRequest<LopModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin lớp theo id
        /// </summary>
        /// <param name="id">Id lớp</param>
        public GetLopByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLopByIdQuery, LopModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LopModel> Handle(GetLopByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LopConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvLops.FirstOrDefaultAsync(x => x.IdLop == id);

                    return AutoMapperUtils.AutoMap<SvLop, LopModel>(entity);
                });
                return item;
            }
        }
    }
}
