using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxLoaiGiayToQuery : IRequest<List<LoaiGiayToSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy loại giấy tờ cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiGiayToQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiGiayToQuery, List<LoaiGiayToSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiGiayToSelectItemModel>> Handle(GetComboboxLoaiGiayToQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiGiayToConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvLoaiGiayTos.OrderBy(x => x.TenGiayTo)
                                select new LoaiGiayToSelectItemModel()
                                {
                                    IdGiayTo = dt.IdGiayTo,
                                    MaGiayTo = dt.MaGiayTo,
                                    TenGiayTo = dt.TenGiayTo,
                                    IdHe = dt.IdHe,
                                    IdPhong = dt.IdPhong
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenGiayTo.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLoaiGiayToQuery : IRequest<PaginationList<LoaiGiayToBaseModel>>
    {
        public LoaiGiayToFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách loại giấy tờ có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiGiayToQuery(LoaiGiayToFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiGiayToQuery, PaginationList<LoaiGiayToBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiGiayToBaseModel>> Handle(GetFilterLoaiGiayToQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvLoaiGiayTos
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            join phong in _dataContext.HtPhongs on dt.IdPhong equals phong.IdPhong
                            select new LoaiGiayToBaseModel
                            {
                                IdGiayTo = dt.IdGiayTo,
                                MaGiayTo = dt.MaGiayTo,
                                TenGiayTo = dt.TenGiayTo,
                                Stt = dt.Stt,
                                IdHe = dt.IdHe,
                                IdPhong = dt.IdPhong,
                                BatBuoc = dt.BatBuoc,
                                MacDinh = dt.MacDinh,
                                Nhom = dt.Nhom,
                                GhiChu = dt.GhiChu,
                                TuyenSinh = dt.TuyenSinh,
                                TenHe = he.TenHe,
                                TenPhong = phong.Phong

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenGiayTo.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<LoaiGiayToBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiGiayToByIdQuery : IRequest<LoaiGiayToModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin loại giấy tờ theo id
        /// </summary>
        /// <param name="id">Id loại giấy tờ</param>
        public GetLoaiGiayToByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiGiayToByIdQuery, LoaiGiayToModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiGiayToModel> Handle(GetLoaiGiayToByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiGiayToConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvLoaiGiayTos.FirstOrDefaultAsync(x => x.IdGiayTo == id);

                    return AutoMapperUtils.AutoMap<SvLoaiGiayTo, LoaiGiayToModel>(entity);
                });
                return item;
            }
        }
    }
}
