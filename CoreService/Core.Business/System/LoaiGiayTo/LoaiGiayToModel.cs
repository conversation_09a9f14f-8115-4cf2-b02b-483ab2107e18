using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LoaiGiayToSelectItemModel
    {
        public int IdGiayTo { get; set; }

        public string MaGiayTo { get; set; }

        public string TenGiayTo { get; set; }

        public int IdHe { get; set; }

        public int IdPhong { get; set; }

    }

    public class LoaiGiayToBaseModel
    {
        public int IdGiayTo { get; set; }

        public string MaGiayTo { get; set; }

        public string TenGiayTo { get; set; }

        public int Stt { get; set; }

        public bool BatBuoc { get; set; }

        public bool MacDinh { get; set; }

        public int Nhom { get; set; }

        public string GhiChu { get; set; }

        public int IdHe { get; set; }

        public int IdPhong { get; set; }

        public string TenPhong { get; set; }

        public string TenHe { get; set; }

        public bool TuyenSinh { get; set; }
    }


    public class LoaiGiayToModel : LoaiGiayToBaseModel
    {

    }

    public class LoaiGiayToFilterModel : BaseQueryFilterModel
    {
        public LoaiGiayToFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdGiayTo";
        }
    }

    public class CreateLoaiGiayToModel
    {
        [Required(ErrorMessage = "LoaiGiayTo.IdGiayTo.NotRequire")]
        public int IdGiayTo { get; set; }

        [MaxLength(5, ErrorMessage = "LoaiGiayTo.MaGiayTo.MaxLength(5)")]
        [Required(ErrorMessage = "LoaiGiayTo.MaGiayTo.NotRequire")]
        public string MaGiayTo { get; set; }

        [MaxLength(100, ErrorMessage = "LoaiGiayTo.TenGiayTo.MaxLength(100)")]
        [Required(ErrorMessage = "LoaiGiayTo.TenGiayTo.NotRequire")]
        public string TenGiayTo { get; set; }

        [Required(ErrorMessage = "LoaiGiayTo.Stt.NotRequire")]
        public int Stt { get; set; }

        [Required(ErrorMessage = "LoaiGiayTo.BatBuoc.NotRequire")]
        public bool BatBuoc { get; set; }

        [Required(ErrorMessage = "LoaiGiayTo.MacDinh.NotRequire")]
        public bool MacDinh { get; set; }

        [Required(ErrorMessage = "LoaiGiayTo.Nhom.NotRequire")]
        public int Nhom { get; set; }

        [Required(ErrorMessage = "LoaiGiayTo.GhiChu.NotRequire")]
        [MaxLength(150, ErrorMessage = "LoaiGiayTo.GhiChu.MaxLength(150)")]
        public string GhiChu { get; set; }

        [Required(ErrorMessage = "LoaiGiayTo.IdHe.NotRequire")]
        public int IdHe { get; set; }

        [Required(ErrorMessage = "LoaiGiayTo.IdPhong.NotRequire")]
        public int IdPhong { get; set; }

        [Required(ErrorMessage = "LoaiGiayTo.TuyenSinh.NotRequire")]
        public bool TuyenSinh { get; set; }
    }

    public class CreateManyLoaiGiayToModel
    {
        public List<CreateLoaiGiayToModel> listLoaiGiayToModels { get; set; }
    }

    public class UpdateLoaiGiayToModel : CreateLoaiGiayToModel
    {
        public void UpdateEntity(SvLoaiGiayTo input)
        {
            input.IdGiayTo = IdGiayTo;
            input.MaGiayTo = MaGiayTo;
            input.TenGiayTo = TenGiayTo;
            input.Stt = Stt;
            input.BatBuoc = BatBuoc;
            input.MacDinh = MacDinh;
            input.Nhom = Nhom;
            input.GhiChu = GhiChu;
            input.IdHe = IdHe;
            input.IdPhong = IdPhong;
            input.TuyenSinh = TuyenSinh;


        }
    }
}
