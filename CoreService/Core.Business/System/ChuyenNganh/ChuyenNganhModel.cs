using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class ChuyenNganhSelectItemModel
    {
        public int IdChuyenNganh { get; set; }
        public string MaChuyenNganh { get; set; }
        public string ChuyenNganh { get; set; }
        public int IdNganh { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
    }

    public class ChuyenNganhBaseModel
    {
        public int IdChuyenNganh { get; set; }
        public string MaChuyenNganh { get; set; }
        public string ChuyenNganh { get; set; }
        public string ChuyenNganhEn { get; set; }
        public int IdNganh { get; set; }
        public string TenNganh { get; set; }
    }


    public class ChuyenNganhModel : ChuyenNganhBaseModel
    {

    }

    public class ChuyenNganhFilterModel : BaseQueryFilterModel
    {
        public ChuyenNganhFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdChuyenNganh";
        }
    }

    public class CreateChuyenNganhModel
    {
        [Required(ErrorMessage = "ChuyenNganh.IdChuyenNganh.NotRequire")]
        public int IdChuyenNganh { get; set; }

        [MaxLength(20, ErrorMessage = "ChuyenNganh.MaChuyenNganh.MaxLength(20)")]
        [Required(ErrorMessage = "ChuyenNganh.MaChuyenNganh.NotRequire")]
        public string MaChuyenNganh { get; set; }

        [MaxLength(200, ErrorMessage = "ChuyenNganh.ChuyenNganh.MaxLength(200)")]
        [Required(ErrorMessage = "ChuyenNganh.ChuyenNganh.NotRequire")]
        public string ChuyenNganh { get; set; }

        [Required(ErrorMessage = "ChuyenNganh.IdNganh.NotRequire")]
        public int IdNganh { get; set; }

        [MaxLength(200, ErrorMessage = "ChuyenNganh.ChuyenNganhEn.MaxLength(200)")]
        public string ChuyenNganhEn { get; set; }

    }

    public class CreateManyChuyenNganhModel
    {
        public List<CreateChuyenNganhModel> listChuyenNganhModels { get; set; }
    }

    public class UpdateChuyenNganhModel : CreateChuyenNganhModel
    {
        public void UpdateEntity(SvChuyenNganh input)
        {
            input.IdChuyenNganh = IdChuyenNganh;
            input.MaChuyenNganh = MaChuyenNganh;
            input.ChuyenNganh = ChuyenNganh;
            input.ChuyenNganhEn = ChuyenNganhEn;
            input.IdNganh = IdNganh;

        }
    }
}
