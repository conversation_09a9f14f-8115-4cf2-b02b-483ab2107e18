using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CapKhenThuongKyLuatSelectItemModel
    {
        public int IdCap { get; set; }
        public string MaCap { get; set; }
        public string TenCap { get; set; }
    }

    public class CapKhenThuongKyLuatBaseModel
    {
        public int IdCap { get; set; }
        public string MaCap { get; set; }
        public string TenCap { get; set; }

    }


    public class CapKhenThuongKyLuatModel : CapKhenThuongKyLuatBaseModel
    {
      
    }

    public class CapKhenThuongKyLuatFilterModel : BaseQueryFilterModel
    {
        public CapKhenThuongKyLuatFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdCap";
        }
    }

    public class CreateCapKhenThuongKyLuatModel
    {
        [Required(ErrorMessage = "CapKhenThuongKyLuat.IdCap.NotRequire")]
        public int IdCap { get; set; }

        [MaxLength(20, ErrorMessage = "CapKhenThuongKyLuat.MaCap.MaxLength(20)")]
        public string MaCap { get; set; }

        [MaxLength(50, ErrorMessage = "CapKhenThuongKyLuat.TenCap.MaxLength(50)")]
        [Required(ErrorMessage = "CapKhenThuongKyLuat.TenCap.NotRequire")]
        public string TenCap { get; set; }

   
    }

    public class CreateManyCapKhenThuongKyLuatModel
    {
        public List<CreateCapKhenThuongKyLuatModel> listCapKhenThuongKyLuatModels { get; set; }
    }

    public class UpdateCapKhenThuongKyLuatModel : CreateCapKhenThuongKyLuatModel
    {
        public void UpdateEntity(SvCapKhenThuongKyLuat input)
        {
            input.IdCap = IdCap;
            input.MaCap = MaCap;
            input.TenCap = TenCap;

        }
    }
}
