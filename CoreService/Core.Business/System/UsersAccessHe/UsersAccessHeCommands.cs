using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GanLopQuanLyCommand : IRequest<Unit>
    {
        public GanLopQuanLyModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public GanLopQuanLyCommand(GanLopQuanLyModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<GanLopQuanLyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(GanLopQuanLyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {UsersAccessHeConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<GanLopQuanLyModel, HtUsersAccessHe>(model);

                var checkCode = await _dataContext.HtUsersAccessHes.AnyAsync(dt => dt.UserId == entity.UserId && dt.IdHe == entity.IdHe
                                                                                          && dt.IdKhoa == entity.IdKhoa && dt.KhoaHoc == entity.KhoaHoc
                                                                                          && dt.IdChuyenNganh == entity.IdChuyenNganh && dt.IdNganh == entity.IdNganh);
                if (checkCode)
                {
                    throw new ArgumentException("Đã tồn tại");
                }

                await _dataContext.HtUsersAccessHes.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {UsersAccessHeConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Gán lớp quản lý",
                    ObjectCode = UsersAccessHeConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(UsersAccessHeConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteGanLopQuanLyCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteGanLopQuanLyCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteGanLopQuanLyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteGanLopQuanLyCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {UsersAccessHeConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.HtUsersAccessHes.FirstOrDefaultAsync(x => x.Id == id);

                _dataContext.HtUsersAccessHes.Remove(entity);

                Log.Information($"Delete {UsersAccessHeConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa quyền quản lý lớp của giảng viên : {entity.UserId}",
                    ObjectCode = UsersAccessHeConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(UsersAccessHeConstant.BuildCacheKey());
                _cacheService.Remove(UsersAccessHeConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
