using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UsersAccessHeBaseModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdNganh { get; set; }
        public int IdChuyenNganh { get; set; }
        public string IdLop { get; set; }
    }

    public class AccessHeByUserBaseModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdNganh { get; set; }
        public int IdChuyenNganh { get; set; }
        public string IdLop { get; set; }
        public List<int> LopIds { get; set; }
        public string TenLop { get; set; }
        public string TenHe { get; set; }
        public string TenKhoa { get; set; }
        public string TenNganh { get; set; }
        public string TenChuyenNganh { get; set; }
    }


    public class UsersAccessHeModel : UsersAccessHeBaseModel
    {

    }

    public class UsersAccessHeFilterModel : BaseQueryFilterModel
    {
        public UsersAccessHeFilterModel()
        {
            Ascending = "desc";
            PropertyName = "Id";
        }
    }

    public class CreateUsersAccessHeModel
    {
        public int UserId { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdNganh { get; set; }
        public int IdChuyenNganh { get; set; }
        public string IdLop { get; set; }
    }

    public class UpdateUsersAccessHeModel : CreateUsersAccessHeModel
    {
        public int Id { get; set; }
        public void UpdateEntity(HtUsersAccessHe input)
        {

        }
    }

    public class UsersAccessHeByUserModel : LopSelectItemModel
    {
        public string MaHe { get; set; }
        public string MaChuyenNganh { get; set; }
        public string He { get; set; }
        public string Khoa { get; set; }
        public string ChuyenNganh { get; set; }
        public int IdNganh { get; set; }
        public string Nganh { get; set; }
    }

    public class LopHocUserModel
    {
        [DataColumn("ID_lop")]
        public int IdLop { get; set; }

        [DataColumn("Ten_lop")]
        public string TenLop { get; set; }

        [DataColumn("ID_he")]
        public int IdHe { get; set; }

        [DataColumn("ID_khoa")]
        public int IdKhoa { get; set; }

        [DataColumn("ID_chuyen_nganh")]
        public int IdChuyenNganh { get; set; }

        [DataColumn("Khoa_hoc")]
        public string KhoaHoc { get; set; }

        [DataColumn("Nien_khoa")]
        public string NienKhoa { get; set; }

        [DataColumn("ID_dt")]
        public int IdDt { get; set; }

        [DataColumn("So_sv")]
        public int SoSv { get; set; }

        [DataColumn("Ra_truong")]
        public bool RaTruong { get; set; }

        [DataColumn("ID_phong")]
        public int IdPhong { get; set; }

        [DataColumn("Ca_hoc")]
        public string CaHoc { get; set; }

        [DataColumn("Ten_he")]
        public string TenHe { get; set; }

        [DataColumn("Ten_khoa")]
        public string TenKhoa { get; set; }

        [DataColumn("Chuyen_nganh")]
        public string ChuyenNganh { get; set; }

        [DataColumn("Ten_nganh")]
        public string TenNganh { get; set; }

        [DataColumn("ID_nganh")]
        public int IdNganh { get; set; }
    }

    public class GanLopQuanLyModel 
    {
        public int UserId { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdNganh { get; set; }
        public int IdChuyenNganh { get; set; }
    }


}
