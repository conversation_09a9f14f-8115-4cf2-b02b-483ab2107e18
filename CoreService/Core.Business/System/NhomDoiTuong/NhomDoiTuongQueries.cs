using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxNhomDoiTuongQuery : IRequest<List<NhomDoiTuongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy nhóm đối tượng cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxNhomDoiTuongQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxNhomDoiTuongQuery, List<NhomDoiTuongSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<NhomDoiTuongSelectItemModel>> Handle(GetComboboxNhomDoiTuongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = NhomDoiTuongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvNhomDoiTuongs.OrderBy(x => x.TenNhom)
                                select new NhomDoiTuongSelectItemModel()
                                {
                                    IdNhomDoiTuong = dt.IdNhomDoiTuong,
                                    MaNhom = dt.MaNhom,
                                    TenNhom = dt.TenNhom
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenNhom.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterNhomDoiTuongQuery : IRequest<PaginationList<NhomDoiTuongBaseModel>>
    {
        public NhomDoiTuongFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách nhóm đối tượng có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterNhomDoiTuongQuery(NhomDoiTuongFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterNhomDoiTuongQuery, PaginationList<NhomDoiTuongBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<NhomDoiTuongBaseModel>> Handle(GetFilterNhomDoiTuongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvNhomDoiTuongs
                            select new NhomDoiTuongBaseModel
                            {
                                IdNhomDoiTuong = dt.IdNhomDoiTuong,
                                MaNhom = dt.MaNhom,
                                TenNhom = dt.TenNhom,
                                TenNhomEn = dt.TenNhomEn

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenNhom.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<NhomDoiTuongBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetNhomDoiTuongByIdQuery : IRequest<NhomDoiTuongModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin nhóm đối tượng theo id
        /// </summary>
        /// <param name="id">Id nhóm đối tượng</param>
        public GetNhomDoiTuongByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetNhomDoiTuongByIdQuery, NhomDoiTuongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<NhomDoiTuongModel> Handle(GetNhomDoiTuongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = NhomDoiTuongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvNhomDoiTuongs.FirstOrDefaultAsync(x => x.IdNhomDoiTuong == id);

                    return AutoMapperUtils.AutoMap<SvNhomDoiTuong, NhomDoiTuongModel>(entity);
                });
                return item;
            }
        }
    }
}
