using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepLoaiHocTapThangDiem10SelectItemModel
    {
        public int IdXepLoai { get; set; }
        public string XepLoai { get; set; }
        public string MaXepLoai { get; set; }
        public int IdHe { get; set; }
    }

    public class XepLoaiHocTapThangDiem10BaseModel
    {
        public int IdXepLoai { get; set; }
        public string XepLoai { get; set; }
        public float TuDiem { get; set; }
        public float DenDiem { get; set; }
        public string MaXepLoai { get; set; }
        public string XepLoaiEn { get; set; }
        public int? IdHe { get; set; }

        public string TenHe { get; set; }
    }


    public class XepLoaiHocTapThangDiem10Model : XepLoaiHocTapThangDiem10BaseModel
    {
      
    }

    public class XepLoaiHocTapThangDiem10FilterModel : BaseQueryFilterModel
    {
        public XepLoaiHocTapThangDiem10FilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepLoai";
        }
    }

    public class CreateXepLoaiHocTapThangDiem10Model
    {
        [MaxLength(50, ErrorMessage = "XepLoaiHocTapThangDiem10.XepLoai.MaxLength(50)")]
        [Required(ErrorMessage = "XepLoaiHocTapThangDiem10.XepLoai.NotRequire")]
        public string XepLoai { get; set; }

        [Required(ErrorMessage = "XepLoaiHocTapThangDiem10.TuDiem.NotRequire")]
        public float TuDiem { get; set; }

        [Required(ErrorMessage = "XepLoaiHocTapThangDiem10.DenDiem.NotRequire")]
        public float DenDiem { get; set; }

        [MaxLength(20, ErrorMessage = "XepLoaiHocTapThangDiem10.MaXepLoai.MaxLength(20)")]
        [Required(ErrorMessage = "XepLoaiHocTapThangDiem10.MaXepLoai.NotRequire")]
        public string MaXepLoai { get; set; }

        [MaxLength(50, ErrorMessage = "XepLoaiHocTapThangDiem10.XepLoaiEn.MaxLength(50)")]
        public string XepLoaiEn { get; set; }

        [Required(ErrorMessage = "XepLoaiHocTapThangDiem10.IdHe.NotRequire")]
        public int IdHe { get; set; }
    }

    public class CreateManyXepLoaiHocTapThangDiem10Model
    {
        public List<CreateXepLoaiHocTapThangDiem10Model> listXepLoaiHocTapThangDiem10Models { get; set; }
    }

    public class UpdateXepLoaiHocTapThangDiem10Model : CreateXepLoaiHocTapThangDiem10Model
    {
        public void UpdateEntity(SvXepLoaiHocTapThangDiem10 input)
        {
            input.XepLoai = XepLoai;
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.MaXepLoai = MaXepLoai;
            input.XepLoaiEn = XepLoaiEn;
            input.IdHe = IdHe;

        }
    }
}
