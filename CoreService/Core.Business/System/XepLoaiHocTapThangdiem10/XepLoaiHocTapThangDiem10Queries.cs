using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;


namespace Core.Business
{
    public class GetComboboxXepLoaiHocTapThangDiem10Query : IRequest<List<XepLoaiHocTapThangDiem10SelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại học tập thang 10 cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepLoaiHocTapThangDiem10Query(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepLoaiHocTapThangDiem10Query, List<XepLoaiHocTapThangDiem10SelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepLoaiHocTapThangDiem10SelectItemModel>> Handle(GetComboboxXepLoaiHocTapThangDiem10Query request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepLoaiHocTapThangDiem10Constant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepLoaiHocTapThangDiem10s.OrderBy(x => x.XepLoai)
                                select new XepLoaiHocTapThangDiem10SelectItemModel()
                                {
                                    IdXepLoai = dt.IdXepLoai,
                                    XepLoai = dt.XepLoai,
                                    MaXepLoai = dt.MaXepLoai,
                                    IdHe = dt.IdHe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepLoai.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepLoaiHocTapThangDiem10Query : IRequest<PaginationList<XepLoaiHocTapThangDiem10BaseModel>>
    {
        public XepLoaiHocTapThangDiem10FilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang 10 có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepLoaiHocTapThangDiem10Query(XepLoaiHocTapThangDiem10FilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepLoaiHocTapThangDiem10Query, PaginationList<XepLoaiHocTapThangDiem10BaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepLoaiHocTapThangDiem10BaseModel>> Handle(GetFilterXepLoaiHocTapThangDiem10Query request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepLoaiHocTapThangDiem10s
                             join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            select new XepLoaiHocTapThangDiem10BaseModel
                            {
                                IdXepLoai = dt.IdXepLoai,
                                XepLoai = dt.XepLoai,
                                TuDiem = dt.TuDiem,
                                DenDiem = dt.DenDiem,
                                MaXepLoai = dt.MaXepLoai,
                                XepLoaiEn = dt.XepLoaiEn,
                                IdHe = dt.IdHe,
                                TenHe = he.TenHe
                            });


                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepLoai.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<XepLoaiHocTapThangDiem10BaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepLoaiHocTapThangDiem10ByIdQuery : IRequest<XepLoaiHocTapThangDiem10Model>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại học tập thang 10 theo id
        /// </summary>
        /// <param name="id">Id Xếp loại học tập thang 10</param>
        public GetXepLoaiHocTapThangDiem10ByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepLoaiHocTapThangDiem10ByIdQuery, XepLoaiHocTapThangDiem10Model>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepLoaiHocTapThangDiem10Model> Handle(GetXepLoaiHocTapThangDiem10ByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepLoaiHocTapThangDiem10Constant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepLoaiHocTapThangDiem10s.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                    return AutoMapperUtils.AutoMap<SvXepLoaiHocTapThangDiem10, XepLoaiHocTapThangDiem10Model>(entity);
                });
                return item;
            }
        }
    }
}
