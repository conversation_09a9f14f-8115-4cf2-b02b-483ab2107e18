using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateVungCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateVungModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateVungCommand(int id, UpdateVungModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateVungCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateVungCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {VungConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvVungs.FirstOrDefaultAsync(dt => dt.IdVung == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvVungs.AnyAsync(x => (x.KyHieu == model.KyHieu) && x.IdVung != model.IdVung);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Vung.Existed", model.KyHieu.ToString()]}");
                }

                Log.Information($"Before Update {VungConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvVungs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {VungConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {VungConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật vùng : {entity.KyHieu}",
                    ObjectCode = VungConstant.CachePrefix,
                    ObjectId = entity.IdVung.ToString()
                });

                //Xóa cache
                _cacheService.Remove(VungConstant.BuildCacheKey(entity.IdVung.ToString()));
                _cacheService.Remove(VungConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }
}
