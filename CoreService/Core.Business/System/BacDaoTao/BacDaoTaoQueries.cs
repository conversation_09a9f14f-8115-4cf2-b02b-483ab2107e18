using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxBacDaoTaoQuery : IRequest<List<BacDaoTaoSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y bậc đào tạo cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxBacDaoTaoQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxBacDaoTaoQuery, List<BacDaoTaoSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<BacDaoTaoSelectItemModel>> Handle(GetComboboxBacDaoTaoQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = BacDaoTaoConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbBacDaoTaos.OrderBy(x => x.BacDaoTao)
                                select new BacDaoTaoSelectItemModel()
                                {
                                    IdBacDaoTao = dt.IdBacDaoTao,
                                    MaBacDaoTao = dt.MaBacDaoTao,
                                    BacDaoTao = dt.BacDaoTao
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.BacDaoTao.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterBacDaoTaoQuery : IRequest<PaginationList<BacDaoTaoBaseModel>>
    {
        public BacDaoTaoFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách bậc đào tạo có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterBacDaoTaoQuery(BacDaoTaoFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterBacDaoTaoQuery, PaginationList<BacDaoTaoBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<BacDaoTaoBaseModel>> Handle(GetFilterBacDaoTaoQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TkbBacDaoTaos
                            select new BacDaoTaoBaseModel
                            {
                                IdBacDaoTao = dt.IdBacDaoTao,
                                MaBacDaoTao = dt.MaBacDaoTao,
                                BacDaoTao = dt.BacDaoTao,

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.BacDaoTao.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<BacDaoTaoBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetBacDaoTaoByIdQuery : IRequest<BacDaoTaoModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin bậc đào tạo theo id
        /// </summary>
        /// <param name="id">Id bậc đào tạo</param>
        public GetBacDaoTaoByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetBacDaoTaoByIdQuery, BacDaoTaoModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<BacDaoTaoModel> Handle(GetBacDaoTaoByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = BacDaoTaoConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbBacDaoTaos.FirstOrDefaultAsync(x => x.IdBacDaoTao == id);

                    return AutoMapperUtils.AutoMap<TkbBacDaoTao, BacDaoTaoModel>(entity);
                });
                return item;
            }
        }
    }
}
