using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateBacDaoTaoCommand : IRequest<Unit>
    {
        public CreateBacDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateBacDaoTaoCommand(CreateBacDaoTaoModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateBacDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateBacDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {BacDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateBacDaoTaoModel, TkbBacDaoTao>(model);

                var checkCode = await _dataContext.TkbBacDaoTaos.AnyAsync(x => x.IdBacDaoTao == entity.IdBacDaoTao || x.BacDaoTao == entity.BacDaoTao || x.MaBacDaoTao == entity.MaBacDaoTao);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["BacDaoTao.Existed", entity.BacDaoTao.ToString()]}");
                }

                await _dataContext.TkbBacDaoTaos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {BacDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới bậc đào tạo: {entity.BacDaoTao}",
                    ObjectCode = BacDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdBacDaoTao.ToString()
                });

                //Xóa cache
                _cacheService.Remove(BacDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyBacDaoTaoCommand : IRequest<Unit>
    {
        public CreateManyBacDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyBacDaoTaoCommand(CreateManyBacDaoTaoModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyBacDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyBacDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {BacDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listBacDaoTaoAdd = model.listBacDaoTaoModels.Select(x => x.BacDaoTao).ToList();
                var listMaBacDaoTaoAdd = model.listBacDaoTaoModels.Select(x => x.MaBacDaoTao).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyBacDaoTaoModel, TkbBacDaoTao>(model);

                // Check data duplicate
                if (listBacDaoTaoAdd.Count() != listBacDaoTaoAdd.Distinct().Count() || listMaBacDaoTaoAdd.Count() != listMaBacDaoTaoAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbBacDaoTaos.AnyAsync(x => listBacDaoTaoAdd.Contains(x.BacDaoTao)) || await _dataContext.TkbBacDaoTaos.AnyAsync(x => listMaBacDaoTaoAdd.Contains(x.MaBacDaoTao)))
                {
                    throw new ArgumentException($"{_localizer["BacDaoTao.Existed"]}");
                }

                var listEntity = model.listBacDaoTaoModels.Select(x => new TkbBacDaoTao()
                {
                    IdBacDaoTao = x.IdBacDaoTao,
                    MaBacDaoTao = x.MaBacDaoTao,
                    BacDaoTao = x.BacDaoTao

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdBacDaoTao).ToList();

                Log.Information($"Create many {BacDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import bậc đào tạo từ file excel",
                    ObjectCode = BacDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(BacDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateBacDaoTaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateBacDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateBacDaoTaoCommand(int id, UpdateBacDaoTaoModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateBacDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateBacDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {BacDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbBacDaoTaos.FirstOrDefaultAsync(dt => dt.IdBacDaoTao == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var m = AutoMapperUtils.AutoMap<UpdateBacDaoTaoModel, TkbBacDaoTao>(model);
                var checkCode = await _dataContext.TkbBacDaoTaos.AnyAsync(x => (x.BacDaoTao == model.BacDaoTao || x.MaBacDaoTao == model.MaBacDaoTao) && x.IdBacDaoTao != model.IdBacDaoTao);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["BacDaoTao.Existed", model.BacDaoTao.ToString()]}");
                }

                Log.Information($"Before Update {BacDaoTaoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbBacDaoTaos.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {BacDaoTaoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {BacDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật bậc đào tạo: {entity.BacDaoTao}",
                    ObjectCode = BacDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdBacDaoTao.ToString()
                });

                //Xóa cache
                _cacheService.Remove(BacDaoTaoConstant.BuildCacheKey(entity.IdBacDaoTao.ToString()));
                _cacheService.Remove(BacDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteBacDaoTaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteBacDaoTaoCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteBacDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteBacDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {BacDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbBacDaoTaos.FirstOrDefaultAsync(x => x.IdBacDaoTao == id);

                _dataContext.TkbBacDaoTaos.Remove(entity);

                Log.Information($"Delete {BacDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa bậc đào tạo: {entity.BacDaoTao}",
                    ObjectCode = BacDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdBacDaoTao.ToString()
                });

                //Xóa cache
                _cacheService.Remove(BacDaoTaoConstant.BuildCacheKey());
                _cacheService.Remove(BacDaoTaoConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
