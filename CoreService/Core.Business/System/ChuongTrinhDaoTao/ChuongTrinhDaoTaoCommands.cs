using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Minio.DataModel;
using Serilog;
using SharpCompress.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
namespace Core.Business
{
    public class CreateChuongTrinhDaoTaoCommand : IRequest<Unit>
    {
        public CreateChuongTrinhDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới chương trình đào tạo
        /// </summary>
        public CreateChuongTrinhDaoTaoCommand(CreateChuongTrinhDaoTaoModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateChuongTrinhDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateChuongTrinhDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateChuongTrinhDaoTaoModel, SvChuongTrinhDaoTao>(model);

                var checkCode = await _dataContext.SvChuongTrinhDaoTaos.AnyAsync(x => x.IdHe == entity.IdHe && x.IdKhoa == entity.IdKhoa && x.IdChuyenNganh == entity.IdChuyenNganh && x.So == entity.So);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChuongTrinhDaoTao.Existed", JsonSerializer.Serialize(model)]}");
                }

                await _dataContext.SvChuongTrinhDaoTaos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới chương trình đào tạo: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(entity.IdDt)
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateChuongTrinhDaoTaoCommand : IRequest<Unit>
    {
        public List<UpdateChuongTrinhDaoTaoModel> Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới chương trình đào tạo
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        public UpdateChuongTrinhDaoTaoCommand(List<UpdateChuongTrinhDaoTaoModel> model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateChuongTrinhDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdateChuongTrinhDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Update {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                foreach (var item in model)
                {
                    var entity = await _dataContext.SvChuongTrinhDaoTaos.FirstOrDefaultAsync(dt => dt.IdDt == item.IdDt);
                    if (!(entity == null))
                    {
                        var checkCode = await _dataContext.SvChuongTrinhDaoTaos.AnyAsync(x => x.IdHe == item.IdHe && x.IdKhoa == item.IdKhoa && x.KhoaHoc == item.KhoaHoc && x.IdChuyenNganh == item.IdChuyenNganh && x.So == item.So && x.IdDt != item.IdDt);
                        if (!checkCode)
                        {
                            item.UpdateEntity(entity);
                            _dataContext.SvChuongTrinhDaoTaos.Update(entity);
                        }

                    }

                }
                var listIdDt = model.Select(x => x.IdDt).ToList();
                Log.Information($"Before Update {NganhConstant.CachePrefix}: {JsonSerializer.Serialize(listIdDt)}");
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {NganhConstant.CachePrefix}: {JsonSerializer.Serialize(listIdDt)}");

                Log.Information($"Update {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật chương trình đào tạo: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(listIdDt)
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteChuongTrinhDaoTaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa chương trình đào tạo
        /// </summary>
        public DeleteChuongTrinhDaoTaoCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteChuongTrinhDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private ICallStoreHelper _callStoreHelper;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, ICallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<Unit> Handle(DeleteChuongTrinhDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;

                Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvChuongTrinhDaoTaos.FirstOrDefaultAsync(x => x.IdDt == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkExit = _callStoreHelper.CallStoreCheckDiemCtdtAsync(id, 0);
                if (checkExit != null && checkExit.Rows.Count > 0)
                {
                    var checkValue = Convert.ToInt32(checkExit.Rows[0][0]);
                    if (checkValue > 0)
                    {
                        throw new ArgumentException($"{_localizer["Chương trình đào tạo đã có điểm"]}");
                    }
                }

                _dataContext.SvChuongTrinhDaoTaos.Remove(entity);
                await _dataContext.SaveChangesAsync();

                var dtct = await _dataContext.SvChuongTrinhDaoTaoChiTiets.Where(x => x.IdDt == id).ToListAsync();
                if (dtct.Count > 0)
                {
                    _dataContext.SvChuongTrinhDaoTaoChiTiets.RemoveRange(dtct);
                    await _dataContext.SaveChangesAsync();
                }



                Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(id));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa chương trình đào tạo: {JsonSerializer.Serialize(id)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(id)
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey(id.ToString()));


                return Unit.Value;
            }
        }
    }

    public class SaoChepChuongTrinhDaoTaoCommand : IRequest<Unit>
    {
        public SaoChepChuongTrinhDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public string User { get; set; }

        /// <summary>
        /// Sao chép chương trình đào tạo
        /// </summary>
        public SaoChepChuongTrinhDaoTaoCommand( SaoChepChuongTrinhDaoTaoModel model, SystemLogModel systemLog, string user)
        {
            Model = model;
            SystemLog = systemLog;
            User = user;
        }

        public class Handler : IRequestHandler<SaoChepChuongTrinhDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(SaoChepChuongTrinhDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var user = request.User;
                 var systemLog = request.SystemLog;

                Log.Information($"Copy {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<SaoChepChuongTrinhDaoTaoModel, SvChuongTrinhDaoTao>(model);

                var checkCode = await _dataContext.SvChuongTrinhDaoTaos.AnyAsync(x => x.IdHe == entity.IdHe && x.IdKhoa == entity.IdKhoa && x.IdChuyenNganh == entity.IdChuyenNganh && x.So == entity.So);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChuongTrinhDaoTao.Existed", JsonSerializer.Serialize(model)]}");
                }

                await _dataContext.SvChuongTrinhDaoTaos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();            

                var idDtMoi = entity.IdDt;

                var hocPhanCuTable = await _dataContext.SvChuongTrinhDaoTaoChiTiets
                    .Where(x => x.IdDt == model.IdDtCu)
                    .ToListAsync();

                var hocPhanMoi = hocPhanCuTable.Select(hocPhanCu => new SaoChepChuongTrinhDaoTaoChiTietModel
                {
                    IdDt = idDtMoi,
                    IdMon = hocPhanCu.IdMon,
                    KyThu = hocPhanCu.KyThu,
                    SoHocTrinh = hocPhanCu.SoHocTrinh,
                    LyThuyet = hocPhanCu.LyThuyet,
                    ThucHanh = hocPhanCu.ThucHanh,
                    BaiTap = hocPhanCu.BaiTap,
                    BaiTapLon = hocPhanCu.BaiTapLon,
                    ThucTap = hocPhanCu.ThucTap,
                    TuChon = hocPhanCu.TuChon,
                    SttMon = hocPhanCu.SttMon,
                    HeSo = hocPhanCu.HeSo,
                    KienThuc = hocPhanCu.KienThuc,
                    KhongTinhTBCHT = hocPhanCu.KhongTinhTbcht,
                    NhomTuChon = hocPhanCu.NhomTuChon,
                    TuHoc = hocPhanCu.TuHoc,
                    SoTinChiTienQuyet = hocPhanCu.SoTinChiTienQuyet,
                    MaKhoaPhuTrach = hocPhanCu.MaKhoaPhuTrach,
                    MonThucHanh = hocPhanCu.MonThucHanh,
                    MonTotNghiep = hocPhanCu.MonTotNghiep,
                    MonDkTn = hocPhanCu.MonDkTn,
                    MonKhoaLuan = false,
                    SoHocTrinhThucHanh = 0,
                    MonLyThuyetThucHanh = false,
                    ModifyDate = DateTime.Now,
                    ModifyUserName = user,
                    DiemTbchpDat = 0,
                    DiemThiDat = 0


                }).ToList();

                foreach (var hocPhan in hocPhanMoi)
                {
                    var newHocPhan = AutoMapperUtils.AutoMap<SaoChepChuongTrinhDaoTaoChiTietModel, SvChuongTrinhDaoTaoChiTiet>(hocPhan);
                    await _dataContext.SvChuongTrinhDaoTaoChiTiets.AddAsync(newHocPhan);
                }

                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Sao chép chương trình đào tạo: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(entity.IdDt)
                });
                
                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class ThayTheHocPhanBangDiemCommand : IRequest<Unit>
    {
        public ThayTheHocPhanBangDiemModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thay thế học phần trong bảng điểm
        /// </summary>
        public ThayTheHocPhanBangDiemCommand(ThayTheHocPhanBangDiemModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<ThayTheHocPhanBangDiemCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(ThayTheHocPhanBangDiemCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Copy {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));
                    var sinhVienIds = await _dataContext.SvDanhSachs
                        .Where(ds => model.ListIdLop.Contains(ds.IdLop))
                        .Select(ds => ds.IdSv)
                        .ToListAsync();

                    if (!sinhVienIds.Any())
                    {
                        throw new ArgumentException($"{_localizer["Không có sinh viên nào trong lớp này"]}");
                    }

                    var monCu = await _dataContext.SvDiems.FirstOrDefaultAsync(diem => sinhVienIds.Contains(diem.IdSv) && diem.IdMon == model.IdMonCu);
                    if (monCu == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }

                    var monMoi = await _dataContext.SvDiems.FirstOrDefaultAsync(diem => sinhVienIds.Contains(diem.IdSv) && diem.IdMon == model.IdMonMoi);
                    if (monMoi != null)
                    {
                        throw new ArgumentException($"{_localizer["Môn mới đã có điểm"]}");
                    }

                    var affectedRows = await _dataContext.SvDiems
                        .Where(diem => sinhVienIds.Contains(diem.IdSv) && diem.IdMon == model.IdMonCu)
                        .ExecuteUpdateAsync(diem => diem.SetProperty(d => d.IdMon, model.IdMonMoi));

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thay thế học phần trong bảng điểm: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(model.IdMonCu)
                });
                
                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class GanChuongTrinhDaoTaoCommand : IRequest<Unit>
    {
        public List<GanChuongTrinhDaoTaoModel> Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Gán chương trình đào tạo
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        public GanChuongTrinhDaoTaoCommand(List<GanChuongTrinhDaoTaoModel> model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GanChuongTrinhDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(GanChuongTrinhDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Update {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                foreach (var item in model)
                {
                    var entity = await _dataContext.SvLops.FirstOrDefaultAsync(dt => dt.IdLop == item.IdLop);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }


                    item.UpdateEntity(entity);

                    _dataContext.SvLops.Update(entity);
                   
                }

                var listIdLop = model.Select(x=> x.IdLop).ToList();

                Log.Information($"After Update {ChuongTrinhDaoTaoConstant.CachePrefix}: {JsonSerializer.Serialize(listIdLop)}");

                Log.Information($"Update {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));
                await _dataContext.SaveChangesAsync();



                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Gán chương trình đào tạo: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(listIdLop)
                });
                return Unit.Value;
            }
        }
    }

    public class CreateChuongTrinhDaoTaoChiTietCommand : IRequest<Unit>
    {
        public List<CreateChuongTrinhDaoTaoChiTietModel> Request { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public string User { get; set; }

        /// <summary>
        /// Thêm mới chương trình đào tạo chi tiết
        /// </summary>
        public CreateChuongTrinhDaoTaoChiTietCommand(List<CreateChuongTrinhDaoTaoChiTietModel> request, SystemLogModel systemLog, string user)
        {
            Request = request;
            SystemLog = systemLog;
            User = user;
        }

        public class Handler : IRequestHandler<CreateChuongTrinhDaoTaoChiTietCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateChuongTrinhDaoTaoChiTietCommand request, CancellationToken cancellationToken)
            {
                var model = request.Request;
                var systemLog = request.SystemLog;
                var user = request.User;

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entitiesToAdd = new List<SvChuongTrinhDaoTaoChiTiet>();

                foreach (var item in model)
                {
                    var entity = AutoMapperUtils.AutoMap<CreateChuongTrinhDaoTaoChiTietModel, SvChuongTrinhDaoTaoChiTiet>(item);

                    var checkCode = await _dataContext.SvChuongTrinhDaoTaoChiTiets
                        .AnyAsync(x => x.IdDt == entity.IdDt && x.IdMon == entity.IdMon, cancellationToken);
                    if (!checkCode)
                    {
                        entity.IdDtMon = 0;
                        entity.KyThu = 0;
                        entity.SoHocTrinh = 0;
                        entity.LyThuyet = 0;
                        entity.ThucHanh = 0;
                        entity.BaiTap = 0;
                        entity.BaiTapLon = 0;
                        entity.ThucTap = 0;
                        entity.TuChon = false;
                        entity.SttMon = 0;
                        entity.HeSo = 0;
                        entity.KienThuc = 0;
                        entity.KhongTinhTbcht = false;
                        entity.NhomTuChon = 0;
                        entity.TuHoc = 0;
                        entity.SoTinChiTienQuyet = 0;
                        entity.MaKhoaPhuTrach = string.Empty;
                        entity.MonThucHanh = false;
                        entity.MonTotNghiep = false;
                        entity.MonDkTn = false;
                        entity.MonKhoaLuan = false;
                        entity.SoHocTrinhThucHanh = 0;
                        entity.MonLyThuyetThucHanh = false;
                        entity.ModifyDate = DateTime.Now;
                        entity.DiemTbchpDat = 0;
                        entity.DiemThiDat = 0;
                        entity.ModifyUserName = string.Empty;
                        entity.ThangNhapDiem = 0;
                        entity.MaKhoaPhuTrach = string.Empty;
                        entity.ModifyUserName = user;

                        entitiesToAdd.Add(entity);
                    }
                }

                await _dataContext.SvChuongTrinhDaoTaoChiTiets.AddRangeAsync(entitiesToAdd, cancellationToken);

                await _dataContext.SaveChangesAsync(cancellationToken);
                var listIdMon = model.Select(x => x.IdMon).ToList();
                systemLog.ListAction.Add(new ActionDetail
                {
                    Description = $"Thêm mới chương trình đào tạo chi tiết: {JsonSerializer.Serialize(listIdMon)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(listIdMon)
                });

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());

                return Unit.Value;
            }

        }
    }

    public class ImportChuongTrinhDaoTaoCommand : IRequest<Unit>
    {
        public ImportChuongTrinhDaoTaoModel Request { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Import chương trình đào tạo
        /// </summary>
        public ImportChuongTrinhDaoTaoCommand(ImportChuongTrinhDaoTaoModel request, SystemLogModel systemLog)
        {
            Request = request;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<ImportChuongTrinhDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(ImportChuongTrinhDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Request;
                var systemLog = request.SystemLog;

                var entity = AutoMapperUtils.AutoMap<CreateChuongTrinhDaoTaoModel, SvChuongTrinhDaoTao>(model);
                var listIdMon = model.ListDaoTaoChiTiet
                               .Select(x => x.IdMon)
                               .ToList();

                // Check data duplicate
                if (listIdMon.Count() != listIdMon.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                var checkCode = await _dataContext.SvChuongTrinhDaoTaos.AnyAsync(x => x.IdHe == entity.IdHe && x.IdKhoa == entity.IdKhoa && x.IdChuyenNganh == entity.IdChuyenNganh && x.So == entity.So);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChuongTrinhDaoTao.Existed", JsonSerializer.Serialize(model)]}");
                }

                await _dataContext.SvChuongTrinhDaoTaos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                foreach(var item in model.ListDaoTaoChiTiet)
                {
                    item.IdDt = entity.IdDt;
                }

                var listEntity = model.ListDaoTaoChiTiet.Select(x => new SvChuongTrinhDaoTaoChiTiet()
                {
                    IdDt = x.IdDt,
                    IdMon = x.IdMon,
                    KyThu = x.KyThu,
                    SoHocTrinh = x.SoHocTrinh,
                    LyThuyet = x.LyThuyet,
                    ThucHanh = x.ThucHanh,
                    BaiTap = x.BaiTap,
                    BaiTapLon = x.BaiTapLon,
                    ThucTap = x.ThucTap,
                    TuChon = x.TuChon,
                    SttMon = x.SttMon,
                    HeSo = x.HeSo,
                    KienThuc = x.KienThuc,
                    KhongTinhTbcht = x.KhongTinhTBCHT,
                    NhomTuChon = x.NhomTuChon,
                    TuHoc = x.TuHoc,
                    SoTinChiTienQuyet = x.SoTinChiTienQuyet,
                    MaKhoaPhuTrach = x.MaKhoaPhuTrach,
                    MonThucHanh = x.MonThucHanh,
                    MonTotNghiep = x.MonTotNghiep,
                    MonKhoaLuan = false,
                    DiemThiDat = x.DiemThiDat,
                    DiemTbchpDat = x.DiemTbchpDat,
                    ModifyDate = DateTime.Now,
                    MonDkTn = x.MonDkTn
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();
             
                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import CTĐT: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(entity.IdDt)
                });

                return Unit.Value;
            }
        }
    }

    public class UpdateChuongTrinhDaoTaoChiTietCommand : IRequest<Unit>
    {
        public List<UpdateChuongTrinhDaoTaoChiTietModel> Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public string User { get; set; }

        /// <summary>
        /// update chương trình đào tạo chi tiết
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        public UpdateChuongTrinhDaoTaoChiTietCommand(List<UpdateChuongTrinhDaoTaoChiTietModel> model, SystemLogModel systemLog, string user)
        {
            Model = model;
            SystemLog = systemLog;
            User = user;
        }

        public class Handler : IRequestHandler<UpdateChuongTrinhDaoTaoChiTietCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdateChuongTrinhDaoTaoChiTietCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                var user = request.User;

                Log.Information($"Update {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));
                var listUpdate = new List<UpdateChuongTrinhDaoTaoChiTietModel>();
                foreach (var item in model)
                {
                    var entity = await _dataContext.SvChuongTrinhDaoTaoChiTiets.FirstOrDefaultAsync(dt => dt.IdDtMon == item.IdDtMon);
                    if (!(entity == null))
                    {
                        var checkCode = await _dataContext.SvChuongTrinhDaoTaoChiTiets.AnyAsync(x => x.IdDtMon != item.IdDtMon && x.IdDt == item.IdDt && x.IdMon == item.IdMon);
                        if (!checkCode)
                        {
                            entity.ModifyUserName = user;
                            item.UpdateEntity(entity);
                            _dataContext.Entry(entity).Property(e => e.IdDtMon).IsModified = false;
                          
                        }
                    }
                }
                var listIdDtMon = model.Select(x => x.IdDtMon).ToList();
                Log.Information($"Before Update {NganhConstant.CachePrefix}: {JsonSerializer.Serialize(listIdDtMon)}");
                await _dataContext.SaveChangesAsync();



                Log.Information($"After Update {NganhConstant.CachePrefix}: {JsonSerializer.Serialize(listIdDtMon)}");

                Log.Information($"Update {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật chương trình đào tạo chi tiết: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(listIdDtMon)
                });

                return Unit.Value;
            }
        }
    }

    public class DeleteChuongTrinhDaoTaoChiTietCommand : IRequest<Unit>
    {
        public List<DeleteChuongTrinhDaoTaoChiTiet> Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa chương trình đào tạo chi tiết
        /// </summary>
        public DeleteChuongTrinhDaoTaoChiTietCommand(List<DeleteChuongTrinhDaoTaoChiTiet> model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteChuongTrinhDaoTaoChiTietCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private ICallStoreHelper _callStoreHelper;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, ICallStoreHelper callStoreHelper)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _callStoreHelper = callStoreHelper;
            }

            public async Task<Unit> Handle(DeleteChuongTrinhDaoTaoChiTietCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var monCoDiem = new List<int>();
                foreach (var item in model)
                {
                    var m = await _dataContext.SvChuongTrinhDaoTaoChiTiets.FirstOrDefaultAsync(x => x.IdDtMon == item.IdDtMon);
                    var checkExit = _callStoreHelper.CallStoreCheckDiemCtdtAsync(item.IdDt, item.IdMon);
                    if (checkExit != null && checkExit.Rows.Count > 0)
                    {
                        var checkValue = Convert.ToInt32(checkExit.Rows[0][0]);
                        if (checkValue > 0)
                        {
                            monCoDiem.Add(item.IdDtMon);
                        }
                    }
                }
                if (monCoDiem.Count > 0)
                {
                    var monCoDiemStr = string.Join(", ", monCoDiem);

                    // Thông báo danh sách các môn
                    throw new ArgumentException($"Những môn đã có điểm: {monCoDiemStr}");
                }

                foreach (var item in model)
                {
                    var entity = await _dataContext.SvChuongTrinhDaoTaoChiTiets.FirstOrDefaultAsync(x => x.IdDtMon == item.IdDtMon);

                    _dataContext.SvChuongTrinhDaoTaoChiTiets.Remove(entity);

                    Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(item.IdDtMon));

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa chương trình đào tạo chi tiết: {JsonSerializer.Serialize(item.IdDtMon)}",
                        ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(item.IdDtMon)
                    });

                    await _dataContext.SaveChangesAsync();
                    _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                    _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey(item.IdDtMon.ToString()));
                }

                return Unit.Value;
            }
        }
    }

    public class AddDiemCtChuongTrinhDaoTaoCommand : IRequest<Unit>
    {
        public AddDiemCtChuongTrinhDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm Công thức tính điểm vào học phần
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        public AddDiemCtChuongTrinhDaoTaoCommand(AddDiemCtChuongTrinhDaoTaoModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<AddDiemCtChuongTrinhDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(AddDiemCtChuongTrinhDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Update {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));
                foreach(var idDtMon in model.IdDtMons)
                {
                    var entity = await _dataContext.SvChuongTrinhDaoTaoChiTiets.FirstOrDefaultAsync(dt => dt.IdDtMon == idDtMon);
                    if (entity == null)
                    {
                        throw new ArgumentException($"{_localizer["data.not-found"]}");
                    }


                    Log.Information($"Before Update {NganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    model.UpdateEntity(entity);
                    _dataContext.Entry(entity).Property(e => e.IdDtMon).IsModified = false;
                    //_dataContext.SvChuongTrinhDaoTaoChiTiets.Update(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"After Update {NganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                    Log.Information($"Update {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Cập nhật chương trình đào tạo chi tiết: {JsonSerializer.Serialize(model)}",
                        ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(idDtMon)
                    });

                    //Xóa cache
                    _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey(idDtMon.ToString()));
                    _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());

                }
               

              
                return Unit.Value;
            }
        }
    }

    public class ThemRangBuocHocPhanCommand : IRequest<Unit>
    {
        public ThemRangBuocHocPhanModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới chương trình đào tạo
        /// </summary>
        public ThemRangBuocHocPhanCommand(ThemRangBuocHocPhanModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<ThemRangBuocHocPhanCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(ThemRangBuocHocPhanCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<ThemRangBuocHocPhanModel, SvChuongTrinhDaoTaoRangBuoc>(model);

                var checkCode = await _dataContext.SvChuongTrinhDaoTaoRangBuocs.AnyAsync(x => x.IdMon == entity.IdMon && x.IdDt == entity.IdDt && x.IdMonRb == entity.IdMonRb);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["RangBuocHocPhan.Existed", JsonSerializer.Serialize(model)]}");
                }

                await _dataContext.SvChuongTrinhDaoTaoRangBuocs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $" Thêm ràng buộc học phần: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(entity.IdMon)
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteRangBuocHocPhanCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa chương trình đào tạo
        /// </summary>
        public DeleteRangBuocHocPhanCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteRangBuocHocPhanCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteRangBuocHocPhanCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;

                Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvChuongTrinhDaoTaoRangBuocs.FirstOrDefaultAsync(x => x.IdRb == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                _dataContext.SvChuongTrinhDaoTaoRangBuocs.Remove(entity);

                Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(id));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa ràng buộc học phần: {JsonSerializer.Serialize(id)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(id)
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }

    public class ThietLapHocPhanTuongDuongCommand : IRequest<Unit>
    {
        public ThietLapHocPhanTuongDuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới học phần tương đương
        /// </summary>
        public ThietLapHocPhanTuongDuongCommand(ThietLapHocPhanTuongDuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<ThietLapHocPhanTuongDuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(ThietLapHocPhanTuongDuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<ThietLapHocPhanTuongDuongModel, SvMonHocTuongDuong>(model);

                var checkCode = await _dataContext.SvMonHocTuongDuongs.AnyAsync(x => x.IdMon == entity.IdMon && x.IdMon1 == entity.IdMon1);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["MonHocTuongDuong.Existed", JsonSerializer.Serialize(model)]}");
                }

                await _dataContext.SvMonHocTuongDuongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thiết lập môn học tương đương: {JsonSerializer.Serialize(model)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(entity.IdMon)
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteHocPhanTuongDuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa Học phần tương đương
        /// </summary>
        public DeleteHocPhanTuongDuongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteHocPhanTuongDuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteHocPhanTuongDuongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;

                Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(id));
                var entity = await _dataContext.SvMonHocTuongDuongs.FirstOrDefaultAsync(x => x.IdMonTuongDuong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                _dataContext.SvMonHocTuongDuongs.Remove(entity);

                Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(id));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa môn học tương đương: {JsonSerializer.Serialize(id)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(id)
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey());
                _cacheService.Remove(ChuongTrinhDaoTaoConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }

    public class CreateMonChungChiCommand : IRequest<Unit>
    {
        public CreateManyMonChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới môn chứng chỉ
        /// </summary>
        public CreateMonChungChiCommand(CreateManyMonChungChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateMonChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateMonChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                foreach (var item in model.ListChungChi) 
                {
                    Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateMonChungChiModel, SvLoaiChungChiDanhSachMon>(item);

                    var checkCode = await _dataContext.SvLoaiChungChiDanhSachMons.AnyAsync(x => x.IdChungChi == entity.IdChungChi && x.IdDt == entity.IdDt && x.IdMon == entity.IdMon);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["MonChungChi.Existed", JsonSerializer.Serialize(model)]}");
                    }

                    await _dataContext.SvLoaiChungChiDanhSachMons.AddAsync(entity);
                    Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới môn chứng chỉ: {JsonSerializer.Serialize(model)}",
                        ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(entity.IdDt)
                    });
                }
              
                await _dataContext.SaveChangesAsync();

            

                return Unit.Value;
            }
        }
    }

    public class DeleteMonChungChiCommand : IRequest<Unit>
    {
        public CreateManyMonChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới môn chứng chỉ
        /// </summary>
        public DeleteMonChungChiCommand(CreateManyMonChungChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteMonChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteMonChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                foreach(var item in model.ListChungChi)
                {
                    Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = await _dataContext.SvLoaiChungChiDanhSachMons.FirstOrDefaultAsync(x => x.IdChungChi == item.IdChungChi && x.IdDt == item.IdDt && x.IdMon == item.IdMon);

                    _dataContext.SvLoaiChungChiDanhSachMons.Remove(entity);
                   
                    Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa môn chứng chỉ: {JsonSerializer.Serialize(model)}",
                        ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(entity)
                    });
                }
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }

    public class CreateMonTuChonCommand : IRequest<Unit>
    {
        public CreateMonTuChonModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới môn tự chọn
        /// </summary>
        public CreateMonTuChonCommand(CreateMonTuChonModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateMonTuChonCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateMonTuChonCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                    Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateMonTuChonModel, SvChuongTrinhDaoTaoNhomTuChon>(model);

                    var checkCode = await _dataContext.SvChuongTrinhDaoTaoNhomTuChons.AnyAsync(x => x.IdDt == entity.IdDt && x.NhomTuChon == entity.NhomTuChon);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["MonTuChon.Existed", JsonSerializer.Serialize(model)]}");
                    }

                    await _dataContext.SvChuongTrinhDaoTaoNhomTuChons.AddAsync(entity);
                    Log.Information($"Create {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới môn tự chọn: {JsonSerializer.Serialize(model)}",
                        ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(entity.IdDt)
                    });

                await _dataContext.SaveChangesAsync();



                return Unit.Value;
            }
        }
    }

    public class DeleteMonTuChonCommand : IRequest<Unit>
    {
        public DeleteMonTuChonModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa môn tự chọn
        /// </summary>
        public DeleteMonTuChonCommand(DeleteMonTuChonModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteMonTuChonCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteMonTuChonCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                    Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = await _dataContext.SvChuongTrinhDaoTaoNhomTuChons.FirstOrDefaultAsync(x => x.IdDt == model.IdDt && x.NhomTuChon == model.NhomTuChon);

                    _dataContext.SvChuongTrinhDaoTaoNhomTuChons.Remove(entity);

                    Log.Information($"Delete {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa môn tự chọn: {JsonSerializer.Serialize(model)}",
                        ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(entity)
                    });
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }

    public class CongThucTinhDiemCommand : IRequest<string>
    {
        public ListDiemModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Test công thức điểm
        /// </summary>
        public CongThucTinhDiemCommand(ListDiemModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CongThucTinhDiemCommand, string>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<string> Handle(CongThucTinhDiemCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;

                Log.Information($"TestDiem {ChuongTrinhDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));
                var diems = new List<object>();
                var values = new Dictionary<string, object>();
                foreach (var item in model.ListDiem)
                {
                    values.Add(item.TenDiem, item.SoDiem);
                }
                string ketQua = CongThucDiem.TinhCongThuc(model.CongThuc, values);
                string result = ketQua ?? "-1";


                Log.Information($"TestDiem {ChuongTrinhDaoTaoConstant.CachePrefix} success: " + JsonSerializer.Serialize(model));

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Test công thức điểm {JsonSerializer.Serialize(model.CongThuc)}",
                    ObjectCode = ChuongTrinhDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(model.CongThuc)
                });

                await _dataContext.SaveChangesAsync();



                return result;
            }
        }
    }

}


