using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading; 
using System.Threading.Tasks;
namespace Core.Business
{
    public class GetComboboxSoCtdtQuery : IRequest<List<SoCtdtSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy hệ cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxSoCtdtQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxSoCtdtQuery, List<SoCtdtSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<SoCtdtSelectItemModel>> Handle(GetComboboxSoCtdtQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = ChuongTrinhDaoTaoConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = _dataContext.SvChuongTrinhDaoTaos
                                .GroupBy(x => x.So)
                                .Select(group => new SoCtdtSelectItemModel()
                                {
                                So = group.Key 
                                }).OrderBy(item => item.So) 
                                .ToList();

                    return  data;
                });


                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetChuongTrinhDaoTaoQuery : IRequest<List<ChuongTrinhDaoTaoModel>>
    {
        public SystemLogModel SystemLog { get; set; }
        /// <summary>
        /// Get danh sách chương trình đào tạo
        /// </summary>
        public GetChuongTrinhDaoTaoQuery(SystemLogModel systemLog)
        {
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetChuongTrinhDaoTaoQuery, List<ChuongTrinhDaoTaoModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<ChuongTrinhDaoTaoModel>> Handle(GetChuongTrinhDaoTaoQuery request, CancellationToken cancellationToken)
            {
                var userName = request.SystemLog.UserName;
                List<int> idHeAccessUser = (from access in _dataContext.HtUsersAccessHes
                                            join user in _dataContext.HtUsers on access.UserId equals user.UserId
                                            where user.UserName == userName
                                            select access.IdHe).ToList();

                var data = (from dt in _dataContext.SvChuongTrinhDaoTaos
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            join khoa in _dataContext.SvKhoas on dt.IdKhoa equals khoa.IdKhoa
                            join cn in _dataContext.SvChuyenNganhs on dt.IdChuyenNganh equals cn.IdChuyenNganh
                            join nganh in _dataContext.SvNganhs on cn.IdNganh equals nganh.IdNganh
                            where (idHeAccessUser.Contains(0) || idHeAccessUser.Contains(dt.IdHe))
                            orderby he.TenHe, khoa.TenKhoa, dt.KhoaHoc, cn.ChuyenNganh
                            select new ChuongTrinhDaoTaoModel
                            {
                                IdDt = dt.IdDt,
                                IdHe = dt.IdHe,
                                IdKhoa = dt.IdKhoa,
                                KhoaHoc = dt.KhoaHoc,
                                IdChuyenNganh = dt.IdChuyenNganh,
                                SoTinChi = dt.SoTinChi,
                                SoKyHoc = dt.SoKyHoc,
                                So = dt.So,
                                SoTinChiDinhMuc = dt.SoTinChiDinhMuc,
                                SoQuyetDinhCtdt = dt.SoQuyetDinhCtdt,
                                SoNamDaoTao = dt.SoNamDaoTao,
                                ChiTietQuyetDinhCtdt = dt.ChiTietQuyetDinhCtdt,
                                TenHe = he.TenHe,
                                TenKhoa = khoa.TenKhoa,
                                TenNganh = nganh.TenNganh,
                                ChuyenNganh = cn.ChuyenNganh,
                                TenChuyenNganh = cn.ChuyenNganh,
                            });

                var listData = await data.ToListAsync();

                return listData;
            }
        }
    }

    public class GetCongThucTinhDiemHocPhanQuery : IRequest<List<CongThucTinhDiemHocPhanModel>>
    {
        public CongThucDiemHocPhanBaseModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        /// <summary>
        /// Get danh sách học phần và hiển thị công thức điểm học phần
        /// </summary>
        public GetCongThucTinhDiemHocPhanQuery(CongThucDiemHocPhanBaseModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetCongThucTinhDiemHocPhanQuery, List<CongThucTinhDiemHocPhanModel>>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<CongThucTinhDiemHocPhanModel>> Handle(GetCongThucTinhDiemHocPhanQuery request, CancellationToken cancellationToken)
            {
                var model = request.Model;

                var data = (from dt in _dataContext.SvChuongTrinhDaoTaos
                            join dtct in _dataContext.SvChuongTrinhDaoTaoChiTiets on dt.IdDt equals dtct.IdDt
                            join mh in _dataContext.SvMonHocs on dtct.IdMon equals mh.IdMonHoc
                            join dctGroup in _dataContext.SvDiemCongThucs
                            on dtct.IdCongThuc equals dctGroup.IdCongThuc into dctLeftJoin
                            from dct in dctLeftJoin.DefaultIfEmpty()
                            where dt.IdHe == model.IdHe 
                            && dtct.SoHocTrinh > 0                            
                            && (dt.IdKhoa == model.IdKhoa || model.IdKhoa == 0) 
                            && (dt.KhoaHoc == model.KhoaHoc || model.KhoaHoc == 0) 
                            && (dt.IdChuyenNganh == model.IdChuyenNganh || model.IdChuyenNganh == 0)
                            orderby mh.TenMon
                            select new CongThucTinhDiemHocPhanModel
                            {
                                IdDt = dt.IdDt,
                                IdDtMon = dtct.IdDtMon,
                                KyHieu = mh.KyHieu,
                                TenMon = mh.TenMon,
                                IdMon = mh.IdMonHoc,
                                IdHe = dt.IdHe,
                                IdKhoa = dt.IdKhoa,
                                KhoaHoc = dt.KhoaHoc,
                                IdChuyenNganh = dt.IdChuyenNganh,
                                SoHocTrinh = dtct.SoHocTrinh,
                                IdCongThuc = dtct.IdCongThuc,
                                TenCongThuc = dct.TenCongThuc,
                                CongThucTinhDiemTBCHP = dct.CongThucTinhDiemTBCHP,
                                MonThucHanh = dtct.MonThucHanh,
                                So = dt.So,
                                MonLyThuyetThucHanh = dtct.MonLyThuyetThucHanh
                            });

                var listData = await data.ToListAsync();

                return listData;
            }
        }
    }

    public class GetHocPhanTheoTinChiQuery : IRequest<List<HocPhanTheoTinChiModel>>
    {
        public SystemLogModel SystemLog { get; set; }
        /// <summary>
        /// Get danh học phần có hiển thị tín chỉ
        /// </summary>
        public GetHocPhanTheoTinChiQuery(SystemLogModel systemLog)
        {
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetHocPhanTheoTinChiQuery, List<HocPhanTheoTinChiModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<HocPhanTheoTinChiModel>> Handle(GetHocPhanTheoTinChiQuery request, CancellationToken cancellationToken)
            {
                var listData = new List<HocPhanTheoTinChiModel>();

                var data = (from mon in _dataContext.SvMonHocs
                            join dtct in _dataContext.SvChuongTrinhDaoTaoChiTiets
                             on mon.IdMonHoc equals dtct.IdMon into dtctGroup
                            from dtct in dtctGroup.DefaultIfEmpty()
                            orderby mon.TenMon
                            select new HocPhanTheoTinChiModel
                            {
                                IdMon = mon.IdMonHoc,
                                TenMon = mon.TenMon,
                                KyHieu = mon.KyHieu,
                                SoHocTrinh = dtct.SoHocTrinh

                            }).GroupBy(x => new { x.IdMon, x.TenMon, x.SoHocTrinh })
                            .Select(g => g.First());

                listData = await data.ToListAsync();
                return listData;
            }
        }
    }

    public class GetChuongTrinhDaoTaoChiTietQuery : IRequest<List<ChuongTrinhDaoTaoChiTietModel>>
    {
        public int IdDt { get; set; }
        /// <summary>
        /// Get danh sách chương trình đào tạo chi tiết
        /// </summary>
        public GetChuongTrinhDaoTaoChiTietQuery(int idDt)
        {
            IdDt = idDt;
        }

        public class Handler : IRequestHandler<GetChuongTrinhDaoTaoChiTietQuery, List<ChuongTrinhDaoTaoChiTietModel>>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<ChuongTrinhDaoTaoChiTietModel>> Handle(GetChuongTrinhDaoTaoChiTietQuery request, CancellationToken cancellationToken)
            {
                var idDt = request.IdDt;

                var data = (from dt in _dataContext.SvChuongTrinhDaoTaoChiTiets
                            join mh in _dataContext.SvMonHocs on dt.IdMon equals mh.IdMonHoc
                            join dtkt in _dataContext.SvChuongTrinhDaoTaoKienThucs on dt.KienThuc equals dtkt.IdKienThuc into dtktGroup
                            from dtkt in dtktGroup.DefaultIfEmpty()
                            where dt.IdDt == idDt
                            orderby dt.KyThu, dt.SttMon
                            select new ChuongTrinhDaoTaoChiTietModel
                            {
                                IdDt = dt.IdDt,
                                KyHieu = mh.KyHieu,
                                TenMon = mh.TenMon,
                                IdDtMon = dt.IdDtMon,
                                IdMon = dt.IdMon,
                                KyThu = dt.KyThu,
                                SoHocTrinh = dt.SoHocTrinh,
                                LyThuyet = dt.LyThuyet,
                                ThucHanh = dt.ThucHanh,
                                BaiTap = dt.BaiTap,
                                BaiTapLon = dt.BaiTapLon,
                                ThucTap = dt.ThucTap,
                                TuChon = dt.TuChon,
                                SttMon = dt.SttMon,
                                HeSo = dt.HeSo,
                                KienThuc = dt.KienThuc,
                                KhongTinhTbcht = dt.KhongTinhTbcht,
                                NhomTuChon = dt.NhomTuChon,
                                TuHoc = dt.TuHoc,
                                SoTinChiTienQuyet = dt.SoTinChiTienQuyet,
                                MaKhoaPhuTrach = dt.MaKhoaPhuTrach,
                                MonThucHanh = dt.MonThucHanh,
                                MonTotNghiep = dt.MonTotNghiep,
                                MonDkTn = dt.MonDkTn,
                                MonKhoaLuan = dt.MonKhoaLuan,
                                SoHocTrinhThucHanh = dt.SoHocTrinhThucHanh,
                                MonLyThuyetThucHanh = dt.MonLyThuyetThucHanh,
                                IdCongThuc = dt.IdCongThuc,
                                ModifyDate = dt.ModifyDate,
                                ModifyUserName = dt.ModifyUserName,
                                MienHocPhi = dt.MienHocPhi,
                                ThangNhapDiem = dt.ThangNhapDiem,
                                DiemTbchpDat = dt.DiemTbchpDat,
                                DiemThiDat = dt.DiemThiDat,
                                IdMonCha = dt.IdMonCha,
                                UrlDeCuongChiTiet = dt.UrlDeCuongChiTiet,
                                TenKienThuc = dtkt != null ? dtkt.TenKienThuc : ""
                            });

                var listData = await data.ToListAsync();

                return listData;
            }
        }
    }

    public class GetRangBuocHocPhanQuery : IRequest<List<RangBuocHocPhanModel>>
    {
        public SystemLogModel SystemLog { get; set; }
        public int IdDt { get; set; }
        /// <summary>
        /// Get danh sách học phần tương đương
        /// </summary>
        public GetRangBuocHocPhanQuery(int idDt, SystemLogModel systemLog)
        {
            SystemLog = systemLog;
            IdDt = idDt;
        }

        public class Handler : IRequestHandler<GetRangBuocHocPhanQuery, List<RangBuocHocPhanModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<RangBuocHocPhanModel>> Handle(GetRangBuocHocPhanQuery request, CancellationToken cancellationToken)
            {
                var listData = new List<RangBuocHocPhanModel>();
                var idDt = request.IdDt;

                var data = (from dtRb in _dataContext.SvChuongTrinhDaoTaoRangBuocs
                            join m in _dataContext.SvMonHocs on dtRb.IdMon equals m.IdMonHoc
                            join mRb in _dataContext.SvMonHocs on dtRb.IdMonRb equals mRb.IdMonHoc
                            join lrb in _dataContext.SvLoaiRangBuocs on dtRb.LoaiRangBuoc equals lrb.LoaiRangBuoc
                            where dtRb.IdDt == idDt
                            orderby m.TenMon
                            select new RangBuocHocPhanModel
                            {
                                IdRb = dtRb.IdRb,
                                IdMon = m.IdMonHoc,
                                TenMon = m.TenMon,
                                KyHieu = m.KyHieu,
                                LoaiRangBuoc = dtRb.LoaiRangBuoc,
                                TenRangBuoc = lrb.TenRangBuoc,
                                IdMonRb = mRb.IdMonHoc,
                                TenMonRb = mRb.TenMon,
                                KyHieuMonRb = mRb.KyHieu,

                            });

                listData = await data.ToListAsync();
                return listData;
            }
        }
    }

    public class GetHocPhanTuongDuongQuery : IRequest<List<HocPhanTuongDuongModel>>
    {
        public SystemLogModel SystemLog { get; set; }
        /// <summary>
        /// Get danh sách học phần tương đương
        /// </summary>
        public GetHocPhanTuongDuongQuery(SystemLogModel systemLog)
        {
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetHocPhanTuongDuongQuery, List<HocPhanTuongDuongModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<HocPhanTuongDuongModel>> Handle(GetHocPhanTuongDuongQuery request, CancellationToken cancellationToken)
            {
                var listData = new List<HocPhanTuongDuongModel>();

                var data = (from mhtd in _dataContext.SvMonHocTuongDuongs
                            join m in _dataContext.SvMonHocs on mhtd.IdMon equals m.IdMonHoc
                            join m1 in _dataContext.SvMonHocs on mhtd.IdMon1 equals m1.IdMonHoc
                            orderby m1.TenMon
                            select new HocPhanTuongDuongModel
                            {
                                IdMonTuongDuong = mhtd.IdMonTuongDuong,
                                IdMon = m.IdMonHoc,
                                TenMon = m.TenMon,
                                KyHieu = m.KyHieu,
                                SoTinChi = mhtd.SoTinChi,
                                IdMon1 = m1.IdMonHoc,
                                TenMon1 = m1.TenMon,
                                KyHieu1 = m1.KyHieu,
                                SoTinChi1 = mhtd.SoTinChi1,

                            });

                listData = await data.ToListAsync();
                return listData;
            }
        }
    }


    public class GetMonChungChiQuery : IRequest<List<MonHocChungChiModel>>
    {
        public SystemLogModel SystemLog { get; set; }
        public MonHocTheoHeFilterModel Model { get; set; }
        /// <summary>
        /// Get danh sách môn chứng chỉ
        /// </summary>
        public GetMonChungChiQuery(MonHocTheoHeFilterModel model, SystemLogModel systemLog)
        {
            SystemLog = systemLog;
            Model = model;
        }

        public class Handler : IRequestHandler<GetMonChungChiQuery, List<MonHocChungChiModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<MonHocChungChiModel>> Handle(GetMonChungChiQuery request, CancellationToken cancellationToken)
            {
                var listData = new List<MonHocChungChiModel>();
                var model = request.Model;

                var data = (from dsm in _dataContext.SvLoaiChungChiDanhSachMons
                            join ctdt in _dataContext.SvChuongTrinhDaoTaos on dsm.IdDt equals ctdt.IdDt
                            join he in _dataContext.SvHes on ctdt.IdHe equals he.IdHe
                            join khoa in _dataContext.SvKhoas on ctdt.IdKhoa equals khoa.IdKhoa
                            join cn in _dataContext.SvChuyenNganhs on ctdt.IdChuyenNganh equals cn.IdChuyenNganh
                            join lop in _dataContext.SvLops
                            on dsm.IdDt equals lop.IdDt into lopGroup 
                            from lop in lopGroup.DefaultIfEmpty()
                            join lcc in _dataContext.SvLoaiChungChis on dsm.IdChungChi equals lcc.IdChungChi
                            join mon in _dataContext.SvMonHocs on dsm.IdMon equals mon.IdMonHoc
                            where ctdt.IdHe == model.IdHe
                                && (ctdt.IdKhoa == model.IdKhoa || model.IdKhoa == 0)
                                && (ctdt.KhoaHoc == model.KhoaHoc || model.KhoaHoc == 0)
                                && (ctdt.IdChuyenNganh == model.IdChuyenNganh || model.IdChuyenNganh == 0)
                                && (lop.IdLop == model.IdLop || model.IdLop == 0)
                            orderby mon.TenMon
                            select new MonHocChungChiModel
                            {
                                IdDt = dsm.IdDt,
                                IdMon = mon.IdMonHoc,
                                TenMon = mon.TenMon,
                                KyHieu = mon.KyHieu,
                                IdHe = ctdt.IdHe,
                                IdKhoa = ctdt.IdKhoa,
                                KhoaHoc = ctdt.KhoaHoc,
                                IdChuyenNganh = ctdt.IdChuyenNganh,
                                CtdtSo = ctdt.So,
                                TenHe = he.TenHe,
                                TenKhoa = khoa.TenKhoa,
                                TenChuyenNganh = cn.ChuyenNganh,
                                TenLoaiChungChi = lcc.LoaiChungChi,
                                IdLoaiChungChi = lcc.IdChungChi,
                            }).GroupBy(x => new { x.IdHe, x.IdKhoa, x.IdChuyenNganh, x.KhoaHoc,x.IdMon })
                            .Select(g => g.First());

                listData = await data.ToListAsync();
                return listData;
            }
        }
    }


    public class GetMonHocTheoHeQuery : IRequest<List<MonHocTheoHeModel>>
    {
        public SystemLogModel SystemLog { get; set; }
        public MonHocTheoHeFilterModel Model { get; set; }
        /// <summary>
        /// Get danh sách môn chứng chỉ
        /// </summary>
        public GetMonHocTheoHeQuery(MonHocTheoHeFilterModel model, SystemLogModel systemLog)
        {
            SystemLog = systemLog;
            Model = model;
        }

        public class Handler : IRequestHandler<GetMonHocTheoHeQuery, List<MonHocTheoHeModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<MonHocTheoHeModel>> Handle(GetMonHocTheoHeQuery request, CancellationToken cancellationToken)
            {
                var listData = new List<MonHocTheoHeModel>();
                var model = request.Model;

                var data = (from dt in _dataContext.SvChuongTrinhDaoTaos
                            join dtct in _dataContext.SvChuongTrinhDaoTaoChiTiets on dt.IdDt equals dtct.IdDt
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            join khoa in _dataContext.SvKhoas on dt.IdKhoa equals khoa.IdKhoa
                            join cn in _dataContext.SvChuyenNganhs on dt.IdChuyenNganh equals cn.IdChuyenNganh
                            join lop in _dataContext.SvLops
                            on dt.IdDt equals lop.IdDt into lopGroup
                            from lop in lopGroup.DefaultIfEmpty()
                            join mon in _dataContext.SvMonHocs on dtct.IdMon equals mon.IdMonHoc
                            where dt.IdHe == model.IdHe
                               &&(dt.IdKhoa == model.IdKhoa || model.IdKhoa == 0)
                               && (dt.KhoaHoc == model.KhoaHoc || model.KhoaHoc == 0)
                               && (dt.IdChuyenNganh == model.IdChuyenNganh || model.IdChuyenNganh == 0)
                               && (lop.IdLop == model.IdLop || model.IdLop == 0)
                            orderby mon.TenMon
                            select new MonHocTheoHeModel
                            {
                                IdDt = dt.IdDt,
                                IdMon = mon.IdMonHoc,
                                TenMon = mon.TenMon,
                                IdHe = dt.IdHe,
                                IdKhoa = dt.IdKhoa,
                                KhoaHoc = dt.KhoaHoc,
                                IdChuyennganh = dt.IdChuyenNganh,
                                IdLop = lop.IdLop,
                                KyHieu = mon.KyHieu
                            }).GroupBy(x => new { x.IdMon, x.TenMon, x.KyHieu })
                            .Select(g => g.First());

                    listData = await data.ToListAsync();
                return listData;
            }
        }
    }


    public class GetLopQuery : IRequest<List<LopHocModel>>
    {
        public SystemLogModel SystemLog { get; set; }
        public LopHocFilterModel Model { get; set; }
        /// <summary>
        /// Get danh sách môn chứng chỉ
        /// </summary>
        public GetLopQuery(LopHocFilterModel model, SystemLogModel systemLog)
        {
            SystemLog = systemLog;
            Model = model;
        }

        public class Handler : IRequestHandler<GetLopQuery, List<LopHocModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<LopHocModel>> Handle(GetLopQuery request, CancellationToken cancellationToken)
            {
                var listData = new List<LopHocModel>();
                var model = request.Model;

                var data = (from dt in _dataContext.SvLops
                            where dt.IdHe == model.IdHe
                               && (dt.IdKhoa == model.IdKhoa || model.IdKhoa == 0)
                               && (dt.KhoaHoc == model.KhoaHoc || model.KhoaHoc == 0)
                               && (dt.IdChuyenNganh == model.IdChuyenNganh || model.IdChuyenNganh == 0)
                            select new LopHocModel
                            {
                                IdLop = dt.IdLop,
                                TenLop = dt.TenLop,
                                NienKhoa = dt.NienKhoa,
                                IdDt = dt.IdDt,
                                DaGan = dt.IdDt == model.IdDt ? true : false
                            });
 

                listData = await data.ToListAsync();

                return listData;
            }
        }
    }


    public class GetNhomTuChonQuery : IRequest<List<NhomTuChonModel>>
    {
        public NhomTuChonFilterModel Model { get; set; }
        /// <summary>
        /// Get danh sách môn tự chọn
        /// </summary>
        public GetNhomTuChonQuery(NhomTuChonFilterModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<GetNhomTuChonQuery, List<NhomTuChonModel>>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<NhomTuChonModel>> Handle(GetNhomTuChonQuery request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var listData = new List<NhomTuChonModel>();
                var data = (from dt in _dataContext.SvChuongTrinhDaoTaoChiTiets
                            join mh in _dataContext.SvMonHocs on dt.IdMon equals mh.IdMonHoc
                            join dtkt in _dataContext.SvChuongTrinhDaoTaoKienThucs on dt.KienThuc equals dtkt.IdKienThuc into dtktGroup
                            from dtkt in dtktGroup.DefaultIfEmpty()
                            join ntc in _dataContext.SvChuongTrinhDaoTaoNhomTuChons
                            on new { dt.IdDt, dt.NhomTuChon } equals new { ntc.IdDt, ntc.NhomTuChon } into ntcGroup
                            from ntc in ntcGroup.DefaultIfEmpty()

                            where dt.IdDt == model.IdDt && dt.NhomTuChon > 0
                            orderby dt.KyThu, dt.SttMon
                            select new NhomTuChonModel
                            {
                                IdDt = dt.IdDt,
                                KyHieu = mh.KyHieu,
                                TenMon = mh.TenMon,
                                IdDtMon = dt.IdDtMon,
                                IdMon = dt.IdMon,
                                SoHocTrinh = dt.SoHocTrinh,
                                KienThuc = dt.KienThuc,
                                NhomTuChon = dt.NhomTuChon,
                                SoMonTuChon = ntc.SoMonTuChon,
                                SoMonDangKy = ntc.SoMonDangKy,
                                SoMonDaChon = model.MonTuChon ? ntc.SoMonTuChon.ToString() + "/" + ntc.SoMonDangKy.ToString() : "",
                                TenKienThuc = dtkt != null ? dtkt.TenKienThuc : ""
                            });

                if (model.MonTuChon)
                {
                    data = data.Where(x => x.SoMonDangKy > 0);
                }
                else
                {
                    data = data.Where(x => !(x.SoMonDangKy > 0) || x.SoMonDangKy == null);
                }

                listData = await data.ToListAsync();

             
                return listData;
            }
        }
    }


    public class CheckImportChuongTrinhDaoTaoChiTietCommand : IRequest<List<ChuongTrinhDaoTaoChiTietModel>>
    {
        public CheckImportManyChuongTrinhDaoTaoChiTiet Request { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới chương trình đào tạo chi tiết
        /// </summary>
        public CheckImportChuongTrinhDaoTaoChiTietCommand(CheckImportManyChuongTrinhDaoTaoChiTiet request, SystemLogModel systemLog)
        {
            Request = request;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CheckImportChuongTrinhDaoTaoChiTietCommand, List<ChuongTrinhDaoTaoChiTietModel>>
        {
            private readonly IMediator _mediator;
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(IMediator mediator, SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _mediator = mediator;
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<List<ChuongTrinhDaoTaoChiTietModel>> Handle(CheckImportChuongTrinhDaoTaoChiTietCommand request, CancellationToken cancellationToken)
            {
                var model = request.Request;
                var systemLog = request.SystemLog;
                var data = new List<ChuongTrinhDaoTaoChiTietModel>();

                var danhSachMonHoc = await _mediator.Send(new GetComboboxMonHocQuery());
                
                foreach (var monHoc in danhSachMonHoc)
                {
                    monHoc.KyHieu = ChuanHoaChuoi.NormalizeString(monHoc.KyHieu);
                }

                var danhSachKienThuc = await _mediator.Send(new GetComboboxChuongTrinhDaoTaoKienThucQuery());

                foreach (var kienThuc in danhSachKienThuc)
                {
                    kienThuc.TenKienThuc = ChuanHoaChuoi.NormalizeString(kienThuc.TenKienThuc);
                }

                 data = model.CheckListDaoTaoChiTiet
                        .GroupBy(item => item.KyHieu)
                        .Select(group => group.First()) 
                        .Select(item => new ChuongTrinhDaoTaoChiTietModel
                        {
                            KyHieu = item.KyHieu,
                            TenMon = item.TenMon,
                            KyThu = item.KyThu,
                            SoHocTrinh = item.SoHocTrinh,
                            LyThuyet = item.LyThuyet,
                            ThucHanh = item.ThucHanh,
                            BaiTap = item.BaiTap,
                            BaiTapLon = item.BaiTapLon,
                            ThucTap = item.ThucTap,
                            TuChon = item.TuChon,
                            SttMon = item.SttMon,
                            HeSo = item.HeSo,
                            TenKienThuc = item.TenKienThuc,
                            KhongTinhTbcht = item.KhongTinhTBCHT,
                            NhomTuChon = item.NhomTuChon,
                            TuHoc = item.TuHoc,
                            SoTinChiTienQuyet = item.SoTinChiTienQuyet,
                            MaKhoaPhuTrach = item.MaKhoaPhuTrach,
                            MonThucHanh = item.MonThucHanh,
                            MonTotNghiep = item.MonTotNghiep,
                            MonDkTn = item.MonDkTn,
                            IdMon = 0,
                            KienThuc = 0,
                            ModifyDate = null,
                            ModifyUserName = null,
                            MienHocPhi = false,
                            ThangNhapDiem = 0,
                            DiemTbchpDat = null,
                            DiemThiDat = null,
                            IdMonCha = 0,
                            UrlDeCuongChiTiet = null,
                            MonKhoaLuan = null,
                            SoHocTrinhThucHanh = null,
                            MonLyThuyetThucHanh = null,
                            IdCongThuc = 0
                        }).ToList();

                foreach (var item in data)
                {
                    string normalizedTenKienThuc = ChuanHoaChuoi.NormalizeString(item.TenKienThuc);
                    string normalizedKyHieu = ChuanHoaChuoi.NormalizeString(item.KyHieu);

                    var kienThuc = danhSachKienThuc.FirstOrDefault(kt => kt.TenKienThuc == normalizedTenKienThuc);
                    item.KienThuc = kienThuc != null ? kienThuc.IdKienThuc : -1;

                    var monHoc = danhSachMonHoc.FirstOrDefault(mh => mh.KyHieu == normalizedKyHieu);
                    item.IdMon = monHoc != null ? monHoc.IdMonHoc : -1;

                    data = data.Where(x => x.IdMon >= 0 && x.KienThuc >= 0).ToList();
                }


                return data;
            }
        }
    }


    public class GetNhomTuChonComboboxQuery : IRequest<List<int>>
    {
        public SystemLogModel SystemLog { get; set; }
        /// <summary>
        /// Get combobox nhóm tự chọn
        /// </summary>
        public GetNhomTuChonComboboxQuery(SystemLogModel systemLog)
        {
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<GetNhomTuChonComboboxQuery, List<int>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, IMediator mediator)
            {
                _dataContext = dataContext;
                _mediator = mediator;
            }

            public async Task<List<int>> Handle(GetNhomTuChonComboboxQuery request, CancellationToken cancellationToken)
            {
                var listData = new List<int>();

                var data = (from dtct in _dataContext.SvChuongTrinhDaoTaoChiTiets
                            select dtct.NhomTuChon).Distinct();


                listData = await data.ToListAsync();
                return listData;
            }
        }
    }

    public class GetLoaiThanhPhanDiemCtdtQuery : IRequest<List<LoaiThanhPhanDiemSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Loại điểm thành phần cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetLoaiThanhPhanDiemCtdtQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetLoaiThanhPhanDiemCtdtQuery, List<LoaiThanhPhanDiemSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiThanhPhanDiemSelectItemModel>> Handle(GetLoaiThanhPhanDiemCtdtQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiThanhPhanDiemConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvThanhPhanMons.OrderBy(x => x.TenThanhPhan)
                                select new LoaiThanhPhanDiemSelectItemModel()
                                {
                                    IdThanhPhan = dt.IdThanhPhan,
                                    KyHieu = dt.KyHieu,
                                    TenThanhPhan = dt.TenThanhPhan,
                                    TyLe = dt.TyLe
                                });

                    return await data.ToListAsync();
                });
                list.Add(new LoaiThanhPhanDiemSelectItemModel
                {
                    KyHieu = "DiemThiLT",
                    TenThanhPhan = "Điểm thi LT"
                });
                list.Add(new LoaiThanhPhanDiemSelectItemModel
                {
                    KyHieu = "So_hoc_trinh_ly_thuyet",
                    TenThanhPhan = "Số học trình điểm LT"
                });
                list.Add(new LoaiThanhPhanDiemSelectItemModel
                {
                    KyHieu = "DiemThiTH",
                    TenThanhPhan = "Điểm thi TH"
                });
                list.Add(new LoaiThanhPhanDiemSelectItemModel
                {
                    KyHieu = "So_hoc_trinh_thuc_hanh",
                    TenThanhPhan = "Số học trình điểm TH"
                });
                list.Add(new LoaiThanhPhanDiemSelectItemModel
                {
                    KyHieu = "So_hoc_trinh",
                    TenThanhPhan = "Số học trình điểm TH"
                });
                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenThanhPhan.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }


}
