

using Core.Data;
using Core.Shared;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Business
{
    public class ChuongTrinhDaoTaoModel
    {
        [DataColumn("ID_dt")]
        public int IdDt { get; set; }

        [DataColumn("ID_he")]
        public int IdHe { get; set; }

        [DataColumn("ID_khoa")]
        public int IdKhoa { get; set; }

        [DataColumn("Khoa_hoc")]
        public int KhoaHoc { get; set; }
        //public string KhoaHoc { get; set; }

        [DataColumn("ID_chuyen_nganh")]
        public int IdChuyenNganh { get; set; }

        [DataColumn("So_tin_chi")]
        public float SoTinChi { get; set; }
        //public int SoTinChi { get; set; }

        [DataColumn("So_ky_hoc")]
        public int SoKyHoc { get; set; }

        [DataColumn("So")]
        public int So { get; set; }

        [DataColumn("So_tin_chi_dinh_muc")]
        public int SoTinChiDinhMuc { get; set; }

        [DataColumn("So_quyet_dinh_ctdt")]
        public string SoQuyetDinhCtdt { get; set; }

        [DataColumn("So_nam_dao_tao")]
        public float SoNamDaoTao { get; set; }
        //public int SoNamDaoTao { get; set; }

        [DataColumn("Chi_tiet_quyet_dinh_ctdt")]
        public string ChiTietQuyetDinhCtdt { get; set; }

        [DataColumn("Ten_he")]
        public string TenHe { get; set; }

        [DataColumn("Ten_khoa")]
        public string TenKhoa { get; set; }

        [DataColumn("Ten_nganh")]
        public string TenNganh { get; set; }

        [DataColumn("Chuyen_nganh")]
        public string ChuyenNganh { get; set; }

        [DataColumn("Ten_chuyen_nganh")]
        public string TenChuyenNganh { get; set; }
    }

    public class SoCtdtSelectItemModel
    {
        public int So { get; set; }
    }



    
    public class DeleteChuongTrinhDaoTaoChiTiet
    {
        public int IdDt { get; set; }
        public int IdDtMon { get; set; }
        public int IdMon { get; set; }
    }

    public class CreateChuongTrinhDaoTaoModel
    {
        //public int IdDt { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdNganh { get; set; }
        public int IdChuyenNganh { get; set; }
        public float SoTinChi { get; set; }
        //public double SoTinChi { get; set; }
        public double SoTinChiDinhMuc { get; set; }
        public int SoKyHoc { get; set; }
        public int So { get; set; }
        public string TenHe { get; set; }
        public string TenKhoa { get; set; }
        public string TenNganh { get; set; }
        public string ChuyenNganh { get; set; }
    }

    public class UpdateChuongTrinhDaoTaoModel : CreateChuongTrinhDaoTaoModel
    {
        public float SoNamDaoTao { get; set; }
        public int IdDt { get; set; }
        public string SoQuyetDinhCtdt { get; set; }
        public void UpdateEntity(SvChuongTrinhDaoTao input)
        {
            input.SoTinChi = SoTinChi;
            input.SoKyHoc = SoKyHoc;
            input.SoQuyetDinhCtdt = SoQuyetDinhCtdt;
            input.SoNamDaoTao = SoNamDaoTao;

        }
    }

    public class CongThucTinhDiemHocPhanModel
    {
        [DataColumn("ID_dt")]
        public int IdDt { get; set; }

        public int IdDtMon { get; set; }

        [DataColumn("ID_he")]
        public int IdHe { get; set; }

        [DataColumn("ID_khoa")]
        public int IdKhoa { get; set; }

        [DataColumn("ID_chuyen_nganh")]
        public int IdChuyenNganh { get; set; }

        [DataColumn("Khoa_hoc)")]
        public int KhoaHoc { get; set; }

        [DataColumn("ID_mon")]
        public int IdMon { get; set; }

        [DataColumn("Ky_hieu")]
        public string KyHieu { get; set; }

        [DataColumn("Ten_mon")]
        public string TenMon { get; set; }

        [DataColumn("ID_cong_thuc")]
        public int IdCongThuc { get; set; }

        [DataColumn("Ten_cong_thuc")]
        public string TenCongThuc { get; set; }

        [DataColumn("Cong_thuc_tinh_diem_TBCHP")]
        public string CongThucTinhDiemTBCHP { get; set; }

        [DataColumn("So_hoc_trinh")]
        public float? SoHocTrinh { get; set; }

        [DataColumn("Mon_thuc_hanh")]
        public bool MonThucHanh { get; set; }
        public int So { get; set; }
        public bool? MonLyThuyetThucHanh { get; set; }



    }

    public class CongThucDiemHocPhanBaseModel
    {
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdChuyenNganh { get; set; }
    }

    public class SaoChepChuongTrinhDaoTaoModel
    {
        public int IdDtCu { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdChuyenNganh { get; set; }
        public int So { get; set; }
    }


    public class SaoChepChuongTrinhDaoTaoChiTietModel
    {
        public int IdDt { get; set; }
        public int IdMon { get; set; }
        public int KyThu { get; set; }
        public float? SoHocTrinh { get; set; }
        //public double SoHocTrinh { get; set; }
        public int LyThuyet { get; set; }
        public int ThucHanh { get; set; }
        public int BaiTap { get; set; }
        public int BaiTapLon { get; set; }
        public int ThucTap { get; set; }
        public bool TuChon { get; set; }
        public int SttMon { get; set; }
        public double HeSo { get; set; }
        public int KienThuc { get; set; }
        public bool KhongTinhTBCHT { get; set; }
        public int NhomTuChon { get; set; }
        public int? TuHoc { get; set; }
        public int? SoTinChiTienQuyet { get; set; }
        public string MaKhoaPhuTrach { get; set; }
        public bool MonThucHanh { get; set; }
        public bool MonTotNghiep { get; set; }
        public bool? MonDkTn { get; set; }
        public bool? MonKhoaLuan { get; set; }
        public float? SoHocTrinhThucHanh { get; set; }
        public bool? MonLyThuyetThucHanh { get; set; }
        public DateTime? ModifyDate { get; set; }
        public decimal? DiemTbchpDat { get; set; }
        public decimal? DiemThiDat { get; set; }
        public string ModifyUserName { get; set; }
    }

    public class ThayTheHocPhanBangDiemModel
    {
        public int IdMonCu { get; set; }
        public int IdMonMoi { get; set; }
        public List<int> ListIdLop { get; set; }
    }


    public class HocPhanTheoTinChiModel
    {
        [DataColumn("ID_mon")]
        public int? IdMon { get; set; }

        [DataColumn("Ky_hieu")]
        public string KyHieu { get; set; }

        [DataColumn("Ten_mon")]
        public string TenMon { get; set; }

        [DataColumn("ID_dt_mon")]
        public int? IdDtMon { get; set; }

        [DataColumn("So_hoc_trinh")]
        public float? SoHocTrinh { get; set; }
    }


    public class GanChuongTrinhDaoTaoModel
    {
        public int IdLop { get; set; }
        public int IdDt { get; set; }
        public void UpdateEntity(SvLop input)
        {
            input.IdDt = IdDt;
        }
    }

    public class CreateChuongTrinhChiTietModel
    {
        public int IdDtOld { get; set; }
        public int IdDtNew { get; set; }
    }

    public class CreateChuongTrinhDaoTaoChiTietModel
    {
        public int IdDt { get; set; }
        public int IdMon { get; set; }
       
    }
    public class ImportChuongTrinhDaoTaoChiTietModel
    {
        public int IdDt { get; set; }
        public int IdMon { get; set; }
        public string KyHieu { get; set; }
        public string TenMon { get; set; }
        public int KyThu { get; set; }
        public float? SoHocTrinh { get; set; }
        public int LyThuyet { get; set; }
        public int ThucHanh { get; set; }
        public int BaiTap { get; set; }
        public int BaiTapLon { get; set; }
        public int ThucTap { get; set; }
        public bool TuChon { get; set; }
        public int SttMon { get; set; }
        public float HeSo { get; set; }
        public int KienThuc { get; set; }
        public string TenKienThuc { get; set; }
        public bool KhongTinhTBCHT { get; set; }
        public int NhomTuChon { get; set; }
        public int TuHoc { get; set; }
        public int SoTinChiTienQuyet { get; set; }
        public string MaKhoaPhuTrach { get; set; }
        public bool MonThucHanh { get; set; }
        public bool MonTotNghiep { get; set; }
        public bool? MonKhoaLuan { get; set; }
        public decimal? DiemTbchpDat { get; set; }
        public decimal? DiemThiDat { get; set; }
        public bool? MonDkTn { get; set; }
    }

    public class ImportChuongTrinhDaoTaoModel : CreateChuongTrinhDaoTaoModel
    { 
        public List<ImportChuongTrinhDaoTaoChiTietModel> ListDaoTaoChiTiet {  get; set; }
    }


    public class CheckImportItemChuongTrinhDaoTaoChiTiet
    {
        public string KyHieu { get; set; }
        public string TenMon { get; set; }
        public int KyThu { get; set; }
        public float? SoHocTrinh { get; set; }
        public int LyThuyet { get; set; }
        public int ThucHanh { get; set; }
        public int BaiTap { get; set; }
        public int BaiTapLon { get; set; }
        public int ThucTap { get; set; }
        public bool TuChon { get; set; }
        public int SttMon { get; set; }
        public float HeSo { get; set; }
        public string TenKienThuc { get; set; }
        public bool KhongTinhTBCHT { get; set; }
        public int NhomTuChon { get; set; }
        public int TuHoc { get; set; }
        public int SoTinChiTienQuyet { get; set; }
        public string MaKhoaPhuTrach { get; set; }
        public bool MonThucHanh { get; set; }
        public bool MonTotNghiep { get; set; }
        public bool? MonDkTn { get; set; }
    }


    public class CheckImportManyChuongTrinhDaoTaoChiTiet 
    { 
        public List<CheckImportItemChuongTrinhDaoTaoChiTiet> CheckListDaoTaoChiTiet { get; set; }
    }




    public class UpdateChuongTrinhDaoTaoChiTietModel : CreateChuongTrinhDaoTaoChiTietModel
    {
        public int IdDtMon { get; set; }
        public int KyThu { get; set; }
        public float? SoHocTrinh { get; set; }
        public int LyThuyet { get; set; }
        public int ThucHanh { get; set; }
        public int BaiTap { get; set; }
        public int BaiTapLon { get; set; }
        public bool TuChon { get; set; }
        public float HeSo { get; set; }
        public int KienThuc { get; set; }
        public bool KhongTinhTBCHT { get; set; }
        public int NhomTuChon { get; set; }
        public int TuHoc { get; set; }
        public string MaKhoaPhuTrach { get; set; }
        public bool MonThucHanh { get; set; }
        public bool MonTotNghiep { get; set; }
        public bool? MonDkTn { get; set; }
        public bool? MonKhoaLuan { get; set; }
        public float? SoHocTrinhThucHanh { get; set; }
        public bool? MonLyThuyetThucHanh { get; set; }
        public int ThangNhapDiem { get; set; }
        public void UpdateEntity(SvChuongTrinhDaoTaoChiTiet input)
        {
            input.KyThu = KyThu;
            input.SoHocTrinh = SoHocTrinh;
            input.LyThuyet = LyThuyet;
            input.ThucHanh = ThucHanh;
            input.BaiTap = BaiTap;
            input.BaiTapLon = BaiTapLon;
            input.TuChon = TuChon;
            input.HeSo = HeSo;
            input.KienThuc = KienThuc;
            input.KhongTinhTbcht = KhongTinhTBCHT;
            input.NhomTuChon = NhomTuChon;
            input.TuHoc = TuHoc;
            input.MaKhoaPhuTrach = MaKhoaPhuTrach;
            input.MonThucHanh = MonThucHanh;
            input.MonTotNghiep = MonTotNghiep;
            input.MonDkTn = MonDkTn;
            input.MonKhoaLuan = MonKhoaLuan;
            input.SoHocTrinhThucHanh = SoHocTrinhThucHanh;
            input.MonLyThuyetThucHanh = MonLyThuyetThucHanh;
            input.ThangNhapDiem = ThangNhapDiem;
            input.ModifyDate = DateTime.Now;
        }
    }

    public class ChuongTrinhDaoTaoChiTietModel
    {
        [DataColumn("ID_dt")]
        public int IdDt { get; set; }

        [DataColumn("Ky_hieu")]
        public string KyHieu { get; set; }

        [DataColumn("Ten_mon")]
        public string TenMon { get; set; }

        [DataColumn("ID_dt_mon")]
        public int IdDtMon { get; set; }

        [DataColumn("ID_mon")]
        public int IdMon { get; set; }

        [DataColumn("Ky_thu")]
        public int KyThu { get; set; }

        [DataColumn("So_hoc_trinh")]
        public float? SoHocTrinh { get; set; }
        //public decimal SoHocTrinh { get; set; }

        [DataColumn("Ly_thuyet")]
        public decimal LyThuyet { get; set; }

        [DataColumn("Thuc_hanh")]
        public decimal ThucHanh { get; set; }

        [DataColumn("Bai_tap")]
        public decimal BaiTap { get; set; }

        [DataColumn("Bai_tap_lon")]
        public decimal BaiTapLon { get; set; }

        [DataColumn("Thuc_tap")]
        public decimal ThucTap { get; set; }

        [DataColumn("Tu_chon")]
        public bool TuChon { get; set; }
        //public string TuChon { get; set; }

        [DataColumn("STT_mon")]
        public int SttMon { get; set; }

        [DataColumn("He_so")]
        public float HeSo { get; set; }
        //public decimal HeSo { get; set; }

        [DataColumn("Kien_thuc")]
        public int KienThuc { get; set; }
        //public string KienThuc { get; set; }

        [DataColumn("Khong_tinh_TBCHT")]
        public bool KhongTinhTbcht { get; set; }
        //public string KhongTinhTbcht { get; set; }

        [DataColumn("Nhom_tu_chon")]
        public int NhomTuChon { get; set; }
        //public string NhomTuChon { get; set; }

        [DataColumn("Tu_hoc")]
        public int? TuHoc { get; set; }
        //public decimal TuHoc { get; set; }

        [DataColumn("So_tin_chi_tien_quyet")]
        public int? SoTinChiTienQuyet { get; set; }
        //public decimal SoTinChiTienQuyet { get; set; }

        [DataColumn("Ma_khoa_phu_trach")]
        public string MaKhoaPhuTrach { get; set; }

        [DataColumn("Mon_thuc_hanh")]
        public bool MonThucHanh { get; set; }
        //public string MonThucHanh { get; set; }

        [DataColumn("Mon_tot_nghiep")]
        public bool MonTotNghiep { get; set; }
        //public string MonTotNghiep { get; set; }

        [DataColumn("Mon_dk_tn")]
        public bool? MonDkTn { get; set; }
        //public string MonDkTn { get; set; }

        [DataColumn("Mon_khoa_luan")]
        public bool? MonKhoaLuan { get; set; }
        //public string MonKhoaLuan { get; set; }

        [DataColumn("So_hoc_trinh_thuc_hanh")]
        public float? SoHocTrinhThucHanh { get; set; }
        //public decimal SoHocTrinhThucHanh { get; set; }

        [DataColumn("Mon_ly_thuyet_thuc_hanh")]
        public bool? MonLyThuyetThucHanh { get; set; }
        //public string MonLyThuyetThucHanh { get; set; }

        [DataColumn("ID_cong_thuc")]
        public int IdCongThuc { get; set; }

        [DataColumn("ModifyDate")]
        public DateTime? ModifyDate { get; set; }

        [DataColumn("ModifyUserName")]
        public string ModifyUserName { get; set; }

        [DataColumn("Mien_hoc_phi")]
        public bool MienHocPhi { get; set; }
        //public string MienHocPhi { get; set; }

        [DataColumn("Thang_nhap_diem")]
        public int ThangNhapDiem { get; set; }

        [DataColumn("Diem_TBCHP_dat")]
        public decimal? DiemTbchpDat { get; set; }

        [DataColumn("Diem_thi_dat")]
        public decimal? DiemThiDat { get; set; }

        [DataColumn("ID_mon_cha")]
        public int IdMonCha { get; set; }

        [DataColumn("URL_de_cuong_chi_tiet")]
        public string UrlDeCuongChiTiet { get; set; }

        [DataColumn("Ten_kien_thuc")]
        public string TenKienThuc { get; set; }
    }

    public class AddDiemCtChuongTrinhDaoTaoModel
    {
        public List<int> IdDtMons { get; set; }
        public int IdCongThuc { get; set; }

        public void UpdateEntity(SvChuongTrinhDaoTaoChiTiet input)
        {
            input.IdCongThuc = IdCongThuc;
        }
    }

    public class ThemRangBuocHocPhanModel
    {
        public int IdDt { get; set; }
        public int IdMon { get; set; }
        public int IdMonRb { get; set; }
        public int LoaiRangBuoc { get; set; }
    }


    public class RangBuocHocPhanModel
    {
        public int? IdRb { get; set; }

        public int? IdMon { get; set; }

        public string KyHieu { get; set; }

        public string TenMon { get; set; }
        public string TenRangBuoc { get; set; }

        public int? LoaiRangBuoc { get; set; }

        public int? IdMonRb { get; set; }

        public string KyHieuMonRb { get; set; }

        public string TenMonRb { get; set; }

    }

    public class ThietLapHocPhanTuongDuongModel
    {
        public int IdMon { get; set; }
        public float SoTinChi { get; set; }
        public int IdMon1 { get; set; }
        public float SoTinChi1 { get; set; }
    }


    public class HocPhanTuongDuongModel
    {
        public int? IdMonTuongDuong { get; set; }

        public int? IdMon { get; set; }

        public string KyHieu { get; set; }

        public string TenMon { get; set; }

        public float? SoTinChi { get; set; }

        public int? IdMon1 { get; set; }

        public string KyHieu1 { get; set; }

        public string TenMon1 { get; set; }

        public float? SoTinChi1 { get; set; }

    }


    public class MonHocChungChiModel
    {
        public int IdDt { get; set; }
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdChuyenNganh { get; set; }
        public int IdLop { get; set; }
        public string TenHe { get; set; }
        public string TenKhoa { get; set; }
        public string TenChuyenNganh { get; set; }
        public string TenLop { get; set; }
        public int CtdtSo { get; set; }
        public int IdMon { get; set; }
        public string KyHieu { get; set; }
        public string TenMon { get; set; }
        public int IdLoaiChungChi { get; set; }
        public string TenLoaiChungChi { get; set; }
    }


    public class CreateMonChungChiModel
    {
        public int IdMon { get; set; }
        public int IdDt { get; set; }
        public int IdChungChi { get; set; }
    }

    public class CreateManyMonChungChiModel
    {
        public List<CreateMonChungChiModel> ListChungChi { get; set; }

    }


    public class MonHocTheoHeModel
    {
        public int? IdMon { get; set; }
        public int? IdDt { get; set; }
        public int? IdHe { get; set; }
        public int? IdKhoa { get; set; }
        public int? KhoaHoc { get; set; }
        public int? IdChuyennganh { get; set; }
        public int? IdLop { get; set; }
        public string KyHieu { get; set; }
        public string TenMon { get; set; }
    }

    public class MonHocTheoHeFilterModel
    {
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdChuyenNganh { get; set; }
        public int IdLop { get; set; }
    }

    public class LopHocFilterModel
    {
        public int IdHe { get; set; }
        public int IdKhoa { get; set; }
        public int KhoaHoc { get; set; }
        public int IdChuyenNganh { get; set; }
        public int IdDt { get; set; }

    }

    public class LopHocModel
    {
        public int? IdLop { get; set; }
        public string TenLop { get; set; }
        public string NienKhoa { get; set; }
        public int? IdDt { get; set; }
        public bool? DaGan { get; set; }
    }


    public class NhomTuChonModel
    {
        public int? IdDt { get; set; }

        public string KyHieu { get; set; }

        public string TenMon { get; set; }

        public int? IdDtMon { get; set; }

        public int? IdMon { get; set; }

        public float? SoHocTrinh { get; set; }

        public int? KienThuc { get; set; }

        public string TenKienThuc { get; set; }

        public int? NhomTuChon { get; set; }

        public int? SoMonTuChon { get; set; }
        public int? SoMonDangKy { get; set; }
        public string SoMonDaChon { get; set; }
    }

    public class NhomTuChonFilterModel
    {
        public int IdDt { get; set; }
        public bool MonTuChon { get; set; }


    }



    public class CreateMonTuChonModel
    {
        public int IdDt { get; set; }
        public int NhomTuChon { get; set; }
        public int SoMonTuChon { get; set; }
        public int SoMonDangKy { get; set; }
    }

    public class DeleteMonTuChonModel
    {
        public int IdDt { get; set; }
        public int NhomTuChon { get; set; }
    }


    public class FarmWork
    {
        public int Id { get; set; }
        public string KyHieu { get; set; }
        public decimal DuLieuTest { get; set; } // Giá trị để tính toán
    }

    public class ApplicationDbContext : DbContext
    {
        public DbSet<FarmWork> FarmWorks { get; set; }
    }

    public class FarmWorkModel
    {
        public string KyHieu { get; set; }
        public string DuLieuTest { get; set; }
    }

    public class TestDiemModel
    {
        public string TenDiem { get; set; }
        public int SoDiem { get; set; }
    }
    public class ListDiemModel
    {
        public List<TestDiemModel> ListDiem { get; set; }
        public string CongThuc { get; set; }
    }

}


