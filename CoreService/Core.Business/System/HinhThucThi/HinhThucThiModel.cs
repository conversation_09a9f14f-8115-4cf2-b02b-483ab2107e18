using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class HinhThucThiSelectItemModel
    {
        public int IdHinhThucThi { get; set; }
        public string MaHinhThucThi { get; set; }
        public string HinhThucThi { get; set; }
    }

    public class HinhThucThiBaseModel
    {
        public int IdHinhThucThi { get; set; }
        public string MaHinhThucThi { get; set; }
        public string HinhThucThi { get; set; }
        public string GhiChu { get; set; }
        public bool KhongKiemTraTrungLich { get; set; }
    }


    public class HinhThucThiModel : HinhThucThiBaseModel
    {

    }

    public class HinhThucThiFilterModel : BaseQueryFilterModel
    {
        public HinhThucThiFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdHinhThucThi";
        }
    }

    public class CreateHinhThucThiModel
    {
        [Required(ErrorMessage = "HinhThucThi.IdHinhThucThi.NotRequire")]
        public int IdHinhThucThi { get; set; }

        [MaxLength(50, ErrorMessage = "HinhThucThi.MaHinhThucThi.MaxLength(50)")]
        [Required(ErrorMessage = "HinhThucThi.MaHinhThucThi.NotRequire")]
        public string MaHinhThucThi { get; set; }

        [MaxLength(50, ErrorMessage = "HinhThucThi.HinhThucThi.MaxLength(50)")]
        [Required(ErrorMessage = "HinhThucThi.HinhThucThi.NotRequire")]
        public string HinhThucThi { get; set; }

        [MaxLength(200, ErrorMessage = "HinhThucThi.ChiChu.MaxLength(200)")]
        public string GhiChu { get; set; }

        [Required(ErrorMessage = "HinhThucThi.KhongKiemtraTrungLich.NotRequire")]
        public bool KhongKiemtraTrungLich { get; set; }

    }

    public class CreateManyHinhThucThiModel
    {
        public List<CreateHinhThucThiModel> listHinhThucThiModels { get; set; }
    }

    public class UpdateHinhThucThiModel : CreateHinhThucThiModel
    {
        public void UpdateEntity(SvHinhThucThi input)
        {
            input.IdHinhThucThi = IdHinhThucThi;
            input.MaHinhThucThi = MaHinhThucThi;
            input.HinhThucThi = HinhThucThi;
            input.GhiChu = GhiChu;
            input.KhongKiemTraTrungLich = KhongKiemtraTrungLich;

        }
    }
}
