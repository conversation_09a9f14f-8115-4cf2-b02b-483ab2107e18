using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateNoiThucTapCommand : IRequest<Unit>
    {
        public CreateNoiThucTapModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateNoiThucTapCommand(CreateNoiThucTapModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateNoiThucTapCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateNoiThucTapCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {NoiThucTapConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateNoiThucTapModel, SvNoiThucTap>(model);

                var checkCode = await _dataContext.SvNoiThucTaps.AnyAsync(x => x.IdNoiThucTap == entity.IdNoiThucTap || x.NoiThucTap == entity.NoiThucTap || x.MaNoiThucTap == entity.MaNoiThucTap);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["NoiThucTap.Existed", entity.NoiThucTap.ToString()]}");
                }

                await _dataContext.SvNoiThucTaps.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {NoiThucTapConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới nơi thực tập: {entity.NoiThucTap}",
                    ObjectCode = NoiThucTapConstant.CachePrefix,
                    ObjectId = entity.IdNoiThucTap.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NoiThucTapConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyNoiThucTapCommand : IRequest<Unit>
    {
        public CreateManyNoiThucTapModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyNoiThucTapCommand(CreateManyNoiThucTapModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyNoiThucTapCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyNoiThucTapCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {NoiThucTapConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listNoiThucTapAdd = model.listNoiThucTapModels.Select(x => x.NoiThucTap).ToList();
                var listMaNoiThucTapAdd = model.listNoiThucTapModels.Select(x => x.MaNoiThucTap).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyNoiThucTapModel, SvNoiThucTap>(model);

                // Check data duplicate
                if (listNoiThucTapAdd.Count() != listNoiThucTapAdd.Distinct().Count() || listMaNoiThucTapAdd.Count() != listMaNoiThucTapAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvNoiThucTaps.AnyAsync(x => listNoiThucTapAdd.Contains(x.NoiThucTap)) || await _dataContext.SvNoiThucTaps.AnyAsync(x => listMaNoiThucTapAdd.Contains(x.MaNoiThucTap)))
                {
                    throw new ArgumentException($"{_localizer["NoiThucTap.Existed"]}");
                }

                var listEntity = model.listNoiThucTapModels.Select(x => new SvNoiThucTap()
                {
                    IdNoiThucTap = x.IdNoiThucTap,
                    MaNoiThucTap = x.MaNoiThucTap,
                    NoiThucTap = x.NoiThucTap,
                    DiaChithucTap = x.DiaChithucTap,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdNoiThucTap).ToList();

                Log.Information($"Create many {NoiThucTapConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import nơi thực tập từ file excel",
                    ObjectCode = NoiThucTapConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(NoiThucTapConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateNoiThucTapCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateNoiThucTapModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateNoiThucTapCommand(int id, UpdateNoiThucTapModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateNoiThucTapCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateNoiThucTapCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {NoiThucTapConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvNoiThucTaps.FirstOrDefaultAsync(dt => dt.IdNoiThucTap == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvNoiThucTaps.AnyAsync(x => (x.NoiThucTap ==model.NoiThucTap || x.MaNoiThucTap ==model.MaNoiThucTap) && x.IdNoiThucTap !=model.IdNoiThucTap);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["NoiThucTap.Existed",model.NoiThucTap.ToString()]}");
                }

                Log.Information($"Before Update {NoiThucTapConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvNoiThucTaps.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {NoiThucTapConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {NoiThucTapConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật nơi thực tập: {entity.NoiThucTap}",
                    ObjectCode = NoiThucTapConstant.CachePrefix,
                    ObjectId = entity.IdNoiThucTap.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NoiThucTapConstant.BuildCacheKey(entity.IdNoiThucTap.ToString()));
                _cacheService.Remove(NoiThucTapConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteNoiThucTapCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteNoiThucTapCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteNoiThucTapCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteNoiThucTapCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {NoiThucTapConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvNoiThucTaps.FirstOrDefaultAsync(x => x.IdNoiThucTap == id);

                _dataContext.SvNoiThucTaps.Remove(entity);

                Log.Information($"Delete {NoiThucTapConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa nơi thực tập: {entity.NoiThucTap}",
                    ObjectCode = NoiThucTapConstant.CachePrefix,
                    ObjectId = entity.IdNoiThucTap.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NoiThucTapConstant.BuildCacheKey());
                _cacheService.Remove(NoiThucTapConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
