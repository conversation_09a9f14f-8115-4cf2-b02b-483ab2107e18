using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GioiTinhSelectItemModel
    {
        public int IdGioiTinh { get; set; }
        public string GioiTinh { get; set; }
    }

    public class GioiTinhBaseModel : GioiTinhSelectItemModel
    {

    }


    public class GioiTinhModel : GioiTinhSelectItemModel
    {

    }

    public class GioiTinhFilterModel : BaseQueryFilterModel
    {
        public GioiTinhFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdGioiTinh";
        }
    }

    public class CreateGioiTinhModel
    {
        [Required(ErrorMessage = "GioiTinh.IdGioiTinh.NotRequire")]
        public int IdGioiTinh { get; set; }

        [MaxLength(3, ErrorMessage = "GioiTinh.GioiTinh.MaxLength(3)")]
        [Required(ErrorMessage = "GioiTinh.GioiTinh.NotRequire")]
        public string GioiTinh { get; set; }

    }

    public class CreateManyGioiTinhModel
    {
        public List<CreateGioiTinhModel> listGioiTinhModels { get; set; }
    }

    public class UpdateGioiTinhModel : CreateGioiTinhModel
    {
        public void UpdateEntity(SvGioiTinh input)
        {
            input.IdGioiTinh = IdGioiTinh;
            input.GioiTinh = GioiTinh;


        }
    }
}
