using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxGioiTinhQuery : IRequest<List<GioiTinhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy giới tính cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxGioiTinhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxGioiTinhQuery, List<GioiTinhSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<GioiTinhSelectItemModel>> Handle(GetComboboxGioiTinhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = GioiTinhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvGioiTinhs.OrderBy(x => x.GioiTinh)
                                select new GioiTinhSelectItemModel()
                                {
                                    IdGioiTinh = dt.IdGioiTinh,
                                    GioiTinh = dt.GioiTinh
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.GioiTinh.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterGioiTinhQuery : IRequest<PaginationList<GioiTinhBaseModel>>
    {
        public GioiTinhFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách giới tính có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterGioiTinhQuery(GioiTinhFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterGioiTinhQuery, PaginationList<GioiTinhBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<GioiTinhBaseModel>> Handle(GetFilterGioiTinhQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvGioiTinhs
                            select new GioiTinhBaseModel
                            {
                                IdGioiTinh = dt.IdGioiTinh,
                                GioiTinh = dt.GioiTinh,

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.GioiTinh.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<GioiTinhBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetGioiTinhByIdQuery : IRequest<GioiTinhModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin giới tính theo id
        /// </summary>
        /// <param name="id">Id giới tính</param>
        public GetGioiTinhByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetGioiTinhByIdQuery, GioiTinhModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<GioiTinhModel> Handle(GetGioiTinhByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = GioiTinhConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvGioiTinhs.FirstOrDefaultAsync(x => x.IdGioiTinh == id);

                    return AutoMapperUtils.AutoMap<SvGioiTinh, GioiTinhModel>(entity);
                });
                return item;
            }
        }
    }
}
