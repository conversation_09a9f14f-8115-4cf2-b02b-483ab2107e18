//using MediatR;
//using Microsoft.EntityFrameworkCore;
//using Core.Data;
//using Core.Shared;
//using Serilog;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text.Json;
//using System.Threading;
//using System.Threading.Tasks;
//using Minio.DataModel;

//namespace Core.Business
//{
//    public class CreatePermissionCommand : IRequest<Unit>
//    {
//        public CreatePermissionModel Model { get; set; }
//        public SystemLogModel SystemLog { get; set; }

//        /// <summary>
//        /// Thêm mới quyền người dùng
//        /// </summary>
//        /// <param name="model">Thông tin quyền người dùng cần thêm mới</param>
//        /// <param name="systemLog">Thông tin lưu log</param>
//        public CreatePermissionCommand(CreatePermissionModel model, SystemLogModel systemLog)
//        {
//            Model = model;
//            SystemLog = systemLog;
//        }

//        public class Handler : IRequestHandler<CreatePermissionCommand, Unit>
//        {
//            private readonly DataContext _dataContext;
//            private readonly ICacheService _cacheService;

//            public Handler(DataContext dataContext, ICacheService cacheService)
//            {
//                _dataContext = dataContext;
//                _cacheService = cacheService;
//            }

//            public async Task<Unit> Handle(CreatePermissionCommand request, CancellationToken cancellationToken)
//            {
//                Log.Information($"Create {PermissionConstant.CachePrefix}: " + JsonSerializer.Serialize(request.Model));

//                var entity = AutoMapperUtils.AutoMap<CreatePermissionModel, Permission>(request.Model);

//                var checkCode = await _dataContext.Permissions.AnyAsync(x => x.Code == entity.Code);
//                if (checkCode)
//                {
//                    throw new ArgumentException("Mã quyền người dùng đã tồn tại trong hệ thống!");
//                }

//                entity.CreatedDate = DateTime.Now;

//                await _dataContext.Permissions.AddAsync(entity);
//                await _dataContext.SaveChangesAsync();

//                Log.Information($"Create {PermissionConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
//                request.SystemLog.ListAction.Add(new ActionDetail()
//                {
//                    Description = $"Thêm mới quyền người dùng mã: {entity.Code}",
//                    ObjectCode = PermissionConstant.CachePrefix,
//                    ObjectId = entity.Id.ToString()
//                });

//                //Xóa cache
//                _cacheService.Remove(PermissionConstant.BuildCacheKey());

//                return Unit.Value;
//            }
//        }
//    }

//    public class CreateManyPermissionCommand : IRequest<List<CreateManyDataResult>>
//    {
//        public List<CreatePermissionModel> List { get; set; }
//        public SystemLogModel SystemLog { get; set; }

//        /// <summary>
//        /// Thêm mới nhiều quyền người dùng
//        /// </summary>
//        /// <param name="list">Danh sách quyền người dùng</param>
//        /// <param name="systemLog">Thông tin lưu log</param>
//        public CreateManyPermissionCommand(List<CreatePermissionModel> list, SystemLogModel systemLog)
//        {
//            List = list;
//            SystemLog = systemLog;
//        }

//        public class Handler : IRequestHandler<CreateManyPermissionCommand, List<CreateManyDataResult>>
//        {
//            private readonly DataContext _dataContext;
//            private readonly ICacheService _cacheService;

//            public Handler(DataContext dataContext, ICacheService cacheService)
//            {
//                _dataContext = dataContext;
//                _cacheService = cacheService;
//            }

//            public async Task<List<CreateManyDataResult>> Handle(CreateManyPermissionCommand request, CancellationToken cancellationToken)
//            {
//                Log.Information($"Create many {PermissionConstant.CachePrefix}: " + JsonSerializer.Serialize(request.List));

//                List<CreateManyDataResult> listRS = new List<CreateManyDataResult>();

//                foreach (var item in request.List)
//                {
//                    var entity = AutoMapperUtils.AutoMap<CreatePermissionModel, Permission>(item);

//                    var checkCode = await _dataContext.Permissions.AnyAsync(x => x.Code == entity.Code);
//                    if (checkCode)
//                    {
//                        listRS.Add(new CreateManyDataResult()
//                        {
//                            Code = entity.Code,
//                            Message = $"Thêm mới thất bại, mã {item.Code} đã tồn tại",
//                            IsSuccess = false
//                        });
//                    }
//                    else
//                    {
//                        entity.CreatedDate = DateTime.Now;

//                        await _dataContext.Permissions.AddAsync(entity);

//                        Log.Information($"Create {PermissionConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
//                        request.SystemLog.ListAction.Add(new ActionDetail()
//                        {
//                            Description = $"Thêm mới quyền người dùng mã: {entity.Code}",
//                            ObjectCode = PermissionConstant.CachePrefix,
//                            ObjectId = entity.Id.ToString()
//                        });
//                    }
//                }

//                await _dataContext.SaveChangesAsync();

//                //Xóa cache
//                _cacheService.Remove(PermissionConstant.BuildCacheKey());

//                return listRS;
//            }
//        }
//    }

//    public class UpdatePermissionCommand : IRequest<Unit>
//    {
//        public UpdatePermissionModel Model { get; set; }
//        public SystemLogModel SystemLog { get; set; }

//        /// <summary>
//        /// Cập nhật quyền người dùng
//        /// </summary>
//        /// <param name="model">Thông tin quyền người dùng cần cập nhật</param>
//        /// <param name="systemLog">Thông tin lưu log</param>
//        public UpdatePermissionCommand(UpdatePermissionModel model, SystemLogModel systemLog)
//        {
//            Model = model;
//            SystemLog = systemLog;
//        }

//        public class Handler : IRequestHandler<UpdatePermissionCommand, Unit>
//        {
//            private readonly DataContext _dataContext;
//            private readonly ICacheService _cacheService;

//            public Handler(DataContext dataContext, ICacheService cacheService)
//            {
//                _dataContext = dataContext;
//                _cacheService = cacheService;
//            }

//            public async Task<Unit> Handle(UpdatePermissionCommand request, CancellationToken cancellationToken)
//            {
//                Log.Information($"Update {PermissionConstant.CachePrefix}: {JsonSerializer.Serialize(request.Model)}");

//                var entity = await _dataContext.Permissions.FirstOrDefaultAsync(x => x.Id == request.Model.Id);
//                Log.Information($"Before Update {PermissionConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

//                request.Model.UpdateEntity(entity);

//                _dataContext.Permissions.Update(entity);
//                await _dataContext.SaveChangesAsync();

//                Log.Information($"After Update {PermissionConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

//                //Xóa cache
//                _cacheService.Remove(PermissionConstant.BuildCacheKey(entity.Id.ToString()));
//                _cacheService.Remove(PermissionConstant.BuildCacheKey());

//                request.SystemLog.ListAction.Add(new ActionDetail()
//                {
//                    Description = $"Cập nhập quyền người dùng mã: {entity.Code}",
//                    ObjectCode = PermissionConstant.CachePrefix,
//                    ObjectId = entity.Id.ToString()
//                });

//                return Unit.Value;
//            }
//        }
//    }

//    public class DeletePermissionCommand : IRequest<Unit>
//    {
//        public List<int> ListId { get; set; }
//        public SystemLogModel SystemLog { get; set; }

//        /// <summary>
//        /// Xóa quyền người dùng theo danh sách truyền vào
//        /// </summary>
//        /// <param name="listId">Danh sách id cần xóa</param>
//        /// <param name="systemLog">Thông tin lưu log</param>
//        public DeletePermissionCommand(List<int> listId, SystemLogModel systemLog)
//        {
//            ListId = listId;
//            SystemLog = systemLog;
//        }

//        public class Handler : IRequestHandler<DeletePermissionCommand, Unit>
//        {
//            private readonly DataContext _dataContext;
//            private readonly ICacheService _cacheService;

//            public Handler(DataContext dataContext, ICacheService cacheService)
//            {
//                _dataContext = dataContext;
//                _cacheService = cacheService;
//            }

//            public async Task<Unit> Handle(DeletePermissionCommand request, CancellationToken cancellationToken)
//            {
//                Log.Information($"Delete {PermissionConstant.CachePrefix}: {JsonSerializer.Serialize(request.ListId)}");
//                var listResult = new List<ResponeDeleteModel>();
//                string name = "";

//                var listDt = await _dataContext.Permissions.Where(x => request.ListId.Contains(x.Id)).ToListAsync();
//                _dataContext.Permissions.RemoveRange(listDt);

//                var codes = string.Join(",", listDt.Select(x => x.Code));
//                request.SystemLog.ListAction.Add(new ActionDetail()
//                {
//                    Description = $"Xóa quyền người dùng mã: {codes}",
//                    ObjectCode = PermissionConstant.CachePrefix
//                });

//                foreach (var item in listDt)
//                {
//                    //Xóa cache
//                    _cacheService.Remove(PermissionConstant.BuildCacheKey(item.Id.ToString()));
//                }
//                await _dataContext.SaveChangesAsync();

//                _cacheService.Remove(PermissionConstant.BuildCacheKey());

//                return Unit.Value;
//            }
//        }
//    }
//}