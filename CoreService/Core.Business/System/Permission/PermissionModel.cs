using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class PermissionBaseModel
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string GroupName { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public int Order { get; set; }
        public int IdPhanHe { get; set; }
        public DateTime? CreatedDate { get; set; }
    }

    public class PermissionModel : PermissionBaseModel
    {
        
    }   
    
    public class CreatePermissionModel : PermissionModel
    {
        public Guid? CreatedUserId { get; set; }        
    }

    public class UpdatePermissionModel : PermissionModel
    {
        public int? ModifiedUserId { get; set; }

        public void UpdateEntity(Permission entity)
        {
            entity.Name = this.Name;
            entity.IdPhanHe = this.IdPhanHe;
            entity.GroupName = this.GroupName;
            entity.IsActive = this.IsActive;
            entity.Description = this.Description;
            entity.Order = this.Order;
            entity.ModifiedDate = DateTime.Now;
            entity.ModifiedUserId = this.ModifiedUserId;
        }
    }

    public class PermissionSelectItemModel : SelectItemModel
    {
        public int IdPhanHe { get; set; }
        public string PhanHe { get; set; }
        public string MoTaPhanHe { get; set; }
        public string GroupName { get; set; }
    }

    public class PermissionQueryFilter
    {
        public string TextSearch { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
        public bool? IsActive { get; set; }
        public string PropertyName { get; set; } = "CreatedDate";
        //asc - desc
        public string Ascending { get; set; } = "desc";
        public PermissionQueryFilter()
        {
            PageNumber = QueryFilter.DefaultPageNumber;
            PageSize = QueryFilter.DefaultPageSize;
        }
    }
}
