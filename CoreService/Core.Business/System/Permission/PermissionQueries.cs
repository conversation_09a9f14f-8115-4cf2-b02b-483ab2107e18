using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Business.System;

namespace Core.Business
{
    //public class GetPermissionByIdQuery : IRequest<PermissionModel>
    //{
    //    public int Id { get; set; }

    //    /// <summary>
    //    /// L<PERSON>y thông tin quyền người dùng theo id
    //    /// </summary>
    //    /// <param name="id">Id quyền người dùng</param>
    //    public GetPermissionByIdQuery(int id)
    //    {
    //        Id = id;
    //    }

    //    public class Handler : IRequestHandler<GetPermissionByIdQuery, PermissionModel>
    //    {
    //        private readonly ReadDbContext _dataContext;
    //        private readonly ICacheService _cacheService;

    //        public Handler(ReadDbContext dataContext, ICacheService cacheService)
    //        {
    //            _dataContext = dataContext;
    //            _cacheService = cacheService;
    //        }

    //        public async Task<PermissionModel> Handle(GetPermissionByIdQuery request, CancellationToken cancellationToken)
    //        {
    //            string cacheKey = PermissionConstant.BuildCacheKey(request.Id.ToString());
    //            var item = await _cacheService.GetOrCreate(cacheKey, async () =>
    //            {
    //                var entity = await _dataContext.Permissions.FirstOrDefaultAsync(x => x.Id == request.Id);

    //                return AutoMapperUtils.AutoMap<Permission, PermissionModel>(entity);
    //            });
    //            return item;
    //        }
    //    }
    //}

    //public class GetFilterPermissionQuery : IRequest<PaginationList<PermissionBaseModel>>
    //{
    //    public PermissionQueryFilter Filter { get; set; }

    //    /// <summary>
    //    /// Lấy danh sách quyền người dùng theo điều kiện lọc
    //    /// </summary>
    //    /// <param name="filter">Thông tin lọc</param>
    //    public GetFilterPermissionQuery(PermissionQueryFilter filter)
    //    {
    //        Filter = filter;
    //    }

    //    public class Handler : IRequestHandler<GetFilterPermissionQuery, PaginationList<PermissionBaseModel>>
    //    {
    //        private readonly ReadDbContext _dataContext;

    //        public Handler(ReadDbContext dataContext)
    //        {
    //            _dataContext = dataContext;
    //        }

    //        public async Task<PaginationList<PermissionBaseModel>> Handle(GetFilterPermissionQuery request, CancellationToken cancellationToken)
    //        {

    //            var data = (from entity in _dataContext.Permissions

    //                        select new PermissionBaseModel()
    //                        {
    //                            Id = entity.Id,
    //                            Code = entity.Code,
    //                            GroupName = entity.GroupName,
    //                            Name = entity.Name,
    //                            IsActive = entity.IsActive,
    //                            CreatedDate = entity.CreatedDate,
    //                            Order = entity.Order,
    //                            IdPhanHe = entity.IdPhanHe,
    //                            Description = entity.Description
    //                        });

    //            if (!string.IsNullOrEmpty(request.Filter.TextSearch))
    //            {
    //                string ts = request.Filter.TextSearch.Trim().ToLower();
    //                data = data.Where(x => x.Name.ToLower().Contains(ts) || x.Code.ToLower().Contains(ts));
    //            }

    //            if (request.Filter.IsActive.HasValue)
    //            {
    //                data = data.Where(x => x.IsActive == request.Filter.IsActive);
    //            }

    //            data = data.OrderByField(request.Filter.PropertyName, request.Filter.Ascending);

    //            int totalCount = data.Count();

    //            // Pagination
    //            if (request.Filter.PageSize.HasValue && request.Filter.PageNumber.HasValue)
    //            {
    //                if (request.Filter.PageSize <= 0)
    //                {
    //                    request.Filter.PageSize = QueryFilter.DefaultPageSize;
    //                }

    //                //Calculate nunber of rows to skip on pagesize
    //                int excludedRows = (request.Filter.PageNumber.Value - 1) * (request.Filter.PageSize.Value);
    //                if (excludedRows <= 0)
    //                {
    //                    excludedRows = 0;
    //                }

    //                // Query
    //                data = data.Skip(excludedRows).Take(request.Filter.PageSize.Value);
    //            }
    //            int dataCount = data.Count();

    //            var listResult = await data.ToListAsync();
    //            return new PaginationList<PermissionBaseModel>()
    //            {
    //                DataCount = dataCount,
    //                TotalCount = totalCount,
    //                PageNumber = request.Filter.PageNumber ?? 0,
    //                PageSize = request.Filter.PageSize ?? 0,
    //                Data = listResult
    //            };

    //        }
    //    }
    //}

    public class GetComboboxPermissionQuery : IRequest<List<PermissionSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }
        public int? IdPhanHe { get; set; }

        /// <summary>
        /// Lấy danh sách quyền người dùng cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxPermissionQuery(int count = 0, string textSearch = "", int? idPhanHe = null)
        {
            this.Count = count;
            this.TextSearch = textSearch;
            this.IdPhanHe = idPhanHe;
        }

        public class Handler : IRequestHandler<GetComboboxPermissionQuery, List<PermissionSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<List<PermissionSelectItemModel>> Handle(GetComboboxPermissionQuery request, CancellationToken cancellationToken)
            {
                string cacheKey = PermissionConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.Permissions.Where(x => x.IsActive).OrderBy(x => x.Order).ThenBy(x => x.Name)
                                select new PermissionSelectItemModel()
                                {
                                    Id = item.Id,
                                    Code = item.Code,
                                    Name = item.Name,
                                    IdPhanHe = item.IdPhanHe,
                                    GroupName = item.GroupName
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(request.TextSearch))
                {
                    request.TextSearch = request.TextSearch.ToLower().Trim();
                    list = list.Where(x => x.Name.ToLower().Contains(request.TextSearch) || x.Note.ToLower().Contains(request.TextSearch)).ToList();
                }

                if (request.IdPhanHe.HasValue && request.IdPhanHe != 0)
                {
                    list = list.Where(x => x.IdPhanHe == request.IdPhanHe).ToList();
                }

                if (request.Count > 0)
                {
                    list = list.Take(request.Count).ToList();
                }

                var listPhanHe = await _mediator.Send(new GetComboboxPhanHeQuery());
                foreach (var item in list)
                {
                    var ph = listPhanHe.FirstOrDefault(x => x.IdPh == item.IdPhanHe);
                    if (ph != null)
                    {
                        item.PhanHe = ph.PhanHe;
                        item.MoTaPhanHe = ph.MieuTa;
                    }
                }

                return list;
            }
        }
    }

}
