using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreatePhuongCommand : IRequest<Unit>
    {
        public CreatePhuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreatePhuongCommand(CreatePhuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreatePhuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreatePhuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {PhuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreatePhuongModel, SvPhuong>(model);

                var checkCode = await _dataContext.SvPhuongs.AnyAsync(x => x.TenPhuong == entity.TenPhuong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Phuong.Existed", entity.TenPhuong.ToString()]}");
                }

                await _dataContext.SvPhuongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {PhuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới phường: {entity.TenPhuong}",
                    ObjectCode = PhuongConstant.CachePrefix,
                    ObjectId = entity.IdPhuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyPhuongCommand : IRequest<Unit>
    {
        public CreateManyPhuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyPhuongCommand(CreateManyPhuongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyPhuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyPhuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {PhuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listPhuongAdd = model.listPhuongModels.Select(x => x.TenPhuong).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyPhuongModel, SvPhuong>(model);

                // Check data duplicate
                if (listPhuongAdd.Count() != listPhuongAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvPhuongs.AnyAsync(x => listPhuongAdd.Contains(x.TenPhuong)))
                {
                    throw new ArgumentException($"{_localizer["Phuong.Existed"]}");
                }

                var listEntity = model.listPhuongModels.Select(x => new SvPhuong()
                {
                    IdPhuong = x.IdPhuong,
                    TenPhuong = x.TenPhuong,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdPhuong).ToList();

                Log.Information($"Create many {PhuongConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import phường từ file excel",
                    ObjectCode = PhuongConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(PhuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdatePhuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdatePhuongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdatePhuongCommand(int id, UpdatePhuongModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdatePhuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdatePhuongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {PhuongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvPhuongs.FirstOrDefaultAsync(dt => dt.IdPhuong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              

                var checkCode = await _dataContext.SvPhuongs.AnyAsync(x => x.TenPhuong == model.TenPhuong  && x.IdPhuong != model.IdPhuong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Phuong.Existed", model.TenPhuong.ToString()]}");
                }

                Log.Information($"Before Update {PhuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvPhuongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {PhuongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {PhuongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật phường: {entity.TenPhuong}",
                    ObjectCode = PhuongConstant.CachePrefix,
                    ObjectId = entity.IdPhuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhuongConstant.BuildCacheKey(entity.IdPhuong.ToString()));
                _cacheService.Remove(PhuongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeletePhuongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeletePhuongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeletePhuongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeletePhuongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {PhuongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvPhuongs.FirstOrDefaultAsync(x => x.IdPhuong == id);

                _dataContext.SvPhuongs.Remove(entity);

                Log.Information($"Delete {PhuongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa phường: {entity.TenPhuong}",
                    ObjectCode = PhuongConstant.CachePrefix,
                    ObjectId = entity.IdPhuong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhuongConstant.BuildCacheKey());
                _cacheService.Remove(PhuongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
