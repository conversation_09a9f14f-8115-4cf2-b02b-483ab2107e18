using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class PhuongSelectItemModel
    {
        public int IdPhuong { get; set; }
        public string TenPhuong { get; set; }
    }

    public class PhuongBaseModel
    {
        public int IdPhuong { get; set; }
        public string TenPhuong { get; set; }
    }


    public class PhuongModel : PhuongBaseModel
    {

    }

    public class PhuongFilterModel : BaseQueryFilterModel
    {
        public PhuongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdPhuong";
        }
    }

    public class CreatePhuongModel
    {
        [Required(ErrorMessage = "Phuong.IdPhuong.NotRequire")]
        public int IdPhuong { get; set; }

        [MaxLength(100, ErrorMessage = "Phuong.TenPhuong.MaxLength(100)")]
        [Required(ErrorMessage = "Phuong.TenPhuong.NotRequire")]
        public string TenPhuong { get; set; }


    }

    public class CreateManyPhuongModel
    {
        public List<CreatePhuongModel> listPhuongModels { get; set; }
    }

    public class UpdatePhuongModel : CreatePhuongModel
    {
        public void UpdateEntity(SvPhuong input)
        {
            input.IdPhuong = IdPhuong;
            input.TenPhuong = TenPhuong;
        }
    }
}
