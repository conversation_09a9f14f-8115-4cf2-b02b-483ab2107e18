using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class ThamSoQuyCheBaseModel
    {
        public int IdThamSoQC { get; set; }
        public int QuyChe { get; set; }
        public string MaThamSo { get; set; }
        public string NhomQuyChe { get; set; }
        public string TenThamSo { get; set; }
        public float GiaTri { get; set; }
        public string UserName { get; set; }
        public DateTime? DateModify { get; set; }
        public bool Active { get; set; }
    }

    public class ThamSoQuyCheModel: ThamSoQuyCheBaseModel
    {

    }
}
