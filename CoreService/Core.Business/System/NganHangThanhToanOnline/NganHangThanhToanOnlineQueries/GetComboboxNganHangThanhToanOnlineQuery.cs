using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxNganHangThanhToanOnlineQuery : IRequest<List<NganHangThanhToanOnlineSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }
        public bool? Active { get; set; }
        public bool? QrCode { get; set; }

        /// <summary>
        /// Lấy danh sách ngân hàng thanh toán online cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        /// <param name="active"><PERSON>ọ<PERSON> theo trạng thái hoạt động</param>
        /// <param name="qrCode">Lọc theo hỗ trợ QR Code</param>
        public GetComboboxNganHangThanhToanOnlineQuery(int count = 0, string textSearch = "", bool? active = null, bool? qrCode = null)
        {
            this.Count = count;
            this.TextSearch = textSearch;
            this.Active = active;
            this.QrCode = qrCode;
        }

        public class Handler : IRequestHandler<GetComboboxNganHangThanhToanOnlineQuery, List<NganHangThanhToanOnlineSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<NganHangThanhToanOnlineSelectItemModel>> Handle(GetComboboxNganHangThanhToanOnlineQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                var active = request.Active;
                var qrCode = request.QrCode;

                string cacheKey = NganHangThanhToanOnlineConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.SvNganHangThanhToanOnlines.AsNoTracking()
                                .OrderBy(x => x.TenNganHang).ThenBy(x => x.MaNganHang)
                                select new NganHangThanhToanOnlineSelectItemModel()
                                {
                                    IdNganHang = item.IdNganHang,
                                    MaNganHang = item.MaNganHang,
                                    TenNganHang = item.TenNganHang,
                                    CongThanhToan = item.CongThanhToan,
                                    Active = item.Active,
                                    QrCode = item.QrCode
                                });

                    return await data.ToListAsync();
                });

                // Lọc theo trạng thái hoạt động
                if (active.HasValue)
                {
                    list = list.Where(x => x.Active == active.Value).ToList();
                }

                // Lọc theo hỗ trợ QR Code
                if (qrCode.HasValue)
                {
                    list = list.Where(x => x.QrCode == qrCode.Value).ToList();
                }

                // Tìm kiếm theo từ khóa
                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.Trim();
                    list = list.Where(x => 
                        x.TenNganHang.Contains(textSearch, StringComparison.OrdinalIgnoreCase) ||
                        (!string.IsNullOrEmpty(x.MaNganHang) && x.MaNganHang.Contains(textSearch, StringComparison.OrdinalIgnoreCase)) ||
                        (!string.IsNullOrEmpty(x.CongThanhToan) && x.CongThanhToan.Contains(textSearch, StringComparison.OrdinalIgnoreCase))
                    ).ToList();
                }

                // Giới hạn số lượng kết quả
                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
