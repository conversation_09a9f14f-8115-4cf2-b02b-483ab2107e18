using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class NganHangThanhToanOnlineSelectItemModel
    {
        public int IdNganHang { get; set; }
        public string <PERSON>NganHang { get; set; }
        public string TenNganHang { get; set; }
        public string CongThanhToan { get; set; }
        public bool Active { get; set; }
        public bool QrCode { get; set; }
    }

    public class NganHangThanhToanOnlineBaseModel
    {
        public int IdNganHang { get; set; }
        public string MaNganHang { get; set; }
        public string TenNganHang { get; set; }
        public string CongThanhToan { get; set; }
        public bool Active { get; set; }
        public bool QrCode { get; set; }
    }
}
