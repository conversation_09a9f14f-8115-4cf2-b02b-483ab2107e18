using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXepLoaiChungChiCommand : IRequest<Unit>
    {
        public CreateXepLoaiChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXepLoaiChungChiCommand(CreateXepLoaiChungChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXepLoaiChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXepLoaiChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XepLoaiChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXepLoaiChungChiModel, SvXepLoaiChungChi>(model);

                var checkCode = await _dataContext.SvXepLoaiChungChis.AnyAsync(x => x.XepHang == entity.XepHang);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepHang-ChungChi.Existed", entity.XepHang.ToString()]}");
                }

                await _dataContext.SvXepLoaiChungChis.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XepLoaiChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới Xếp hạng học lực: {entity.XepHang}",
                    ObjectCode = XepLoaiChungChiConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXepLoaiChungChiCommand : IRequest<Unit>
    {
        public CreateManyXepLoaiChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXepLoaiChungChiCommand(CreateManyXepLoaiChungChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXepLoaiChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXepLoaiChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XepLoaiChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listXepHangAdd = model.listXepLoaiChungChiModels.Select(x => x.XepHang).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXepLoaiChungChiModel, SvXepLoaiChungChi>(model);

                // Check data duplicate
                if (listXepHangAdd.Count() != listXepHangAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvXepLoaiChungChis.AnyAsync(x => listXepHangAdd.Contains(x.XepHang)))

                {
                    throw new ArgumentException($"{_localizer["XepHang-ChungChi.Existed"]}");
                }


                var listEntity = model.listXepLoaiChungChiModels.Select(x => new SvXepLoaiChungChi()
                {
                    XepHang = x.XepHang,
                    TuDiem = x.TuDiem,
                    DenDiem = x.DenDiem,
                    XepHangEn = x.XepHangEn
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXepHang).ToList();

                Log.Information($"Create many {XepLoaiChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Xếp hạng học lực từ file excel",
                    ObjectCode = XepLoaiChungChiConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXepLoaiChungChiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepLoaiChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepLoaiChungChiCommand(int id, UpdateXepLoaiChungChiModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepLoaiChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepLoaiChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepLoaiChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepLoaiChungChis.FirstOrDefaultAsync(dt => dt.IdXepHang == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
             

                var checkCode = await _dataContext.SvXepLoaiChungChis.AnyAsync(x => x.IdXepHang != entity.IdXepHang && x.XepHang == model.XepHang);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepHang-ChungChi.Existed", model.XepHang.ToString()]}");
                }


                Log.Information($"Before Update {XepLoaiChungChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepLoaiChungChis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepLoaiChungChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepLoaiChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Xếp hạng học lực: {entity.XepHang}",
                    ObjectCode = XepLoaiChungChiConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiChungChiConstant.BuildCacheKey(entity.IdXepHang.ToString()));
                _cacheService.Remove(XepLoaiChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepLoaiChungChiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepLoaiChungChiCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepLoaiChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepLoaiChungChiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepLoaiChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepLoaiChungChis.FirstOrDefaultAsync(x => x.IdXepHang == id);

                _dataContext.SvXepLoaiChungChis.Remove(entity);

                Log.Information($"Delete {XepLoaiChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Xếp hạng học lực: {entity.XepHang}",
                    ObjectCode = XepLoaiChungChiConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiChungChiConstant.BuildCacheKey());
                _cacheService.Remove(XepLoaiChungChiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
