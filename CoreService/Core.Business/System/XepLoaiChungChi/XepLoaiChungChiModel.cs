using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepLoaiChungChiSelectItemModel
    {
        public int IdXepHang { get; set; }
        public string XepHang { get; set; }
        public string XepHangEn { get; set; }
    }

    public class XepLoaiChungChiBaseModel
    {
        public int IdXepHang { get; set; }
        public float TuDiem { get; set; }
        public float DenDiem { get; set; }
        public string XepHang { get; set; }
        public string XepHangEn { get; set; }

    }


    public class XepLoaiChungChiModel : XepLoaiChungChiBaseModel
    {
      
    }

    public class XepLoaiChungChiFilterModel : BaseQueryFilterModel
    {
        public XepLoaiChungChiFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepHang";
        }
    }

    public class CreateXepLoaiChungChiModel
    {
        [MaxLength(20, ErrorMessage = "XepLoaiChungChi.XepHang.MaxLength(20)")]
        [Required(ErrorMessage = "XepLoaiChungChi.XepHang.NotRequire")]
        public string XepHang { get; set; }

        [Required(ErrorMessage = "XepLoaiChungChi.TuDiem.NotRequire")]
        public float TuDiem { get; set; }

        [Required(ErrorMessage = "XepLoaiChungChi.DenDiem.NotRequire")]
        public float DenDiem { get; set; }

        [MaxLength(50, ErrorMessage = "XepLoaiChungChi.XepHangEn.MaxLength(50)")]
        public string XepHangEn { get; set; }
    }

    public class CreateManyXepLoaiChungChiModel
    {
        public List<CreateXepLoaiChungChiModel> listXepLoaiChungChiModels { get; set; }
    }

    public class UpdateXepLoaiChungChiModel : CreateXepLoaiChungChiModel
    {
        public void UpdateEntity(SvXepLoaiChungChi input)
        {
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.XepHang = XepHang;
            input.XepHangEn = XepHangEn;
        }
    }
}
