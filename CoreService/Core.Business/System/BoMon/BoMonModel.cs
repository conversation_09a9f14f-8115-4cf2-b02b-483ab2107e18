using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class BoMonSelectItemModel
    {
        public int IdBoMon { get; set; }
        public string MaBoMon { get; set; }
        public string BoMon { get; set; }
    }

    public class BoMonBaseModel
    {
        [DataColumn("ID_bm")]
        public int IdBoMon { get; set; }

        [DataColumn("Ma_bo_mon")]
        public string MaBoMon { get; set; }

        [DataColumn("Bo_mon")]
        public string BoMon { get; set; }

        [DataColumn("So_nhom")]
        public int? SoNhom { get; set; }

        [DataColumn("So_mon")]
        public int? SoMon { get; set; }

        [DataColumn("So_giang_vien")]
        public int? SoGiangVien { get; set; }
    }


    public class BoMonModel : BoMonBaseModel
    {

    }

    public class BoMonFilterModel : BaseQueryFilterModel
    {
        public int IdMon { get; set; }
        public int IdCb { get; set; }
        public int IdBoMon { get; set; }
        public BoMonFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdBoMon";
        }
    }

    public class CreateBoMonModel
    {
        [Required(ErrorMessage = "BoMon.IdBoMon.NotRequire")]
        public int IdBoMon { get; set; }

        [MaxLength(10, ErrorMessage = "BoMon.MaBoMon.MaxLength(10)")]
        [Required(ErrorMessage = "BoMon.MaBoMon.NotRequire")]
        public string MaBoMon { get; set; }

        [MaxLength(100, ErrorMessage = "BoMon.BoMon.MaxLength(100)")]
        [Required(ErrorMessage = "BoMon.BoMon.NotRequire")]
        public string BoMon { get; set; }

        [Required(ErrorMessage = "BoMon.BoMon.NotRequire")]
        public int? SoNhom { get; set; }

    }

    public class CreateManyBoMonModel
    {
        public List<CreateBoMonModel> listBoMonModels { get; set; }
    }

    public class UpdateBoMonModel : CreateBoMonModel
    {
        public void UpdateEntity(TkbBoMon input)
        {
            input.IdBoMon = IdBoMon;
            input.MaBoMon = MaBoMon;
            input.BoMon = BoMon;
            input.SoNhom = SoNhom;

        }
    }

    public class GiangVienFilterModel : BaseQueryFilterModel
    {
        public int IdBm { get; set; }
        public GiangVienFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdCb";
        }

    }

    public class GiangVienBaseModel
    {
        [DataColumn("ID_cb")]
        public int IdCb { get; set; }

        [DataColumn("Ho_ten")]
        public string HoTen { get; set; }

        [DataColumn("Ma_cb")]
        public string MaCb { get; set; }

    }


    public class MonHocModel : BaseQueryFilterModel
    {
        public int IdBm { get; set; }
        public MonHocModel()
        {
            Ascending = "desc";
            PropertyName = "IdMon";
        }

    }

    public class MonHocBaseModel
    {
        [DataColumn("ID_mon")]
        public int IdMon { get; set; }

        [DataColumn("Ten_mon")]
        public string TenMon { get; set; }

        [DataColumn("Ky_hieu")]
        public string KyHieu { get; set; }

    }


    public class UpdateMonHocModel
    {

        [Required(ErrorMessage = "MonHoc.IdMonHoc.NotRequire")]
        public List<int> ListIdMonHoc { get; set; }

        [Required(ErrorMessage = "MonHoc.IdBm.NotRequire")]
        public int IdBm { get; set; }
        public void UpdateEntity(SvMonHoc input)
        {
            input.IdBoMon = IdBm;

        }
    }


    public class CreateBoMonGiangVienModel
    {
        [Required(ErrorMessage = "BoMon.IdBoMon.NotRequire")]
        public int IdBoMon { get; set; }

        [Required(ErrorMessage = "BoMon.IdCb.NotRequire")]
        public List<int> ListIdCb { get; set; }
    }

    public class DeleteBoMonGiangVienModel
    {
        public int IdBm { get; set; }
        public List<int> ListIdCb { get; set; }
    }
}
