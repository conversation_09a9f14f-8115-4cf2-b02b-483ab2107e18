using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxDiemQuyDoiQuery : IRequest<List<DiemQuyDoiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy điểm quy đổi cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxDiemQuyDoiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxDiemQuyDoiQuery, List<DiemQuyDoiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<DiemQuyDoiSelectItemModel>> Handle(GetComboboxDiemQuyDoiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = DiemQuyDoiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvDiemQuyDois.OrderBy(x => x.IdXepLoai)
                                select new DiemQuyDoiSelectItemModel()
                                {
                                    IdXepLoai = dt.IdXepLoai,
                                    TuHocKy = dt.TuHocKy,
                                    TuNamHoc = dt.TuNamHoc,
                                    DenHocKy = dt.DenHocKy,
                                    DenNamHoc = dt.DenNamHoc,
                                    XepLoai = dt.XepLoai,
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepLoai.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterDiemQuyDoiQuery : IRequest<PaginationList<DiemQuyDoiBaseModel>>
    {
        public DiemQuyDoiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách điểm quy đổi có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterDiemQuyDoiQuery(DiemQuyDoiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterDiemQuyDoiQuery, PaginationList<DiemQuyDoiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<DiemQuyDoiBaseModel>> Handle(GetFilterDiemQuyDoiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvDiemQuyDois
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            select new DiemQuyDoiBaseModel
                            {
                               IdXepLoai = dt.IdXepLoai,
                               TuHocKy = dt.TuHocKy,
                               TuNamHoc = dt.TuNamHoc,
                               DenHocKy = dt.DenHocKy,
                               DenNamHoc = dt.DenNamHoc,
                               XepLoai = dt.XepLoai,
                               DiemChu = dt.DiemChu,
                               DiemSo = dt.DiemSo,
                               TuDiem = dt.TuDiem,
                               DenDiem = dt.DenDiem,
                               TichLuy = dt.TichLuy,
                               IdHe = dt.IdHe,
                               TenHe = he.TenHe


                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepLoai.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<DiemQuyDoiBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetDiemQuyDoiByIdQuery : IRequest<DiemQuyDoiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin điểm quy đổi theo id
        /// </summary>
        /// <param name="id">Id điểm quy đổi</param>
        public GetDiemQuyDoiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetDiemQuyDoiByIdQuery, DiemQuyDoiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<DiemQuyDoiModel> Handle(GetDiemQuyDoiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = DiemQuyDoiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvDiemQuyDois.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                    return AutoMapperUtils.AutoMap<SvDiemQuyDoi, DiemQuyDoiModel>(entity);
                });
                return item;
            }
        }
    }
}
