
using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DiemQuyDoiSelectItemModel
    {
        public int IdXepLoai { get; set; }
        public int TuHocKy { get; set; }
        public string TuNamHoc { get; set; }
        public int DenHocKy { get; set; }
        public string DenNamHoc { get; set; }
        public string XepLoai { get; set; }
    }

    public class DiemQuyDoiBaseModel
    {
        public int IdXepLoai { get; set; }
        public int TuHocKy { get; set; }
        public string TuNamHoc { get; set; }
        public int DenHocKy { get; set; }
        public string DenNamHoc { get; set; }
        public string XepLoai { get; set; }
        public string DiemChu { get; set; }
        public float DiemSo { get; set; }
        public float TuDiem { get; set; }
        public float DenDiem { get; set; }
        public bool TichLuy { get; set; }
        public int IdHe { get; set; }
        public string TenHe { get; set; }
    }


    public class DiemQuyDoiModel : DiemQuyDoiBaseModel
    {

    }

    public class DiemQuyDoiFilterModel : BaseQueryFilterModel
    {
        public DiemQuyDoiFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepLoai";
        }
    }

    public class CreateDiemQuyDoiModel
    {
        [Required(ErrorMessage = "DiemQuyDoi.IdXepLoai.NotRequire")]
        public int IdXepLoai { get; set; }

        [Required(ErrorMessage = "DiemQuyDoi.TuHocKy.NotRequire")]
        public int TuHocKy { get; set; }

        [MaxLength(10, ErrorMessage = "DiemQuyDoi.TuNamHoc.MaxLength(10)")]
        [Required(ErrorMessage = "DiemQuyDoi.TuNamHoc.NotRequire")]
        public string TuNamHoc { get; set; }

        [Required(ErrorMessage = "DiemQuyDoi.DenHocKy.NotRequire")]
        public int DenHocKy { get; set; }

        [MaxLength(10, ErrorMessage = "DiemQuyDoi.DenNamHoc.MaxLength(10)")]
        [Required(ErrorMessage = "DiemQuyDoi.DenNamHoc.NotRequire")]
        public string DenNamHoc { get; set; }

        [MaxLength(50, ErrorMessage = "DiemQuyDoi.XepLoai.MaxLength(50)")]
        [Required(ErrorMessage = "DiemQuyDoi.XepLoai.NotRequire")]
        public string XepLoai { get; set; }

        [MaxLength(2, ErrorMessage = "DiemQuyDoi.DieChu.MaxLength(2)")]
        [Required(ErrorMessage = "DiemQuyDoi.DieChu.NotRequire")]
        public string DiemChu { get; set; }

        [Required(ErrorMessage = "DiemQuyDoi.DiemSo.NotRequire")]
        public float DiemSo { get; set; }

        [Required(ErrorMessage = "DiemQuyDoi.TuDiem.NotRequire")]
        public float TuDiem { get; set; }

        [Required(ErrorMessage = "DiemQuyDoi.DenDiem.NotRequire")]
        public float DenDiem { get; set; }

        [Required(ErrorMessage = "DiemQuyDoi.TichLuy.NotRequire")]
        public bool TichLuy { get; set; }

        [Required(ErrorMessage = "DiemQuyDoi.MaDiemQuyDoi.NotRequire")]
        public int IdHe { get; set; }

    }

    public class CreateManyDiemQuyDoiModel
    {
        public List<CreateDiemQuyDoiModel> listDiemQuyDoiModels { get; set; }
    }

    public class UpdateDiemQuyDoiModel : CreateDiemQuyDoiModel
    {
        public void UpdateEntity(SvDiemQuyDoi input)
        {
            input.IdXepLoai = IdXepLoai;
            input.TuHocKy = TuHocKy;
            input.TuNamHoc = TuNamHoc;
            input.DenHocKy = DenHocKy;
            input.DenNamHoc = DenNamHoc;
            input.XepLoai = XepLoai;
            input.DiemChu = DiemChu;
            input.DiemSo = DiemSo;
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.TichLuy = TichLuy;
            input.IdHe = IdHe;

        }
    }
}
