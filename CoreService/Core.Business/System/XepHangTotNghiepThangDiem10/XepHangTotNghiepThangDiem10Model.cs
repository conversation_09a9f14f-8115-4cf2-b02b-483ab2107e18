using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepHangTotNghiepThangDiem10SelectItemModel
    {
        public int IdXepHang { get; set; }
        public string XepHang { get; set; }
        public string MaXepHang { get; set; }
        public int IdHe { get; set; }
    }

    public class XepHangTotNghiepThangDiem10BaseModel
    {
        public int IdXepHang { get; set; }
        public string XepHang { get; set; }
        public float TuDiem { get; set; }
        public float DenDiem { get; set; }
        public string MaXepHang { get; set; }
        public string XepHangEn { get; set; }
        public int? IdHe { get; set; }
        public string TenHe { get; set; }
    }


    public class XepHangTotNghiepThangDiem10Model : XepHangTotNghiepThangDiem10BaseModel
    {
      
    }

    public class XepHangTotNghiepThangDiem10FilterModel : BaseQueryFilterModel
    {
        public XepHangTotNghiepThangDiem10FilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepHang";
        }
    }

    public class CreateXepHangTotNghiepThangDiem10Model
    {
        [MaxLength(50, ErrorMessage = "XepHangTotNghiepThangDiem10.XepHang.MaxLength(50)")]
        [Required(ErrorMessage = "XepHangTotNghiepThangDiem10.XepHang.NotRequire")]
        public string XepHang { get; set; }

        [Required(ErrorMessage = "XepHangTotNghiepThangDiem10.TuDiem.NotRequire")]
        public float TuDiem { get; set; }

        [Required(ErrorMessage = "XepHangTotNghiepThangDiem10.DenDiem.NotRequire")]
        public float DenDiem { get; set; }

        [MaxLength(20, ErrorMessage = "XepHangTotNghiepThangDiem10.MaXepHang.MaxLength(20)")]
        [Required(ErrorMessage = "XepHangTotNghiepThangDiem10.MaXepHang.NotRequire")]
        public string MaXepHang { get; set; }

        [MaxLength(50, ErrorMessage = "XepHangTotNghiepThangDiem10.XepHangEn.MaxLength(50)")]
        public string XepHangEn { get; set; }

        [Required(ErrorMessage = "XepHangTotNghiepThangDiem10.IdHe.NotRequire")]
        public int IdHe { get; set; }
    }

    public class CreateManyXepHangTotNghiepThangDiem10Model
    {
        public List<CreateXepHangTotNghiepThangDiem10Model> listXepHangTotNghiepThangDiem10Models { get; set; }
    }

    public class UpdateXepHangTotNghiepThangDiem10Model : CreateXepHangTotNghiepThangDiem10Model
    {
        public void UpdateEntity(SvXepHangTotNghiepThangDiem10 input)
        {
            input.XepHang = XepHang;
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.MaXepHang = MaXepHang;
            input.XepHangEn = XepHangEn;
            input.IdHe = IdHe;

        }
    }
}
