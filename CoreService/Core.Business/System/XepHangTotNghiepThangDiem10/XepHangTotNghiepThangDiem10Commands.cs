using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


        public class CreateXepHangTotNghiepThangDiem10Command : IRequest<Unit>
        {
            public CreateXepHangTotNghiepThangDiem10Model Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateXepHangTotNghiepThangDiem10Command(CreateXepHangTotNghiepThangDiem10Model model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateXepHangTotNghiepThangDiem10Command, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateXepHangTotNghiepThangDiem10Command request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create {XepHangTotNghiepThangDiem10Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var entity = AutoMapperUtils.AutoMap<CreateXepHangTotNghiepThangDiem10Model, SvXepHangTotNghiepThangDiem10>(model);

                    var checkCode = await _dataContext.SvXepHangTotNghiepThangDiem10s.AnyAsync(x => x.XepHang == entity.XepHang && x.IdHe == entity.IdHe);
                    if (checkCode)
                    {
                        throw new ArgumentException($"{_localizer["XepHang.Existed", entity.XepHang.ToString()]}");
                    }

                     var checkCodeMaXepHang = await _dataContext.SvXepHangTotNghiepThangDiem10s.AnyAsync(x => x.MaXepHang == entity.MaXepHang && x.IdHe == entity.IdHe);
                     if (checkCodeMaXepHang)
                     {
                        throw new ArgumentException($"{_localizer["MaXepHang.Existed", entity.MaXepHang.ToString()]}");
                     }

                await _dataContext.SvXepHangTotNghiepThangDiem10s.AddAsync(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Create {XepHangTotNghiepThangDiem10Constant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới Xếp hạng tốt nghiệp thang 10: {entity.XepHang}",
                        ObjectCode = XepHangTotNghiepThangDiem10Constant.CachePrefix,
                        ObjectId = entity.IdXepHang.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(XepHangTotNghiepThangDiem10Constant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class CreateManyXepHangTotNghiepThangDiem10Command : IRequest<Unit>
        {
            public CreateManyXepHangTotNghiepThangDiem10Model Model { get; set; }
            public SystemLogModel SystemLog { get; set; }
            public CreateManyXepHangTotNghiepThangDiem10Command(CreateManyXepHangTotNghiepThangDiem10Model model, SystemLogModel systemLog)
            {
                Model = model;
                SystemLog = systemLog;
            }
            public class Handler : IRequestHandler<CreateManyXepHangTotNghiepThangDiem10Command, Unit>
            {
                private readonly SystemDataContext _dataContext;
                private readonly ICacheService _cacheService;
                private readonly IStringLocalizer<Resources> _localizer;
                public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
                {
                    _dataContext = dataContext;
                    _cacheService = cacheService;
                    _localizer = localizer;
                }
                public async Task<Unit> Handle(CreateManyXepHangTotNghiepThangDiem10Command request, CancellationToken cancellationToken)
                {
                    var model = request.Model;
                    var systemLog = request.SystemLog;
                    Log.Information($"Create many {XepHangTotNghiepThangDiem10Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                    var listXepHangAdd = model.listXepHangTotNghiepThangDiem10Models.Select(x => x.XepHang).ToList();
                    var listMaXepHangAdd = model.listXepHangTotNghiepThangDiem10Models.Select(x => x.MaXepHang).ToList();
                    var listIdHeAdd = model.listXepHangTotNghiepThangDiem10Models.Select(x => x.IdHe).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXepHangTotNghiepThangDiem10Model, SvXepHangTotNghiepThangDiem10>(model);

                    // Check data duplicate
                    if (listXepHangAdd.Count() != listXepHangAdd.Distinct().Count() || listMaXepHangAdd.Count() != listMaXepHangAdd.Distinct().Count())
                    {
                        throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                    }
                // Check data exits DB
                     if (await _dataContext.SvXepHangTotNghiepThangDiem10s.AnyAsync(x => listXepHangAdd.Contains(x.XepHang))
                        && await _dataContext.SvXepHangTotNghiepThangDiem10s.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                     {
                        throw new ArgumentException($"{_localizer["XepHang.Existed"]}");
                     }

                     if (await _dataContext.SvXepHangTotNghiepThangDiem10s.AnyAsync(x => listMaXepHangAdd.Contains(x.MaXepHang))
                      && await _dataContext.SvXepHangTotNghiepThangDiem10s.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                     {
                        throw new ArgumentException($"{_localizer["MaXepHang.Existed"]}");
                     }

                var listEntity = model.listXepHangTotNghiepThangDiem10Models.Select(x => new SvXepHangTotNghiepThangDiem10()
                    {
                        XepHang = x.XepHang,
                        TuDiem = x.TuDiem,
                        DenDiem = x.DenDiem,
                        MaXepHang = x.MaXepHang,
                        XepHangEn = x.XepHangEn,
                        IdHe = x.IdHe


                    }).ToList();

                    await _dataContext.AddRangeAsync(listEntity);
                    await _dataContext.SaveChangesAsync();

                    var createdIds = listEntity.Select(e => e.IdXepHang).ToList();

                    Log.Information($"Create many {XepHangTotNghiepThangDiem10Constant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                    systemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Import Xếp hạng tốt nghiệp thang 10 từ file excel",
                        ObjectCode = XepHangTotNghiepThangDiem10Constant.CachePrefix,
                        ObjectId = JsonSerializer.Serialize(createdIds)
                    });

                    //Xóa cache
                    _cacheService.Remove(XepHangTotNghiepThangDiem10Constant.BuildCacheKey());
                    return Unit.Value;
                }
            }
        }

        public class UpdateXepHangTotNghiepThangDiem10Command : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepHangTotNghiepThangDiem10Model Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepHangTotNghiepThangDiem10Command(int id, UpdateXepHangTotNghiepThangDiem10Model model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepHangTotNghiepThangDiem10Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepHangTotNghiepThangDiem10Command request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepHangTotNghiepThangDiem10Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepHangTotNghiepThangDiem10s.FirstOrDefaultAsync(dt => dt.IdXepHang == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
             

                var checkCode = await _dataContext.SvXepHangTotNghiepThangDiem10s.AnyAsync(x => x.IdXepHang != entity.IdXepHang && x.XepHang == model.XepHang && x.IdHe == model.IdHe);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepHang.Existed", model.XepHang.ToString()]}");
                }

                var checkCodeMaXepHang = await _dataContext.SvXepHangTotNghiepThangDiem10s.AnyAsync(x => x.IdXepHang != entity.IdXepHang && x.MaXepHang == model.MaXepHang && x.IdHe == model.IdHe);
                if (checkCodeMaXepHang)
                {
                    throw new ArgumentException($"{_localizer["MaXepHang.Existed", model.MaXepHang.ToString()]}");
                }

                Log.Information($"Before Update {XepHangTotNghiepThangDiem10Constant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepHangTotNghiepThangDiem10s.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepHangTotNghiepThangDiem10Constant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepHangTotNghiepThangDiem10Constant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Xếp hạng tốt nghiệp thang 10: {entity.XepHang}",
                    ObjectCode = XepHangTotNghiepThangDiem10Constant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepHangTotNghiepThangDiem10Constant.BuildCacheKey(entity.IdXepHang.ToString()));
                _cacheService.Remove(XepHangTotNghiepThangDiem10Constant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepHangTotNghiepThangDiem10Command : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepHangTotNghiepThangDiem10Command(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepHangTotNghiepThangDiem10Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepHangTotNghiepThangDiem10Command request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepHangTotNghiepThangDiem10Constant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepHangTotNghiepThangDiem10s.FirstOrDefaultAsync(x => x.IdXepHang == id);

                _dataContext.SvXepHangTotNghiepThangDiem10s.Remove(entity);

                Log.Information($"Delete {XepHangTotNghiepThangDiem10Constant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Xếp hạng tốt nghiệp thang 10: {entity.XepHang}",
                    ObjectCode = XepHangTotNghiepThangDiem10Constant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepHangTotNghiepThangDiem10Constant.BuildCacheKey());
                _cacheService.Remove(XepHangTotNghiepThangDiem10Constant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
