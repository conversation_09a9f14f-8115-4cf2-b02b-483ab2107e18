using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;


namespace Core.Business
{
    public class GetComboboxXepHangTotNghiepThangDiem10Query : IRequest<List<XepHangTotNghiepThangDiem10SelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại học tập thang 10 cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepHangTotNghiepThangDiem10Query(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepHangTotNghiepThangDiem10Query, List<XepHangTotNghiepThangDiem10SelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepHangTotNghiepThangDiem10SelectItemModel>> Handle(GetComboboxXepHangTotNghiepThangDiem10Query request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepHangTotNghiepThangDiem10Constant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepHangTotNghiepThangDiem10s.OrderBy(x => x.XepHang)
                                select new XepHangTotNghiepThangDiem10SelectItemModel()
                                {
                                    IdXepHang = dt.IdXepHang,
                                    XepHang = dt.XepHang,
                                    MaXepHang = dt.MaXepHang,
                                    IdHe = dt.IdHe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepHang.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepHangTotNghiepThangDiem10Query : IRequest<PaginationList<XepHangTotNghiepThangDiem10BaseModel>>
    {
        public XepHangTotNghiepThangDiem10FilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang 10 có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepHangTotNghiepThangDiem10Query(XepHangTotNghiepThangDiem10FilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepHangTotNghiepThangDiem10Query, PaginationList<XepHangTotNghiepThangDiem10BaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepHangTotNghiepThangDiem10BaseModel>> Handle(GetFilterXepHangTotNghiepThangDiem10Query request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepHangTotNghiepThangDiem10s
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            select new XepHangTotNghiepThangDiem10BaseModel
                            {
                                IdXepHang = dt.IdXepHang,
                                XepHang = dt.XepHang,
                                TuDiem = dt.TuDiem,
                                DenDiem = dt.DenDiem,
                                MaXepHang = dt.MaXepHang,
                                XepHangEn = dt.XepHangEn,
                                IdHe = dt.IdHe,
                                TenHe = he.TenHe

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepHang.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<XepHangTotNghiepThangDiem10BaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepHangTotNghiepThangDiem10ByIdQuery : IRequest<XepHangTotNghiepThangDiem10Model>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại học tập thang 10 theo id
        /// </summary>
        /// <param name="id">Id Xếp loại học tập thang 10</param>
        public GetXepHangTotNghiepThangDiem10ByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepHangTotNghiepThangDiem10ByIdQuery, XepHangTotNghiepThangDiem10Model>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepHangTotNghiepThangDiem10Model> Handle(GetXepHangTotNghiepThangDiem10ByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepHangTotNghiepThangDiem10Constant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepHangTotNghiepThangDiem10s.FirstOrDefaultAsync(x => x.IdXepHang == id);

                    return AutoMapperUtils.AutoMap<SvXepHangTotNghiepThangDiem10, XepHangTotNghiepThangDiem10Model>(entity);
                });
                return item;
            }
        }
    }
}
