using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateNganhCommand : IRequest<Unit>
    {
        public CreateNganhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateNganhCommand(CreateNganhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateNganhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateNganhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {NganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateNganhModel, SvNganh>(model);

                var checkCode = await _dataContext.SvNganhs.AnyAsync(x => x.IdNganh == entity.IdNganh || x.TenNganh == entity.TenNganh || x.MaNganh == entity.MaNganh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Nganh.Existed", entity.TenNganh.ToString()]}");
                }

                await _dataContext.SvNganhs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {NganhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới ngành: {entity.TenNganh}",
                    ObjectCode = NganhConstant.CachePrefix,
                    ObjectId = entity.IdNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NganhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyNganhCommand : IRequest<Unit>
    {
        public CreateManyNganhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyNganhCommand(CreateManyNganhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyNganhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyNganhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {NganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listNganhAdd = model.listNganhModels.Select(x => x.TenNganh).ToList();
                var listMaNganhAdd = model.listNganhModels.Select(x => x.MaNganh).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyNganhModel, SvNganh>(model);

                // Check data duplicate
                if (listNganhAdd.Count() != listNganhAdd.Distinct().Count() || listMaNganhAdd.Count() != listMaNganhAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvNganhs.AnyAsync(x => listNganhAdd.Contains(x.TenNganh)) || await _dataContext.SvNganhs.AnyAsync(x => listMaNganhAdd.Contains(x.MaNganh)))
                {
                    throw new ArgumentException($"{_localizer["Nganh.Existed"]}");
                }

                var listEntity = model.listNganhModels.Select(x => new SvNganh()
                {
                    IdNganh = x.IdNganh,
                    MaNganh = x.MaNganh,
                    TenNganh = x.TenNganh,
                    TenNganhEn = x.TenNganhEn,
                    SuPham = x.SuPham,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdNganh).ToList();

                Log.Information($"Create many {NganhConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import ngành từ file excel",
                    ObjectCode = NganhConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(NganhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateNganhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateNganhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateNganhCommand(int id, UpdateNganhModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateNganhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateNganhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {NganhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvNganhs.FirstOrDefaultAsync(dt => dt.IdNganh == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvNganhs.AnyAsync(x => (x.TenNganh ==model.TenNganh || x.MaNganh ==model.MaNganh) && x.IdNganh !=model.IdNganh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Nganh.Existed",model.TenNganh.ToString()]}");
                }

                Log.Information($"Before Update {NganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvNganhs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {NganhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {NganhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật ngành: {entity.TenNganh}",
                    ObjectCode = NganhConstant.CachePrefix,
                    ObjectId = entity.IdNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NganhConstant.BuildCacheKey(entity.IdNganh.ToString()));
                _cacheService.Remove(NganhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteNganhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteNganhCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteNganhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteNganhCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {NganhConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvNganhs.FirstOrDefaultAsync(x => x.IdNganh == id);

                _dataContext.SvNganhs.Remove(entity);

                Log.Information($"Delete {NganhConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa ngành: {entity.TenNganh}",
                    ObjectCode = NganhConstant.CachePrefix,
                    ObjectId = entity.IdNganh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NganhConstant.BuildCacheKey());
                _cacheService.Remove(NganhConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
