using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateChuongTrinhDaoTaoKienThucCommand : IRequest<Unit>
    {
        public CreateChuongTrinhDaoTaoKienThucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateChuongTrinhDaoTaoKienThucCommand(CreateChuongTrinhDaoTaoKienThucModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateChuongTrinhDaoTaoKienThucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateChuongTrinhDaoTaoKienThucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {ChuongTrinhDaoTaoKienThucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateChuongTrinhDaoTaoKienThucModel, SvChuongTrinhDaoTaoKienThuc>(model);

                var checkCode = await _dataContext.SvChuongTrinhDaoTaoKienThucs.AnyAsync(x => x.TenKienThuc == entity.TenKienThuc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChuongTrinhDaoTaoKienThuc.Existed", entity.TenKienThuc.ToString()]}");
                }

                await _dataContext.SvChuongTrinhDaoTaoKienThucs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChuongTrinhDaoTaoKienThucConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tôn giáo: {entity.TenKienThuc}",
                    ObjectCode = ChuongTrinhDaoTaoKienThucConstant.CachePrefix,
                    ObjectId = entity.IdKienThuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoKienThucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyChuongTrinhDaoTaoKienThucCommand : IRequest<Unit>
    {
        public CreateManyChuongTrinhDaoTaoKienThucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyChuongTrinhDaoTaoKienThucCommand(CreateManyChuongTrinhDaoTaoKienThucModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyChuongTrinhDaoTaoKienThucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyChuongTrinhDaoTaoKienThucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {ChuongTrinhDaoTaoKienThucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listChuongTrinhDaoTaoKienThucAdd = model.listChuongTrinhDaoTaoKienThucModels.Select(x => x.TenKienThuc).ToList();
               
                var entity = AutoMapperUtils.AutoMap<CreateManyChuongTrinhDaoTaoKienThucModel, SvChuongTrinhDaoTaoKienThuc>(model);

                // Check data duplicate
                if (listChuongTrinhDaoTaoKienThucAdd.Count() != listChuongTrinhDaoTaoKienThucAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvChuongTrinhDaoTaoKienThucs.AnyAsync(x => listChuongTrinhDaoTaoKienThucAdd.Contains(x.TenKienThuc)))
                {
                    throw new ArgumentException($"{_localizer["ChuongTrinhDaoTaoKienThuc.Existed"]}");
                }

                var listEntity = model.listChuongTrinhDaoTaoKienThucModels.Select(x => new SvChuongTrinhDaoTaoKienThuc()
                {
                    IdKienThuc = x.IdKienThuc,
                    MonChuyenNganh = x.MonChuyenNganh,
                    TenKienThuc = x.TenKienThuc,
                    TenKienThucEn = x.TenKienThucEn,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdKienThuc).ToList();

                Log.Information($"Create many {ChuongTrinhDaoTaoKienThucConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Tôn Giáo từ file excel",
                    ObjectCode = ChuongTrinhDaoTaoKienThucConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoKienThucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateChuongTrinhDaoTaoKienThucCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateChuongTrinhDaoTaoKienThucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateChuongTrinhDaoTaoKienThucCommand(int id, UpdateChuongTrinhDaoTaoKienThucModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateChuongTrinhDaoTaoKienThucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateChuongTrinhDaoTaoKienThucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {ChuongTrinhDaoTaoKienThucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvChuongTrinhDaoTaoKienThucs.FirstOrDefaultAsync(dt => dt.IdKienThuc == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              
                var checkCode = await _dataContext.SvChuongTrinhDaoTaoKienThucs.AnyAsync(x => x.TenKienThuc == model.TenKienThuc  && x.IdKienThuc != model.IdKienThuc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChuongTrinhDaoTaoKienThuc.Existed", model.TenKienThuc.ToString()]}");
                }

                Log.Information($"Before Update {ChuongTrinhDaoTaoKienThucConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvChuongTrinhDaoTaoKienThucs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ChuongTrinhDaoTaoKienThucConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {ChuongTrinhDaoTaoKienThucConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tôn giáo: {entity.TenKienThuc}",
                    ObjectCode = ChuongTrinhDaoTaoKienThucConstant.CachePrefix,
                    ObjectId = entity.IdKienThuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoKienThucConstant.BuildCacheKey(entity.IdKienThuc.ToString()));
                _cacheService.Remove(ChuongTrinhDaoTaoKienThucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteChuongTrinhDaoTaoKienThucCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteChuongTrinhDaoTaoKienThucCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteChuongTrinhDaoTaoKienThucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteChuongTrinhDaoTaoKienThucCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {ChuongTrinhDaoTaoKienThucConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvChuongTrinhDaoTaoKienThucs.FirstOrDefaultAsync(x => x.IdKienThuc == id);

                _dataContext.SvChuongTrinhDaoTaoKienThucs.Remove(entity);

                Log.Information($"Delete {ChuongTrinhDaoTaoKienThucConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Tôn giáo: {entity.TenKienThuc}",
                    ObjectCode = ChuongTrinhDaoTaoKienThucConstant.CachePrefix,
                    ObjectId = entity.IdKienThuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChuongTrinhDaoTaoKienThucConstant.BuildCacheKey());
                _cacheService.Remove(ChuongTrinhDaoTaoKienThucConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
