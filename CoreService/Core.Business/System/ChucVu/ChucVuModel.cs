using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class ChucVuSelectItemModel
    {
        public int IdChucVu { get; set; }
        public string MaChucVu { get; set; }
        public string ChucVu { get; set; }
    }

    public class ChucVuBaseModel
    {
        public int IdChucVu { get; set; }
        public string MaChucVu { get; set; }
        public string ChucVu { get; set; }

    }


    public class ChucVuModel : ChucVuBaseModel
    {

    }

    public class ChucVuFilterModel : BaseQueryFilterModel
    {
        public ChucVuFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdChucVu";
        }
    }

    public class CreateChucVuModel
    {
        [Required(ErrorMessage = "ChucVu.IdChucVu.NotRequire")]
        public int IdChucVu { get; set; }

        [MaxLength(5, ErrorMessage = "ChucVu.MaChucVu.MaxLength(5)")]
        [Required(ErrorMessage = "ChucVu.MaChucVu.NotRequire")]
        public string MaChucVu { get; set; }

        [MaxLength(100, ErrorMessage = "ChucVu.ChucVu.MaxLength(100)")]
        [Required(ErrorMessage = "ChucVu.ChucVu.NotRequire")]
        public string ChucVu { get; set; }


    }

    public class CreateManyChucVuModel
    {
        public List<CreateChucVuModel> listChucVuModels { get; set; }
    }

    public class UpdateChucVuModel : CreateChucVuModel
    {
        public void UpdateEntity(TkbChucVu input)
        {
            input.IdChucVu = IdChucVu;
            input.MaChucVu = MaChucVu;
            input.ChucVu = ChucVu;

        }
    }
}
