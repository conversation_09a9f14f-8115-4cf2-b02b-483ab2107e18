using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CoSoDaoTaoSelectItemModel
    {
        public int IdCoSo { get; set; }
        public string MaCoSo { get; set; }
        public string TenCoSo { get; set; }
    }

    public class CoSoDaoTaoBaseModel
    {
        public int IdCoSo { get; set; }
        public string MaCoSo { get; set; }
        public string TenCoSo { get; set; }
        public bool DayNgoaiTruong { get; set; }
        public bool GdCongViec { get; set; }
    }


    public class CoSoDaoTaoModel : CoSoDaoTaoBaseModel
    {

    }

    public class CoSoDaoTaoFilterModel : BaseQueryFilterModel
    {
        public CoSoDaoTaoFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdCoSo";
        }
    }

    public class CreateCoSoDaoTaoModel
    {
        [Required(ErrorMessage = "CoSoDaoTao.IdCoSo.NotRequire")]
        public int IdCoSo { get; set; }

        [MaxLength(20, ErrorMessage = "CoSoDaoTao.MaCoSo.MaxLength(20)")]
        [Required(ErrorMessage = "CoSoDaoTao.MaCoSo.NotRequire")]
        public string MaCoSo { get; set; }

        [MaxLength(200, ErrorMessage = "CoSoDaoTao.TenCoSo.MaxLength(200)")]
        [Required(ErrorMessage = "CoSoDaoTao.TenCoSo.NotRequire")]
        public string TenCoSo { get; set; }

        [Required(ErrorMessage = "CoSoDaoTao.DayNgoaiTruong.NotRequire")]
        public bool DayNgoaiTruong { get; set; }

        [Required(ErrorMessage = "CoSoDaoTao.GdCongViec.NotRequire")]
        public bool GdCongViec { get; set; }

    }

    public class CreateManyCoSoDaoTaoModel
    {
        public List<CreateCoSoDaoTaoModel> listCoSoDaoTaoModels { get; set; }
    }

    public class UpdateCoSoDaoTaoModel : CreateCoSoDaoTaoModel
    {
        public void UpdateEntity(TkbCoSoDaoTao input)
        {
            input.IdCoSo = IdCoSo;
            input.MaCoSo = MaCoSo;
            input.TenCoSo = TenCoSo;
            input.DayNgoaiTruong = DayNgoaiTruong;
            input.GdCongViec = GdCongViec;

        }
    }
}
