using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateCoSoDaoTaoCommand : IRequest<Unit>
    {
        public CreateCoSoDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateCoSoDaoTaoCommand(CreateCoSoDaoTaoModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateCoSoDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateCoSoDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {CoSoDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateCoSoDaoTaoModel, TkbCoSoDaoTao>(model);

                var checkCode = await _dataContext.TkbCoSoDaoTaos.AnyAsync(x =>  x.TenCoSo == entity.TenCoSo || x.MaCoSo == entity.MaCoSo);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["CoSoDaoTao.Existed", entity.TenCoSo.ToString()]}");
                }

                await _dataContext.TkbCoSoDaoTaos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {CoSoDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tôn giáo: {entity.TenCoSo}",
                    ObjectCode = CoSoDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdCoSo.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CoSoDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyCoSoDaoTaoCommand : IRequest<Unit>
    {
        public CreateManyCoSoDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyCoSoDaoTaoCommand(CreateManyCoSoDaoTaoModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyCoSoDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyCoSoDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {CoSoDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listCoSoDaoTaoAdd = model.listCoSoDaoTaoModels.Select(x => x.TenCoSo).ToList();
                var listMaCoSoAdd = model.listCoSoDaoTaoModels.Select(x => x.MaCoSo).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyCoSoDaoTaoModel, TkbCoSoDaoTao>(model);

                // Check data duplicate
                if (listCoSoDaoTaoAdd.Count() != listCoSoDaoTaoAdd.Distinct().Count() || listMaCoSoAdd.Count() != listMaCoSoAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbCoSoDaoTaos.AnyAsync(x => listCoSoDaoTaoAdd.Contains(x.TenCoSo)) || await _dataContext.TkbCoSoDaoTaos.AnyAsync(x => listMaCoSoAdd.Contains(x.MaCoSo)))
                {
                    throw new ArgumentException($"{_localizer["CoSoDaoTao.Existed"]}");
                }

                var listEntity = model.listCoSoDaoTaoModels.Select(x => new TkbCoSoDaoTao()
                {
                    IdCoSo = x.IdCoSo,
                    MaCoSo = x.MaCoSo,
                    TenCoSo = x.TenCoSo,
                    DayNgoaiTruong = x.DayNgoaiTruong,
                    GdCongViec = x.GdCongViec

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdCoSo).ToList();

                Log.Information($"Create many {CoSoDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Tôn Giáo từ file excel",
                    ObjectCode = CoSoDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(CoSoDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateCoSoDaoTaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateCoSoDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateCoSoDaoTaoCommand(int id, UpdateCoSoDaoTaoModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateCoSoDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateCoSoDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {CoSoDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbCoSoDaoTaos.FirstOrDefaultAsync(dt => dt.IdCoSo == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.TkbCoSoDaoTaos.AnyAsync(x => (x.TenCoSo == model.TenCoSo || x.MaCoSo == model.MaCoSo) && x.IdCoSo != model.IdCoSo);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["CoSoDaoTao.Existed", model.TenCoSo.ToString()]}");
                }

                Log.Information($"Before Update {CoSoDaoTaoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbCoSoDaoTaos.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {CoSoDaoTaoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {CoSoDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tôn giáo: {entity.TenCoSo}",
                    ObjectCode = CoSoDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdCoSo.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CoSoDaoTaoConstant.BuildCacheKey(entity.IdCoSo.ToString()));
                _cacheService.Remove(CoSoDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteCoSoDaoTaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteCoSoDaoTaoCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteCoSoDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteCoSoDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {CoSoDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbCoSoDaoTaos.FirstOrDefaultAsync(x => x.IdCoSo == id);

                _dataContext.TkbCoSoDaoTaos.Remove(entity);

                Log.Information($"Delete {CoSoDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Tôn giáo: {entity.TenCoSo}",
                    ObjectCode = CoSoDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdCoSo.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CoSoDaoTaoConstant.BuildCacheKey());
                _cacheService.Remove(CoSoDaoTaoConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
