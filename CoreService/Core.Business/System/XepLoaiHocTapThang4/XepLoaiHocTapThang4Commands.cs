using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXepLoaiHocTapThang4Command : IRequest<Unit>
    {
        public CreateXepLoaiHocTapThang4Model Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXepLoaiHocTapThang4Command(CreateXepLoaiHocTapThang4Model model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXepLoaiHocTapThang4Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXepLoaiHocTapThang4Command request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XepLoaiHocTapThang4Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXepLoaiHocTapThang4Model, SvXepLoaiHocTap>(model);

                var checkCode = await _dataContext.SvXepLoaiHocTaps.AnyAsync(x => x.XepLoai == entity.XepLoai && x.IdHe == entity.IdHe);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepLoai.Existed", entity.XepLoai.ToString()]}");
                }
                var checkCodeMaXepLoai = await _dataContext.SvXepLoaiHocTaps.AnyAsync(x => x.MaXepLoai == entity.MaXepLoai && x.IdHe == entity.IdHe);
                if (checkCodeMaXepLoai)
                {
                    throw new ArgumentException($"{_localizer["MaXepLoai.Existed", entity.MaXepLoai.ToString()]}");
                }

                await _dataContext.SvXepLoaiHocTaps.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XepLoaiHocTapThang4Constant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới Xếp loại học tập thang 4: {entity.XepLoai}",
                    ObjectCode = XepLoaiHocTapThang4Constant.CachePrefix,
                    ObjectId = entity.IdXepLoai .ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocTapThang4Constant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXepLoaiHocTapThang4Command : IRequest<Unit>
    {
        public CreateManyXepLoaiHocTapThang4Model Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXepLoaiHocTapThang4Command(CreateManyXepLoaiHocTapThang4Model model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXepLoaiHocTapThang4Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXepLoaiHocTapThang4Command request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XepLoaiHocTapThang4Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listXepLoaiAdd = model.listXepLoaiHocTapThang4Models.Select(x => x.XepLoai).ToList();
                var listMaXepLoaiAdd = model.listXepLoaiHocTapThang4Models.Select(x => x.MaXepLoai).ToList();
                var listIdHeAdd = model.listXepLoaiHocTapThang4Models.Select(x => x.IdHe).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXepLoaiHocTapThang4Model, SvXepLoaiHocTap>(model);

                // Check data duplicate
                if (listXepLoaiAdd.Count() != listXepLoaiAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvXepLoaiHocTaps.AnyAsync(x => listXepLoaiAdd.Contains(x.XepLoai))
                       && await _dataContext.SvXepLoaiHocTaps.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                {
                    throw new ArgumentException($"{_localizer["XepLoai.Existed"]}");
                }
                if (await _dataContext.SvXepLoaiHocTaps.AnyAsync(x => listMaXepLoaiAdd.Contains(x.MaXepLoai))
                      && await _dataContext.SvXepLoaiHocTaps.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                {
                    throw new ArgumentException($"{_localizer["MaXepLoai.Existed"]}");
                }


                var listEntity = model.listXepLoaiHocTapThang4Models.Select(x => new SvXepLoaiHocTap()
                {
                    XepLoai = x.XepLoai,
                    TuDiem = x.TuDiem,
                    DenDiem = x.DenDiem,
                    MaXepLoai = x.MaXepLoai,
                    XepLoaiEn = x.XepLoaiEn,
                    IdHe = x.IdHe,
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXepLoai ).ToList();

                Log.Information($"Create many {XepLoaiHocTapThang4Constant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Xếp loại học tập thang 4 từ file excel",
                    ObjectCode = XepLoaiHocTapThang4Constant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocTapThang4Constant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXepLoaiHocTapThang4Command : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepLoaiHocTapThang4Model Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepLoaiHocTapThang4Command(int id, UpdateXepLoaiHocTapThang4Model model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepLoaiHocTapThang4Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepLoaiHocTapThang4Command request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepLoaiHocTapThang4Constant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepLoaiHocTaps.FirstOrDefaultAsync(dt => dt.IdXepLoai  == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              

                var checkCode = await _dataContext.SvXepLoaiHocTaps.AnyAsync(x => x.IdXepLoai != entity.IdXepLoai && x.XepLoai == model.XepLoai && x.IdHe == model.IdHe);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepLoai.Existed", model.XepLoai.ToString()]}");
                }
                var checkCodeMaXepLoai = await _dataContext.SvXepLoaiHocTaps.AnyAsync(x => x.IdXepLoai != entity.IdXepLoai && x.MaXepLoai == model.MaXepLoai && x.IdHe == model.IdHe);
                if (checkCodeMaXepLoai)
                {
                    throw new ArgumentException($"{_localizer["MaXepLoai.Existed", model.MaXepLoai.ToString()]}");
                }


                Log.Information($"Before Update {XepLoaiHocTapThang4Constant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepLoaiHocTaps.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepLoaiHocTapThang4Constant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepLoaiHocTapThang4Constant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Xếp loại học tập thang 4: {entity.XepLoai}",
                    ObjectCode = XepLoaiHocTapThang4Constant.CachePrefix,
                    ObjectId = entity.IdXepLoai .ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocTapThang4Constant.BuildCacheKey(entity.IdXepLoai .ToString()));
                _cacheService.Remove(XepLoaiHocTapThang4Constant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepLoaiHocTapThang4Command : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepLoaiHocTapThang4Command(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepLoaiHocTapThang4Command, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepLoaiHocTapThang4Command request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepLoaiHocTapThang4Constant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepLoaiHocTaps.FirstOrDefaultAsync(x => x.IdXepLoai  == id);

                _dataContext.SvXepLoaiHocTaps.Remove(entity);

                Log.Information($"Delete {XepLoaiHocTapThang4Constant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Xếp loại học tập thang 4: {entity.XepLoai}",
                    ObjectCode = XepLoaiHocTapThang4Constant.CachePrefix,
                    ObjectId = entity.IdXepLoai .ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocTapThang4Constant.BuildCacheKey());
                _cacheService.Remove(XepLoaiHocTapThang4Constant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
