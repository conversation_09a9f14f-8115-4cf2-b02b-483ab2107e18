using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepLoaiHocTapThang4SelectItemModel
    {
        public int IdXepLoai { get; set; }
        public string XepLoai { get; set; }
        public string MaXepLoai { get; set; }
        public int IdHe { get; set; }
    }

    public class XepLoaiHocTapThang4BaseModel
    {
        public int IdXepLoai { get; set; }
        public string XepLoai { get; set; }
        public float TuDiem { get; set; }
        public float DenDiem { get; set; }
        public string XepLoaiEn { get; set; }
        public string MaXepLoai { get; set; }
        public int IdHe { get; set; }
        public string TenHe { get; set; }
    }


    public class XepLoaiHocTapThang4Model : XepLoaiHocTapThang4BaseModel
    {
      
    }

    public class XepLoaiHocTapThang4FilterModel : BaseQueryFilterModel
    {
        public XepLoaiHocTapThang4FilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepLoai";
        }
    }

    public class CreateXepLoaiHocTapThang4Model
    {
        [MaxLength(50, ErrorMessage = "XepLoaiHocTapThang4.XepLoai.MaxLength(50)")]
        [Required(ErrorMessage = "XepLoaiHocTapThang4.XepLoai.NotRequire")]
        public string XepLoai { get; set; }

        [Required(ErrorMessage = "XepLoaiHocTapThang4.DenDiem.NotRequire")]
        public float TuDiem { get; set; }

        [Required(ErrorMessage = "XepLoaiHocTapThang4.DenDiem.NotRequire")]
        public float DenDiem { get; set; }

        [MaxLength(20, ErrorMessage = "XepLoaiHocTapThang4.MaXepLoai.MaxLength(20)")]
        [Required(ErrorMessage = "XepLoaiHocTapThang4.MaXepLoai.NotRequire")]
        public string MaXepLoai { get; set; }

        [MaxLength(20, ErrorMessage = "XepLoaiHocTapThang4.XepLoaiEn.MaxLength(20)")]
        public string XepLoaiEn { get; set; }

        [Required(ErrorMessage = "XepLoaiHocTapThang4.IdHe.NotRequire")]
        public int IdHe { get; set; }
    }

    public class CreateManyXepLoaiHocTapThang4Model
    {
        public List<CreateXepLoaiHocTapThang4Model> listXepLoaiHocTapThang4Models { get; set; }
    }

    public class UpdateXepLoaiHocTapThang4Model : CreateXepLoaiHocTapThang4Model
    {
        public void UpdateEntity(SvXepLoaiHocTap input)
        {
            input.XepLoai = XepLoai;
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.MaXepLoai = MaXepLoai;
            input.XepLoaiEn = XepLoaiEn;
            input.IdHe = IdHe;
        }
    }
}
