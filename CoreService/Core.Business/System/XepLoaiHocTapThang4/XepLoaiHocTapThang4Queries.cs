using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;
using static System.Runtime.InteropServices.JavaScript.JSType;


namespace Core.Business
{
    public class GetComboboxXepLoaiHocTapThang4Query : IRequest<List<XepLoaiHocTapThang4SelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại học tập thang 4 cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepLoaiHocTapThang4Query(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepLoaiHocTapThang4Query, List<XepLoaiHocTapThang4SelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepLoaiHocTapThang4SelectItemModel>> Handle(GetComboboxXepLoaiHocTapThang4Query request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepLoaiHocTapThang4Constant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepLoaiHocTaps.OrderBy(x => x.XepLoai)
                                select new XepLoaiHocTapThang4SelectItemModel()
                                {
                                    IdXepLoai = dt.IdXepLoai,
                                    XepLoai = dt.XepLoai,
                                    MaXepLoai = dt.MaXepLoai,
                                    IdHe = dt.IdHe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepLoai.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepLoaiHocTapThang4Query : IRequest<PaginationList<XepLoaiHocTapThang4BaseModel>>
    {
        public XepLoaiHocTapThang4FilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang 4 có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepLoaiHocTapThang4Query(XepLoaiHocTapThang4FilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepLoaiHocTapThang4Query, PaginationList<XepLoaiHocTapThang4BaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepLoaiHocTapThang4BaseModel>> Handle(GetFilterXepLoaiHocTapThang4Query request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepLoaiHocTaps
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            select new XepLoaiHocTapThang4BaseModel
                            {
                                IdXepLoai = dt.IdXepLoai,
                                XepLoai = dt.XepLoai,
                                TuDiem = dt.TuDiem,
                                DenDiem = dt.DenDiem,
                                MaXepLoai = dt.MaXepLoai,
                                XepLoaiEn = dt.XepLoaiEn,
                                IdHe = dt.IdHe,
                                TenHe = he.TenHe
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepLoai.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<XepLoaiHocTapThang4BaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepLoaiHocTapThang4ByIdQuery : IRequest<XepLoaiHocTapThang4Model>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại học tập thang 4 theo id
        /// </summary>
        /// <param name="id">Id Xếp loại học tập thang 4</param>
        public GetXepLoaiHocTapThang4ByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepLoaiHocTapThang4ByIdQuery, XepLoaiHocTapThang4Model>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepLoaiHocTapThang4Model> Handle(GetXepLoaiHocTapThang4ByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepLoaiHocTapThang4Constant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepLoaiHocTaps.FirstOrDefaultAsync(x => x.IdXepLoai == id);

                    return AutoMapperUtils.AutoMap<SvXepLoaiHocTap, XepLoaiHocTapThang4Model>(entity);
                });
                return item;
            }
        }
    }
}
