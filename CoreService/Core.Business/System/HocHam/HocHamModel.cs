using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class HocHamSelectItemModel
    {
        public int IdHocHam { get; set; }
        public string MaHocHam { get; set; }
        public string HocHam { get; set; }

    }

    public class HocHamBaseModel : HocHamSelectItemModel
    {

    }


    public class HocHamModel : HocHamSelectItemModel
    {

    }

    public class HocHamFilterModel : BaseQueryFilterModel
    {
        public HocHamFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdHocHam";
        }
    }

    public class CreateHocHamModel
    {
        [Required(ErrorMessage = "HocHam.IdHocHam.NotRequire")]
        public int IdHocHam { get; set; }

        [MaxLength(10, ErrorMessage = "HocHam.MaHocHam.MaxLength(10)")]
        [Required(ErrorMessage = "HocHam.MaHocHam.NotRequire")]
        public string MaHocHam { get; set; }

        [MaxLength(100, ErrorMessage = "HocHam.HocHam.MaxLength(100)")]
        [Required(ErrorMessage = "HocHam.HocHam.NotRequire")]
        public string HocHam { get; set; }


    }

    public class CreateManyHocHamModel
    {
        public List<CreateHocHamModel> listHocHamModels { get; set; }
    }

    public class UpdateHocHamModel : CreateHocHamModel
    {
        public void UpdateEntity(TkbHocHam input)
        {
            input.IdHocHam = IdHocHam;
            input.MaHocHam = MaHocHam;
            input.HocHam = HocHam;

        }
    }
}
