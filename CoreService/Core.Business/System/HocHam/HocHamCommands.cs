using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateHocHamCommand : IRequest<Unit>
    {
        public CreateHocHamModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateHocHamCommand(CreateHocHamModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateHocHamCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateHocHamCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {HocHamConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateHocHamModel, TkbHocHam>(model);

                var checkCode = await _dataContext.TkbHocHams.AnyAsync(x => x.IdHocHam == entity.IdHocHam || x.HocHam == entity.HocHam || x.MaHocHam == entity.MaHocHam);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HocHam.Existed", entity.HocHam.ToString()]}");
                }

                await _dataContext.TkbHocHams.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {HocHamConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới học hàm: {entity.HocHam}",
                    ObjectCode = HocHamConstant.CachePrefix,
                    ObjectId = entity.IdHocHam.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocHamConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyHocHamCommand : IRequest<Unit>
    {
        public CreateManyHocHamModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyHocHamCommand(CreateManyHocHamModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyHocHamCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyHocHamCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {HocHamConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listHocHamAdd = model.listHocHamModels.Select(x => x.HocHam).ToList();
                var listMaHocHamAdd = model.listHocHamModels.Select(x => x.MaHocHam).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyHocHamModel, TkbHocHam>(model);

                // Check data duplicate
                if (listHocHamAdd.Count() != listHocHamAdd.Distinct().Count() || listMaHocHamAdd.Count() != listMaHocHamAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbHocHams.AnyAsync(x => listHocHamAdd.Contains(x.HocHam)) || await _dataContext.TkbHocHams.AnyAsync(x => listMaHocHamAdd.Contains(x.MaHocHam)))
                {
                    throw new ArgumentException($"{_localizer["HocHam.Existed"]}");
                }

                var listEntity = model.listHocHamModels.Select(x => new TkbHocHam()
                {
                    IdHocHam = x.IdHocHam,
                    MaHocHam = x.MaHocHam,
                    HocHam = x.HocHam

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdHocHam).ToList();

                Log.Information($"Create many {HocHamConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import học hàm từ file excel",
                    ObjectCode = HocHamConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(HocHamConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateHocHamCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateHocHamModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateHocHamCommand(int id, UpdateHocHamModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateHocHamCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateHocHamCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {HocHamConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbHocHams.FirstOrDefaultAsync(dt => dt.IdHocHam == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var m = AutoMapperUtils.AutoMap<UpdateHocHamModel, TkbHocHam>(model);
                var checkCode = await _dataContext.TkbHocHams.AnyAsync(x => (x.HocHam == model.HocHam || x.MaHocHam == model.MaHocHam) && x.IdHocHam != model.IdHocHam);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HocHam.Existed", model.HocHam.ToString()]}");
                }

                Log.Information($"Before Update {HocHamConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbHocHams.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {HocHamConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {HocHamConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật học hàm: {entity.HocHam}",
                    ObjectCode = HocHamConstant.CachePrefix,
                    ObjectId = entity.IdHocHam.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocHamConstant.BuildCacheKey(entity.IdHocHam.ToString()));
                _cacheService.Remove(HocHamConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteHocHamCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteHocHamCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteHocHamCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteHocHamCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {HocHamConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbHocHams.FirstOrDefaultAsync(x => x.IdHocHam == id);

                _dataContext.TkbHocHams.Remove(entity);

                Log.Information($"Delete {HocHamConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa học hàm: {entity.HocHam}",
                    ObjectCode = HocHamConstant.CachePrefix,
                    ObjectId = entity.IdHocHam.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocHamConstant.BuildCacheKey());
                _cacheService.Remove(HocHamConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
