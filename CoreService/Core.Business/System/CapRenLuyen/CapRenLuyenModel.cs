using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CapRenLuyenSelectItemModel
    {
        public int IdCapRenLuyen { get; set; }
        public string KyHieu { get; set; }
        public string TenCap { get; set; }
        public int Diem {  get; set; }
    }

    public class CapRenLuyenBaseModel
    {
        public int IdCapRenLuyen { get; set; }
        public string KyHieu { get; set; }
        public string TenCap { get; set; }
        public int Diem { get; set; }
    }


    public class CapRenLuyenModel : CapRenLuyenBaseModel
    {

    }

    public class CapRenLuyenFilterModel : BaseQueryFilterModel
    {
        public CapRenLuyenFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdCapRenLuyen";
        }
    }

    public class CreateCapRenLuyenModel
    {
        [Required(ErrorMessage = "CapRenLuyen.IdCapRenLuyen.NotRequire")]
        public int IdCapRenLuyen { get; set; }

        [MaxLength(5, ErrorMessage = "CapRenLuyen.KyHieu.MaxLength(10)")]
        [Required(ErrorMessage = "CapRenLuyen.KyHieu.NotRequire")]
        public string KyHieu { get; set; }

        [MaxLength(50, ErrorMessage = "CapRenLuyen.TenCap.MaxLength(200)")]
        [Required(ErrorMessage = "CapRenLuyen.TenCap.NotRequire")]
        public string TenCap { get; set; }

        [Required(ErrorMessage = "CapRenLuyen.Diem.NotRequire")]
        public int Diem { get; set; }

    }

    public class CreateManyCapRenLuyenModel
    {
        public List<CreateCapRenLuyenModel> listCapRenLuyenModels { get; set; }
    }

    public class UpdateCapRenLuyenModel : CreateCapRenLuyenModel
    {
        public void UpdateEntity(SvCapRenLuyen input)
        {
            input.IdCapRenLuyen = IdCapRenLuyen;
            input.KyHieu = KyHieu;
            input.TenCap = TenCap;
            input.Diem = Diem;

        }
    }
}
