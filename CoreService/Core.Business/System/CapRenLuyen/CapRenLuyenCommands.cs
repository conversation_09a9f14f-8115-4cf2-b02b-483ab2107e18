using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateCapRenLuyenCommand : IRequest<Unit>
    {
        public CreateCapRenLuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateCapRenLuyenCommand(CreateCapRenLuyenModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateCapRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateCapRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {CapRenLuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateCapRenLuyenModel, SvCapRenLuyen>(model);

                var checkCode = await _dataContext.SvCapRenLuyens.AnyAsync(x => x.IdCapRenLuyen == entity.IdCapRenLuyen || x.TenCap == entity.TenCap || x.KyHieu == entity.KyHieu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["CapRenLuyen.Existed", entity.TenCap.ToString()]}");
                }

                await _dataContext.SvCapRenLuyens.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {CapRenLuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới cấp rèn luyện: {entity.TenCap}",
                    ObjectCode = CapRenLuyenConstant.CachePrefix,
                    ObjectId = entity.IdCapRenLuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CapRenLuyenConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }


    public class UpdateCapRenLuyenCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateCapRenLuyenModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateCapRenLuyenCommand(int id, UpdateCapRenLuyenModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateCapRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateCapRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {CapRenLuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvCapRenLuyens.FirstOrDefaultAsync(dt => dt.IdCapRenLuyen == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.SvCapRenLuyens.AnyAsync(x => (x.TenCap == model.TenCap || x.KyHieu == model.KyHieu) && x.IdCapRenLuyen != model.IdCapRenLuyen);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["CapRenLuyen.Existed", model.TenCap.ToString()]}");
                }

                Log.Information($"Before Update {CapRenLuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvCapRenLuyens.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {CapRenLuyenConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {CapRenLuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật cấp rèn luyện: {entity.TenCap}",
                    ObjectCode = CapRenLuyenConstant.CachePrefix,
                    ObjectId = entity.IdCapRenLuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CapRenLuyenConstant.BuildCacheKey(entity.IdCapRenLuyen.ToString()));
                _cacheService.Remove(CapRenLuyenConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteCapRenLuyenCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteCapRenLuyenCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteCapRenLuyenCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteCapRenLuyenCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {CapRenLuyenConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvCapRenLuyens.FirstOrDefaultAsync(x => x.IdCapRenLuyen == id);

                _dataContext.SvCapRenLuyens.Remove(entity);

                Log.Information($"Delete {CapRenLuyenConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa cấp rèn luyện: {entity.TenCap}",
                    ObjectCode = CapRenLuyenConstant.CachePrefix,
                    ObjectId = entity.IdCapRenLuyen.ToString()
                });

                //Xóa cache
                _cacheService.Remove(CapRenLuyenConstant.BuildCacheKey());
                _cacheService.Remove(CapRenLuyenConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
