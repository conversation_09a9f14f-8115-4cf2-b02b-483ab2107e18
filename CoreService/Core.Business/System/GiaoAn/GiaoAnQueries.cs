using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetGiaoAnByKeyGroupQuery : IRequest<GiaoAnModel>
    {
        public KeySelectGiaoAnModel KeyGroup { get; set; }

        /// <summary>
        /// Lấy thông tin giáo án theo giáo viên, lớp, môn
        /// </summary>
        /// <param name="keyGroup">nhóm khóa</param>
        public GetGiaoAnByKeyGroupQuery(KeySelectGiaoAnModel keyGroup)
        {
            KeyGroup = keyGroup;
        }

        public class Handler : IRequestHandler<GetGiaoAnByKeyGroupQuery, GiaoAnModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<GiaoAnModel> Handle(GetGiaoAnByKeyGroupQuery request, CancellationToken cancellationToken)
            {
                var keyGroup = request.KeyGroup;
                    
                var entity = await _dataContext.TkbGiaoAns.FirstOrDefaultAsync(x => x.IdCb == keyGroup.IdCb && x.IdLop == keyGroup.IdLop && x.IdMon == keyGroup.IdMon);

                GiaoAnModel item = new();

                if (entity != null)
                {
                    item = AutoMapperUtils.AutoMap<TkbGiaoAn, GiaoAnModel>(entity);
                    item.IsCreated = true;
                }

                return item;
            }
        }
    }
}
