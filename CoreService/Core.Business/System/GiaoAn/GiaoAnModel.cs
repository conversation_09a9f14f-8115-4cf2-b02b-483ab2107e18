using Core.Data;

namespace Core.Business
{
    public class GiaoAnBaseModel
    {
        public int IdGiaoAn { get; set; } = 0;
        public int IdCb { get; set; } = 0;
        public int IdLop { get; set; } = 0;
        public int IdMon { get; set; } = 0;
        public string TieuDe { get; set; } = "";
        public string MoTa { get; set; } = "";
        public string UrlGiaoAn { get; set; } = "";
    }

    public class GiaoAnModel : GiaoAnBaseModel
    {
        public bool IsCreated { get; set; } = false;
    }

    public class CreateGiaoAnModel
    {
        public int IdCb { get; set; }
        public int IdLop { get; set; }
        public int IdMon { get; set; }
        public string TieuDe { get; set; }
        public string MoTa { get; set; }
        public string UrlGiaoAn { get; set; }
    }

    public class UpdateGiaoAnModel
    {
        public string TieuDe { get; set; }
        public string MoTa { get; set; }
        public string UrlGiaoAn { get; set; }
        public void UpdateEntity(TkbGiaoAn input)
        {
            input.TieuDe = TieuDe;
            input.MoTa = MoTa;
            input.UrlGiaoAn = UrlGiaoAn;

        }
    }

    public class KeySelectGiaoAnModel 
    {
        public int IdCb { get; set; }
        public int IdLop { get; set; }
        public int IdMon { get; set; }
    }
}
