using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateGiaoAnCommand : IRequest<Unit>
    {
        public CreateGiaoAnModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateGiaoAnCommand(CreateGiaoAnModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateGiaoAnCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateGiaoAnCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {GiaoAnConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateGiaoAnModel, TkbGiaoAn>(model);

                var checkCode = await _dataContext.TkbGiaoAns.AnyAsync(x => x.IdCb == entity.IdCb && x.IdLop == entity.IdLop && x.IdLop == entity.IdMon);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["GiaoAn.Existed", entity.TieuDe.ToString()]}");
                }

                await _dataContext.TkbGiaoAns.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {GiaoAnConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới giáo án: {entity.TieuDe}",
                    ObjectCode = GiaoAnConstant.CachePrefix,
                    ObjectId = entity.IdGiaoAn.ToString()
                });

                //Xóa cache
                _cacheService.Remove(GiaoAnConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }
    
    public class UpdateGiaoAnCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateGiaoAnModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateGiaoAnCommand(int id, UpdateGiaoAnModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateGiaoAnCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateGiaoAnCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {GiaoAnConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbGiaoAns.FirstOrDefaultAsync(dt => dt.IdGiaoAn == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                Log.Information($"Before Update {GiaoAnConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbGiaoAns.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {GiaoAnConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {GiaoAnConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật giáo án: {entity.TieuDe}",
                    ObjectCode = GiaoAnConstant.CachePrefix,
                    ObjectId = entity.IdGiaoAn.ToString()
                });

                //Xóa cache
                _cacheService.Remove(GiaoAnConstant.BuildCacheKey(entity.IdGiaoAn.ToString()));
                _cacheService.Remove(GiaoAnConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteGiaoAnCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteGiaoAnCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteGiaoAnCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteGiaoAnCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {GiaoAnConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbGiaoAns.FirstOrDefaultAsync(x => x.IdGiaoAn == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                _dataContext.TkbGiaoAns.Remove(entity);

                Log.Information($"Delete {GiaoAnConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa giáo án: {entity.TieuDe}",
                    ObjectCode = GiaoAnConstant.CachePrefix,
                    ObjectId = entity.IdGiaoAn.ToString()
                });

                //Xóa cache
                _cacheService.Remove(GiaoAnConstant.BuildCacheKey());
                _cacheService.Remove(GiaoAnConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
