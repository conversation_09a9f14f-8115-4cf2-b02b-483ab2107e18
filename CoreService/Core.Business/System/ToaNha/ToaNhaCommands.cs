using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateToaNhaCommand : IRequest<Unit>
    {
        public CreateToaNhaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateToaNhaCommand(CreateToaNhaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateToaNhaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateToaNhaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {ToaNhaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateToaNhaModel, TkbToaNha>(model);

                var checkCode = await _dataContext.TkbToaNhas.AnyAsync(x =>  x.MaNha == entity.MaNha);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ToaNha.Existed", entity.TenNha.ToString()]}");
                }

                await _dataContext.TkbToaNhas.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ToaNhaConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tòa nhà: {entity.TenNha}",
                    ObjectCode = ToaNhaConstant.CachePrefix,
                    ObjectId = entity.IdNha.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ToaNhaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyToaNhaCommand : IRequest<Unit>
    {
        public CreateManyToaNhaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyToaNhaCommand(CreateManyToaNhaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyToaNhaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyToaNhaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {ToaNhaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listToaNhaAdd = model.listToaNhaModels.Select(x => x.TenNha).ToList();
                var listMaNhaAdd = model.listToaNhaModels.Select(x => x.MaNha).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyToaNhaModel, TkbToaNha>(model);

                // Check data duplicate
                if (listToaNhaAdd.Count() != listToaNhaAdd.Distinct().Count() || listMaNhaAdd.Count() != listMaNhaAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbToaNhas.AnyAsync(x => listToaNhaAdd.Contains(x.TenNha)) || await _dataContext.TkbToaNhas.AnyAsync(x => listMaNhaAdd.Contains(x.MaNha)))
                {
                    throw new ArgumentException($"{_localizer["ToaNha.Existed"]}");
                }

                var listEntity = model.listToaNhaModels.Select(x => new TkbToaNha()
                {
                    IdNha = x.IdNha,
                    MaNha = x.MaNha,
                    TenNha = x.TenNha,
                    IdCoSo = x.IDCoSo,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdNha).ToList();

                Log.Information($"Create many {ToaNhaConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import tòa nhà từ file excel",
                    ObjectCode = ToaNhaConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(ToaNhaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateToaNhaCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateToaNhaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateToaNhaCommand(int id, UpdateToaNhaModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateToaNhaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateToaNhaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {ToaNhaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbToaNhas.FirstOrDefaultAsync(dt => dt.IdNha == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.TkbToaNhas.AnyAsync(x => (x.TenNha == model.TenNha || x.MaNha == model.MaNha) && x.IdNha != model.IdNha);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ToaNha.Existed", model.TenNha.ToString()]}");
                }

                Log.Information($"Before Update {ToaNhaConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbToaNhas.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ToaNhaConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {ToaNhaConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tòa nhà: {entity.TenNha}",
                    ObjectCode = ToaNhaConstant.CachePrefix,
                    ObjectId = entity.IdNha.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ToaNhaConstant.BuildCacheKey(entity.IdNha.ToString()));
                _cacheService.Remove(ToaNhaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteToaNhaCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteToaNhaCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteToaNhaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteToaNhaCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {ToaNhaConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbToaNhas.FirstOrDefaultAsync(x => x.IdNha == id);

                _dataContext.TkbToaNhas.Remove(entity);

                Log.Information($"Delete {ToaNhaConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa tòa nhà: {entity.TenNha}",
                    ObjectCode = ToaNhaConstant.CachePrefix,
                    ObjectId = entity.IdNha.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ToaNhaConstant.BuildCacheKey());
                _cacheService.Remove(ToaNhaConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
