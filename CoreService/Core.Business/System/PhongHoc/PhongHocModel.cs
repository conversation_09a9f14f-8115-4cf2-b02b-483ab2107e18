using Core.Business;
using Core.Data;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace Core.Business
{
    public class PhongHocSelectItemModel
    {
        public int? IdPhong { get; set; }
        public string SoPhong { get; set; }
    }

    public class PhongHocBaseModel
    {
        public int? IdPhong { get; set; }
        public int? IdCoSo { get; set; }
        public string TenCoSo { get; set; }
        public int? IdNha { get; set; }
        public string TenNha { get; set; }
        public int? IdTang { get; set; }
        public string TenTang { get; set; }
        public string SoPhong { get; set; }
        public int? SucChua { get; set; }
        public int? SoSv { get; set; }
        public int? IdLoaiPhong { get; set; }
        public string ThietBi { get; set; }
        public string TenLoaiPhong { get; set; }
        public int KhongToChucThi { get; set; }
        public string Ghi<PERSON>hu { get; set; }
        public bool? TrungPhong { get; set; }
        public int? IdKhoa { get; set; }
        public string TenKhoa { get; set; }
        public int? SucChuaThi { get; set; }
        public bool? AmThanh { get; set; }
        public bool? MayTinh { get; set; }
        public bool? TiVi { get; set; }
        public bool? MayChieu { get; set; }
    }
}


public class PhongHocModel 
{
    public int? IdPhong { get; set; }
    public int? IdCoSo { get; set; }
    public int? IdNha { get; set; }
    public int? IdTang { get; set; }
    public string SoPhong { get; set; }
    public string LoaiPhong { get; set; }
    public int? SoBan { get; set; }
    public int? SoSvMotBan { get; set; }
    public int? SucChua { get; set; }
    public int? SucChuaThi { get; set; }
    public bool? AmThanh { get; set; }
    public bool? MayTinh { get; set; }
    public bool? TiVi { get; set; }
    public bool? MayChieu { get; set; }
    public int? SoSv { get; set; }
    public int? IdLoaiPhong { get; set; }
    public string ThietBi { get; set; }
    public int KhongToChucThi { get; set; }
    public string GhiChu { get; set; }
    public bool? TrungPhong { get; set; }
    public int? IdKhoa { get; set; }
}

public class PhongHocFilterModel : BaseQueryFilterModel
{
    public PhongHocFilterModel()
    {
        Ascending = "desc";
        PropertyName = "IdPhong";
    }
}

public class CreatePhongHocModel
{


    public int? IdCoSo { get; set; }

    public int? IdNha { get; set; }

    public int? IdTang { get; set; }

    [MaxLength(50, ErrorMessage = "PhongHoc.SoPhong.MaxLength(50)")]
    [Required(ErrorMessage = "PhongHoc.SoPhong.NotRequire")]
    public string SoPhong { get; set; }

    [MaxLength(200, ErrorMessage = "PhongHoc.LoaiPhong.MaxLength(200)")]
    [Required(ErrorMessage = "PhongHoc.LoaiPhong.NotRequire")]
    public string LoaiPhong { get; set; }

    public int? SoBan { get; set; }

    public int? SoSvMotBan { get; set; }

    public int? SucChua { get; set; }

    public int? SucChuaThi { get; set; }

    public bool? AmThanh { get; set; }

    public bool? MayTinh { get; set; }

    public bool? TiVi { get; set; }

    public bool? MayChieu { get; set; }

    public int? SoSv{ get; set; }

    public int? IdLoaiPhong { get; set; }

    [MaxLength(1000, ErrorMessage = "PhongHoc.ThietBi.MaxLength(1000)")]
    [Required(ErrorMessage = "PhongHoc.ThietBi.NotRequire")]
    public string ThietBi { get; set; }

    [Required(ErrorMessage = "PhongHoc.KhongToChucThi.NotRequire")]
    public int KhongToChucThi { get; set; }

    [MaxLength(100, ErrorMessage = "PhongHoc.GhiChu.MaxLength(100)")]
    [Required(ErrorMessage = "PhongHoc.GhiChu.NotRequire")]
    public string GhiChu { get; set; }

    public bool? TrungPhong { get; set; }


    public int? IdKhoa { get; set; }

}

public class CreateManyPhongHocModel
{
    public List<CreatePhongHocModel> listPhongHocModels { get; set; }
}

public class UpdatePhongHocModel : CreatePhongHocModel
{
    public int? IdPhong { get; set; }
    public void UpdateEntity(TkbPhongHoc input)
    {
        input.IdCoSo = IdCoSo;
        input.IdNha = IdNha;
        input.IdTang = IdTang;
        input.SoPhong = SoPhong;
        input.LoaiPhong = LoaiPhong;
        input.SoBan = SoBan;
        input.SoSvMotBan = SoSvMotBan;
        input.SucChua = SucChua;
        input.SucChuaThi = SucChuaThi;
        input.AmThanh = AmThanh;
        input.MayTinh = MayTinh;
        input.TiVi = TiVi;
        input.MayChieu = MayChieu;
        input.SoSv = SoSv;
        input.IdLoaiPhong = IdLoaiPhong;
        input.ThietBi = ThietBi;
        input.KhongToChucThi = KhongToChucThi;
        input.GhiChu = GhiChu;
        input.TrungPhong = TrungPhong;
        input.IdKhoa = IdKhoa;
      
    }
}



