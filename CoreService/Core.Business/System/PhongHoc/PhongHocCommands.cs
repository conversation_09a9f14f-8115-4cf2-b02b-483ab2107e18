using Core.Data;
using Core.Shared;
using Fluid.Tags;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreatePhongHocCommand : IRequest<Unit>
    {
        public CreatePhongHocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreatePhongHocCommand(CreatePhongHocModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreatePhongHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreatePhongHocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {PhongHocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreatePhongHocModel, TkbPhongHoc>(model);

                var checkCode = await _dataContext.TkbPhongHocs.AnyAsync(x => x.IdPhong == entity.IdPhong || x.SoPhong == entity.SoPhong );
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["PhongHoc.Existed", entity.SoPhong.ToString()]}");
                }

                await _dataContext.TkbPhongHocs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {PhongHocConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới phòng học: {entity.SoPhong}",
                    ObjectCode = PhongHocConstant.CachePrefix,
                    ObjectId = entity.IdPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhongHocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyPhongHocCommand : IRequest<Unit>
    {
        public CreateManyPhongHocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyPhongHocCommand(CreateManyPhongHocModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyPhongHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyPhongHocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {PhongHocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listPhongHocAdd = model.listPhongHocModels.Select(x => x.SoPhong).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyPhongHocModel, TkbPhongHoc>(model);

                // Check data duplicate
                if (listPhongHocAdd.Count() != listPhongHocAdd.Distinct().Count() )
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbPhongHocs.AnyAsync(x => listPhongHocAdd.Contains(x.SoPhong)) )
                {
                    throw new ArgumentException($"{_localizer["PhongHoc.Existed"]}");
                }

                var listEntity = model.listPhongHocModels.Select(x => new TkbPhongHoc()
                {
                     IdCoSo = x.IdCoSo,
                     IdNha = x.IdNha,
                     IdTang = x.IdTang,
                     SoPhong = x.SoPhong,
                     LoaiPhong = x.LoaiPhong,
                     SoBan = x.SoBan,
                     SoSvMotBan = x.SoSvMotBan,
                     SucChua = x.SucChua,
                     SucChuaThi = x.SucChuaThi,
                     AmThanh = x.AmThanh,
                     MayTinh = x.MayTinh,
                     TiVi = x.TiVi,
                     MayChieu = x.MayChieu,
                     SoSv = x.SoSv,
                     IdLoaiPhong = x.IdLoaiPhong,
                     ThietBi = x.ThietBi,
                     KhongToChucThi = x.KhongToChucThi,
                     GhiChu = x.GhiChu,
                     TrungPhong = x.TrungPhong,
                     IdKhoa = x.IdKhoa

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdPhong).ToList();

                Log.Information($"Create many {PhongHocConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import phòng học từ file excel",
                    ObjectCode = PhongHocConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(PhongHocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdatePhongHocCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdatePhongHocModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdatePhongHocCommand(int id, UpdatePhongHocModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdatePhongHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdatePhongHocCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {PhongHocConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbPhongHocs.FirstOrDefaultAsync(dt => dt.IdPhong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.TkbPhongHocs.AnyAsync(x => x.SoPhong == model.SoPhong && x.IdPhong != model.IdPhong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["PhongHoc.Existed", model.SoPhong.ToString()]}");
                }

                Log.Information($"Before Update {PhongHocConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbPhongHocs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {PhongHocConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {PhongHocConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật phòng học: {entity.SoPhong}",
                    ObjectCode = PhongHocConstant.CachePrefix,
                    ObjectId = entity.IdPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhongHocConstant.BuildCacheKey(entity.IdPhong.ToString()));
                _cacheService.Remove(PhongHocConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeletePhongHocCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeletePhongHocCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeletePhongHocCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeletePhongHocCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {PhongHocConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbPhongHocs.FirstOrDefaultAsync(x => x.IdPhong == id);

                _dataContext.TkbPhongHocs.Remove(entity);

                Log.Information($"Delete {PhongHocConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa phòng học: {entity.SoPhong}",
                    ObjectCode = PhongHocConstant.CachePrefix,
                    ObjectId = entity.IdPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(PhongHocConstant.BuildCacheKey());
                _cacheService.Remove(PhongHocConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
