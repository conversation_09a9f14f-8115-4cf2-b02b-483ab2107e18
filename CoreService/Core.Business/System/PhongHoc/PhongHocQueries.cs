using Core.Data;
using Core.Shared;
using MediatR;
using RazorLight.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Core.Business
{

    
    public class GetFilterPhongHocQuery : IRequest<PaginationList<PhongHocBaseModel>>
    {
        public PhongHocFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách phòng học có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterPhongHocQuery(PhongHocFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterPhongHocQuery, PaginationList<PhongHocBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<PhongHocBaseModel>> Handle(GetFilterPhongHocQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from ph in _dataContext.TkbPhongHocs
                            join tn in _dataContext.TkbToaNhas on ph.IdNha equals tn.IdNha
                            join tang in _dataContext.TkbTangs on ph.IdTang equals tang.IdTang
                            join cs in _dataContext.TkbCoSoDaoTaos on ph.IdCoSo equals cs.IdCoSo
                            join loai in _dataContext.TkbLoaiPhongs on ph.IdLoaiPhong equals loai.IdLoaiPhong into phLoai
                            from loai in phLoai.DefaultIfEmpty()
                            join k in _dataContext.SvKhoas on ph.IdKhoa equals k.IdKhoa into phK
                            from k in phK.DefaultIfEmpty()
                            select new PhongHocBaseModel
                            {
                                IdPhong = ph != null ? ph.IdPhong : 0,
                                IdCoSo = ph != null ? ph.IdCoSo : 0,
                                TenCoSo = cs != null ? cs.TenCoSo : string.Empty,
                                IdNha = ph != null ? ph.IdNha : 0,
                                TenNha = tn != null ? tn.MaNha : string.Empty,
                                IdTang = ph != null ? ph.IdTang : 0,
                                TenTang = tang != null ? tang.TenTang : string.Empty,
                                SoPhong = ph != null ? ph.SoPhong : string.Empty,
                                SucChua = ph != null ? ph.SucChua : 0,
                                SoSv = ph != null ? ph.SoSv : 0,
                                IdLoaiPhong = ph != null ? ph.IdLoaiPhong : 0,
                                ThietBi = ph != null ? ph.ThietBi : string.Empty,
                                TenLoaiPhong = loai != null ? loai.TenLoaiPhong : string.Empty,
                                KhongToChucThi = ph != null ? ph.KhongToChucThi : 0,
                                GhiChu = ph != null ? ph.GhiChu : string.Empty,
                                TrungPhong = ph != null ? ph.TrungPhong : false,
                                IdKhoa = k != null ? k.IdKhoa : 0,
                                TenKhoa = k != null ? k.TenKhoa : string.Empty,
                                SucChuaThi = ph != null ? ph.SucChuaThi : 0,
                                AmThanh = ph != null ? ph.AmThanh : false,
                                MayTinh = ph != null ? ph.MayTinh : false,
                                TiVi = ph != null ? ph.TiVi : false,
                                MayChieu = ph != null ? ph.MayChieu : false,

                            }); ;

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.SoPhong.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<PhongHocBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetComboboxPhongHocQuery : IRequest<List<PhongHocSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy phòng học cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxPhongHocQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxPhongHocQuery, List<PhongHocSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<PhongHocSelectItemModel>> Handle(GetComboboxPhongHocQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = PhongHocConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbPhongHocs.OrderBy(x => x.SoPhong)
                                select new PhongHocSelectItemModel()
                                {
                                    IdPhong = dt.IdPhong,
                                    SoPhong = dt.SoPhong
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.SoPhong.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetPhongHocByIdQuery : IRequest<PhongHocModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin phòng học theo id
        /// </summary>
        /// <param name="id">Id phòng học</param>
        public GetPhongHocByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetPhongHocByIdQuery, PhongHocModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<PhongHocModel> Handle(GetPhongHocByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = PhongHocConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbPhongHocs.FirstOrDefaultAsync(x => x.IdPhong == id);

                    return AutoMapperUtils.AutoMap<TkbPhongHoc, PhongHocModel>(entity);
                });
                return item;
            }
        }
    }
}
