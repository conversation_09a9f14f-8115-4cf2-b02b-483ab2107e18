using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXepHangNamDaoTaoCommand : IRequest<Unit>
    {
        public CreateXepHangNamDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXepHangNamDaoTaoCommand(CreateXepHangNamDaoTaoModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXepHangNamDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXepHangNamDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XepHangNamDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXepHangNamDaoTaoModel, SvXepHangNamDaoTao>(model);

                var checkCode = await _dataContext.SvXepHangNamDaoTaos.AnyAsync(x => x.NamThu == entity.NamThu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["NamThu.Existed", entity.NamThu.ToString()]}");
                }

               

                await _dataContext.SvXepHangNamDaoTaos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XepHangNamDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới Xếp hạng năm đào tạo: {entity.NamThu}",
                    ObjectCode = XepHangNamDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepHangNamDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXepHangNamDaoTaoCommand : IRequest<Unit>
    {
        public CreateManyXepHangNamDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXepHangNamDaoTaoCommand(CreateManyXepHangNamDaoTaoModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXepHangNamDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXepHangNamDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XepHangNamDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listNamThuAdd = model.listXepHangNamDaoTaoModels.Select(x => x.NamThu).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXepHangNamDaoTaoModel, SvXepHangNamDaoTao>(model);

                // Check data duplicate
                if (listNamThuAdd.Count() != listNamThuAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvXepHangNamDaoTaos.AnyAsync(x => listNamThuAdd.Contains(x.NamThu)))
                {
                    throw new ArgumentException($"{_localizer["XepLoai.Existed"]}");
                }

               
                var listEntity = model.listXepHangNamDaoTaoModels.Select(x => new SvXepHangNamDaoTao()
                {
                    NamThu = x.NamThu,
                    TuTinChi = x.TuTinChi,
                    DenTinChi = x.DenTinChi,
                    NamThuEn = x.NamThuEn
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXepHang).ToList();

                Log.Information($"Create many {XepHangNamDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Xếp hạng năm đào tạo từ file excel",
                    ObjectCode = XepHangNamDaoTaoConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XepHangNamDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXepHangNamDaoTaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepHangNamDaoTaoModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepHangNamDaoTaoCommand(int id, UpdateXepHangNamDaoTaoModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepHangNamDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepHangNamDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepHangNamDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepHangNamDaoTaos.FirstOrDefaultAsync(dt => dt.IdXepHang == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
             

                var checkCode = await _dataContext.SvXepHangNamDaoTaos.AnyAsync(x => x.IdXepHang != entity.IdXepHang && x.NamThu == model.NamThu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["NamThu.Existed", model.NamThu.ToString()]}");
                }


                Log.Information($"Before Update {XepHangNamDaoTaoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepHangNamDaoTaos.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepHangNamDaoTaoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepHangNamDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Xếp hạng năm đào tạo: {entity.NamThu}",
                    ObjectCode = XepHangNamDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepHangNamDaoTaoConstant.BuildCacheKey(entity.IdXepHang.ToString()));
                _cacheService.Remove(XepHangNamDaoTaoConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepHangNamDaoTaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepHangNamDaoTaoCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepHangNamDaoTaoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepHangNamDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepHangNamDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepHangNamDaoTaos.FirstOrDefaultAsync(x => x.IdXepHang == id);

                _dataContext.SvXepHangNamDaoTaos.Remove(entity);

                Log.Information($"Delete {XepHangNamDaoTaoConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Xếp hạng năm đào tạo: {entity.NamThu}",
                    ObjectCode = XepHangNamDaoTaoConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepHangNamDaoTaoConstant.BuildCacheKey());
                _cacheService.Remove(XepHangNamDaoTaoConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
