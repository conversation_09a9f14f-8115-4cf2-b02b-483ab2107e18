using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;


namespace Core.Business
{
    public class GetComboboxXepHangNamDaoTaoQuery : IRequest<List<XepHangNamDaoTaoSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại học tập thang 10 cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepHangNamDaoTaoQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepHangNamDaoTaoQuery, List<XepHangNamDaoTaoSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepHangNamDaoTaoSelectItemModel>> Handle(GetComboboxXepHangNamDaoTaoQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepHangNamDaoTaoConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepHangNamDaoTaos.OrderBy(x => x.NamThu)
                                select new XepHangNamDaoTaoSelectItemModel()
                                {
                                    IdXepHang = dt.IdXepHang,
                                    NamThu = dt.NamThu,
                                    TuTinChi = dt.TuTinChi,
                                    DenTinChi = dt.DenTinChi
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.NamThu.ToString().ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepHangNamDaoTaoQuery : IRequest<PaginationList<XepHangNamDaoTaoBaseModel>>
    {
        public XepHangNamDaoTaoFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang 10 có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepHangNamDaoTaoQuery(XepHangNamDaoTaoFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepHangNamDaoTaoQuery, PaginationList<XepHangNamDaoTaoBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepHangNamDaoTaoBaseModel>> Handle(GetFilterXepHangNamDaoTaoQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepHangNamDaoTaos
                            select new XepHangNamDaoTaoBaseModel
                            {
                                IdXepHang = dt.IdXepHang,
                                NamThu = dt.NamThu,
                                TuTinChi = dt.TuTinChi,
                                DenTinChi = dt.DenTinChi,
                                NamThuEn = dt.NamThuEn
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.NamThu.ToString().ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<XepHangNamDaoTaoBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepHangNamDaoTaoByIdQuery : IRequest<XepHangNamDaoTaoModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại học tập thang 10 theo id
        /// </summary>
        /// <param name="id">Id Xếp loại học tập thang 10</param>
        public GetXepHangNamDaoTaoByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepHangNamDaoTaoByIdQuery, XepHangNamDaoTaoModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepHangNamDaoTaoModel> Handle(GetXepHangNamDaoTaoByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepHangNamDaoTaoConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepHangNamDaoTaos.FirstOrDefaultAsync(x => x.IdXepHang == id);

                    return AutoMapperUtils.AutoMap<SvXepHangNamDaoTao, XepHangNamDaoTaoModel>(entity);
                });
                return item;
            }
        }
    }
}
