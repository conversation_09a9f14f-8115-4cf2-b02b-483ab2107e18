using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;


namespace Core.Business
{
    public class GetComboboxThanhPhanMonTheoHeQuery : IRequest<List<ThanhPhanMonTheoHeSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y thành phần môn theo hệ cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxThanhPhanMonTheoHeQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxThanhPhanMonTheoHeQuery, List<ThanhPhanMonTheoHeSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ThanhPhanMonTheoHeSelectItemModel>> Handle(GetComboboxThanhPhanMonTheoHeQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = ThanhPhanMonTheoHeConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvThanhPhanMonTheoHes.OrderBy(x => x.IdThanhPhan)
                                select new ThanhPhanMonTheoHeSelectItemModel()
                                {
                                    IdThanhPhan = dt.IdThanhPhan,
                                    IdHe = dt.IdHe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.IdHe.ToString().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterThanhPhanMonTheoHeQuery : IRequest<PaginationList<ThanhPhanMonTheoHeBaseModel>>
    {
        public ThanhPhanMonTheoHeFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách thành phần môn theo hệ có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterThanhPhanMonTheoHeQuery(ThanhPhanMonTheoHeFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterThanhPhanMonTheoHeQuery, PaginationList<ThanhPhanMonTheoHeBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ThanhPhanMonTheoHeBaseModel>> Handle(GetFilterThanhPhanMonTheoHeQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvThanhPhanMonTheoHes
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            join mon in _dataContext.SvThanhPhanMons on dt.IdThanhPhan equals mon.IdThanhPhan
                            select new ThanhPhanMonTheoHeBaseModel
                            {
                                IdThanhPhan = dt.IdThanhPhan,
                                TenThanhPhan= mon.TenThanhPhan,
                                IdHe = dt.IdHe,
                                TenHe = he.TenHe,
                                Stt = dt.Stt,
                                TyLe = dt.TyLe,
                                TyLeNhom = dt.TyLeNhom,
                                NhomThanhPhan = dt.NhomThanhPhan,
                                ChonMacDinh = dt.ChonMacDinh

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.IdHe.ToString().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<ThanhPhanMonTheoHeBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetThanhPhanMonTheoHeByIdQuery : IRequest<ThanhPhanMonTheoHeModel>
    {
        public int IdThanhPhan { get; set; }
        public int IdHe { get; set; }

        /// <summary>
        /// Lấy thông tin thành phần môn theo hệ theo id
        /// </summary>
        /// <param name="id">Id thành phần môn theo hệ</param>
        public GetThanhPhanMonTheoHeByIdQuery(int idThanhPhan, int idHe)
        {
            IdThanhPhan = idThanhPhan;
            IdHe = idHe;
        }

        public class Handler : IRequestHandler<GetThanhPhanMonTheoHeByIdQuery, ThanhPhanMonTheoHeModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ThanhPhanMonTheoHeModel> Handle(GetThanhPhanMonTheoHeByIdQuery request, CancellationToken cancellationToken)
            {
                var idThanhPhan = request.IdThanhPhan;
                var idHe = request.IdHe;

                string cacheKey = ThanhPhanMonTheoHeConstant.BuildCacheKey(idThanhPhan.ToString() + "_" + idHe.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvThanhPhanMonTheoHes.FirstOrDefaultAsync(x => x.IdThanhPhan == idThanhPhan && x.IdHe == idHe);

                    return AutoMapperUtils.AutoMap<SvThanhPhanMonTheoHe, ThanhPhanMonTheoHeModel>(entity);
                });
                return item;
            }
        }
    }
}
