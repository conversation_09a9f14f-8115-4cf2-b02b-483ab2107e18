using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DiemRenLuyenQuyDoiSelectItemModel
    {
        public int IdXepLoai { get; set; }
        public string XepLoai { get; set; }
    }

    public class DiemRenLuyenQuyDoiBaseModel
    {
        public int IdXepLoai { get; set; }
        public string XepLoai { get; set; }
        public float TuDiem { get; set; }
        public float DenDiem { get; set; }
        public float DiemCong10 { get; set; }
        public float DiemCong4 { get; set; }
    }


    public class DiemRenLuyenQuyDoiModel : DiemRenLuyenQuyDoiBaseModel
    {

    }

    public class DiemRenLuyenQuyDoiFilterModel : BaseQueryFilterModel
    {
        public DiemRenLuyenQuyDoiFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepLoai";
        }
    }

    public class CreateDiemRenLuyenQuyDoiModel
    {
        [Required(ErrorMessage = "DiemRenLuyenQuyDoi.IdXepLoai.NotRequire")]
        public int IdXepLoai { get; set; }

        [MaxLength(50, ErrorMessage = "DiemRenLuyenQuyDoi.XepLoai.MaxLength(50)")]
        [Required(ErrorMessage = "DiemRenLuyenQuyDoi.XepLoai.NotRequire")]
        public string XepLoai { get; set; }

        [Required(ErrorMessage = "DiemRenLuyenQuyDoi.TuDiem.NotRequire")]
        public float TuDiem { get; set; }

        [Required(ErrorMessage = "DiemRenLuyenQuyDoi.DenDiem.NotRequire")]
        public float DenDiem { get; set; }

        [Required(ErrorMessage = "DiemRenLuyenQuyDoi.DiemCong10.NotRequire")]
        public float DiemCong10 { get; set; }

        [Required(ErrorMessage = "DiemRenLuyenQuyDoi.DiemCong4.NotRequire")]
        public float DiemCong4 { get; set; }

    }

    public class CreateManyDiemRenLuyenQuyDoiModel
    {
        public List<CreateDiemRenLuyenQuyDoiModel> listDiemRenLuyenQuyDoiModels { get; set; }
    }

    public class UpdateDiemRenLuyenQuyDoiModel : CreateDiemRenLuyenQuyDoiModel
    {
        public void UpdateEntity(SvDiemRenLuyenQuyDoi input)
        {
            input.IdXepLoai = IdXepLoai;
            input.XepLoai = XepLoai;
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.DiemCong10 = DiemCong10;
            input.DiemCong4 = DiemCong4;

        }
    }
}
