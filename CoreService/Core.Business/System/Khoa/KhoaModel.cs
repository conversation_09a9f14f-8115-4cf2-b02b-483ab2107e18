using Core.Data;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class KhoaSelectItemModel
    {
        public int IdKhoa { get; set; }
        public string Ma<PERSON>hoa { get; set; }
        public string TenKhoa { get; set; }
        public string TenKhoaEn { get; set; }
        public int IdHe { get; set; }
    }

    public class KhoaBaseModel : KhoaSelectItemModel
    {

    }


    public class KhoaModel : KhoaSelectItemModel
    {

    }

    public class KhoaFilterModel : BaseQueryFilterModel
    {
        public KhoaFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdKhoa";
        }
    }

    public class CreateKhoaModel
    {
        [Required(ErrorMessage = "Khoa.IdKhoa.NotRequire")]
        public int IdKhoa { get; set; }

        [MaxLength(5, ErrorMessage = "Khoa.MaKhoa.MaxLength(5)")]
        [Required(ErrorMessage = "Khoa.MaKhoa.NotRequire")]
        public string MaKhoa { get; set; }

        [MaxLength(50, ErrorMessage = "Khoa.Khoa.MaxLength(50)")]
        [Required(ErrorMessage = "Khoa.Khoa.NotRequire")]
        public string TenKhoa { get; set; }

        [MaxLength(50, ErrorMessage = "Khoa.KhoaEn.MaxLength(50)")]
        public string TenKhoaEn { get; set; }

    }

    public class CreateManyKhoaModel
    {
        public List<CreateKhoaModel> listKhoaModels { get; set; }
    }

    public class UpdateKhoaModel : CreateKhoaModel
    {
        public void UpdateEntity(SvKhoa input)
        {
            input.IdKhoa = IdKhoa;
            input.MaKhoa = MaKhoa;
            input.TenKhoa = TenKhoa;
            input.TenKhoaEn = TenKhoaEn;

        }
    }
}
