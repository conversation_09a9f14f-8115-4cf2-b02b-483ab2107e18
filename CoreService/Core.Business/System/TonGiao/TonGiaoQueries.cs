using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxTonGiaoQuery : IRequest<List<TonGiaoSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy tôn giáo cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxTonGiaoQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxTonGiaoQuery, List<TonGiaoSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TonGiaoSelectItemModel>> Handle(GetComboboxTonGiaoQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = TonGiaoConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvTonGiaos.OrderBy(x => x.TonGiao)
                                select new TonGiaoSelectItemModel()
                                {
                                    IdTonGiao = dt.IdTonGiao,
                                    MaTonGiao = dt.MaTonGiao,
                                    TonGiao = dt.TonGiao
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TonGiao.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterTonGiaoQuery : IRequest<PaginationList<TonGiaoBaseModel>>
    {
        public TonGiaoFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tôn giáo có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTonGiaoQuery(TonGiaoFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTonGiaoQuery, PaginationList<TonGiaoBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TonGiaoBaseModel>> Handle(GetFilterTonGiaoQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvTonGiaos
                            select new TonGiaoBaseModel
                            {
                                IdTonGiao = dt.IdTonGiao,
                                MaTonGiao = dt.MaTonGiao,
                                TonGiao = dt.TonGiao,
                                TonGiaoEn = dt.TonGiaoEn

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TonGiao.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<TonGiaoBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetTonGiaoByIdQuery : IRequest<TonGiaoModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Tôn giáo theo id
        /// </summary>
        /// <param name="id">Id tôn giáo</param>
        public GetTonGiaoByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTonGiaoByIdQuery, TonGiaoModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TonGiaoModel> Handle(GetTonGiaoByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = TonGiaoConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvTonGiaos.FirstOrDefaultAsync(x => x.IdTonGiao == id);

                    return AutoMapperUtils.AutoMap<SvTonGiao, TonGiaoModel>(entity);
                });
                return item;
            }
        }
    }
}
