using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateHocKyDangKyCommand : IRequest<Unit>
    {
        public CreateHocKyDangKyModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateHocKyDangKyCommand(CreateHocKyDangKyModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateHocKyDangKyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateHocKyDangKyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {HocKyDangKyConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateHocKyDangKyModel, TkbHocKyDangKy>(model);

                var checkCode = await _dataContext.TkbHocKyDangKys.AnyAsync(x =>  x.Dot == entity.Dot && x.HocKy == entity.HocKy && x.NamHoc == entity.NamHoc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HocKyDangKy.Existed"]}");
                }

                await _dataContext.TkbHocKyDangKys.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {HocKyDangKyConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới học kỳ đăng ký Đợt {entity.Dot} - Học kỳ {entity.HocKy} - Năm học{entity.NamHoc}",
                    ObjectCode = HocKyDangKyConstant.CachePrefix,
                    ObjectId = entity.KyDangKy.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocKyDangKyConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyHocKyDangKyCommand : IRequest<Unit>
    {
        public CreateManyHocKyDangKyModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyHocKyDangKyCommand(CreateManyHocKyDangKyModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyHocKyDangKyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyHocKyDangKyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {HocKyDangKyConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listHocKyDangKyAdd = model.listHocKyDangKyModels.Select(x => new{ x.Dot, x.HocKy, x.NamHoc }).ToList();

                var entity = AutoMapperUtils.AutoMap<CreateManyHocKyDangKyModel, TkbHocKyDangKy>(model);

                // Check data duplicate
                if (listHocKyDangKyAdd.Count() != listHocKyDangKyAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB

                for (var i = 0; i < listHocKyDangKyAdd.Count(); i++)
                {
                    if (await _dataContext.TkbHocKyDangKys.AnyAsync(x => (x.Dot == listHocKyDangKyAdd[i].Dot && x.HocKy == listHocKyDangKyAdd[i].HocKy && x.NamHoc == listHocKyDangKyAdd[i].NamHoc)))
                    {
                        throw new ArgumentException($"{_localizer["HocKyDangKy.Existed"]}");
                    }
                }

                var listEntity = model.listHocKyDangKyModels.Select(x => new TkbHocKyDangKy()
                {
                    Dot = x.Dot,
                    HocKy = x.HocKy,
                    NamHoc = x.NamHoc,
                    TuNgay = x.TuNgay,
                    DenNgay = x.DenNgay,
                    ChonDangKy = x.ChonDangKy,
                    KhoaTkb = x.KhoaTkb

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.KyDangKy).ToList();

                Log.Information($"Create many {HocKyDangKyConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import học kỳ đăng ký từ file excel",
                    ObjectCode = HocKyDangKyConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(HocKyDangKyConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateHocKyDangKyCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateHocKyDangKyModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateHocKyDangKyCommand(int id, UpdateHocKyDangKyModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateHocKyDangKyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateHocKyDangKyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {HocKyDangKyConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbHocKyDangKys.FirstOrDefaultAsync(dt => dt.KyDangKy == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.TkbHocKyDangKys.AnyAsync(x => x.Dot == model.Dot && x.HocKy == model.HocKy && x.NamHoc == model.NamHoc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HocKyDangKy.Existed"] }");
                }

                Log.Information($"Before Update {HocKyDangKyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbHocKyDangKys.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {HocKyDangKyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {HocKyDangKyConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật học kỳ đăng ký Đợt {entity.Dot} - Học kỳ {entity.HocKy} - Năm học{entity.NamHoc}",
                    ObjectCode = HocKyDangKyConstant.CachePrefix,
                    ObjectId = entity.KyDangKy.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocKyDangKyConstant.BuildCacheKey(entity.KyDangKy.ToString()));
                _cacheService.Remove(HocKyDangKyConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteHocKyDangKyCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteHocKyDangKyCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteHocKyDangKyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteHocKyDangKyCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {HocKyDangKyConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbHocKyDangKys.FirstOrDefaultAsync(x => x.KyDangKy == id);

                _dataContext.TkbHocKyDangKys.Remove(entity);

                Log.Information($"Delete {HocKyDangKyConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa học kỳ đăng ký Đợt {entity.Dot} - Học kỳ {entity.HocKy} - Năm học{entity.NamHoc}",
                    ObjectCode = HocKyDangKyConstant.CachePrefix,
                    ObjectId = entity.KyDangKy.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocKyDangKyConstant.BuildCacheKey());
                _cacheService.Remove(HocKyDangKyConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
