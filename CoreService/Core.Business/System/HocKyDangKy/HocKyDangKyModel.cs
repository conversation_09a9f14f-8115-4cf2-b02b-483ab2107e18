using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class HocKyDangKySelectItemModel
    {
        public int KyDangKy { get; set; }
        public int Dot { get; set; }
        public int HocKy { get; set; }
        public string NamHoc { get; set; }
       
    }

    public class HocKyDangKyBaseModel
    {
        public int KyDangKy { get; set; }
        public int Dot { get; set; }
        public int HocKy { get; set; }
        public string NamHoc { get; set; }
        public DateTime TuNgay { get; set; }
        public DateTime DenNgay { get; set; }
        public bool ChonDangKy { get; set; }
        public bool KhoaTkb { get; set; }

    }


    public class HocKyDangKyModel : HocKyDangKyBaseModel
    {

    }

    public class HocKyDangKyFilterModel : BaseQueryFilterModel
    {
        public HocKyDangKyFilterModel()
        {
            Ascending = "desc";
            PropertyName = "KyDangKy";
        }
    }

    public class CreateHocKyDangKyModel
    {

        [Required(ErrorMessage = "HocKyDangKy.Dot.NotRequire")]
        public int Dot { get; set; }

        [Required(ErrorMessage = "HocKyDangKy.HocKy.NotRequire")]
        public int HocKy { get; set; }

        [MaxLength(50, ErrorMessage = "HocKyDangKy.NamHoc.MaxLength(10)")]
        [Required(ErrorMessage = "HocKyDangKy.NamHoc.NotRequire")]
        public string NamHoc { get; set; }

        [Required(ErrorMessage = "HocKyDangKy.TuNgay.NotRequire")]
        public DateTime TuNgay { get; set; }

        [Required(ErrorMessage = "HocKyDangKy.DenNgay.NotRequire")]
        public DateTime DenNgay { get; set; }

        [Required(ErrorMessage = "HocKyDangKy.ChonHocKy.NotRequire")]
        public bool ChonDangKy { get; set; }

        [Required(ErrorMessage = "HocKyDangKy.HocKy.NotRequire")]
        public bool KhoaTkb { get; set; }

    }

    public class CreateManyHocKyDangKyModel
    {
        public List<CreateHocKyDangKyModel> listHocKyDangKyModels { get; set; }
    }

    public class UpdateHocKyDangKyModel : CreateHocKyDangKyModel
    {
        public void UpdateEntity(TkbHocKyDangKy input)
        {
            input.Dot = Dot;
            input.HocKy = HocKy;
            input.NamHoc = NamHoc;
            input.TuNgay = TuNgay;
            input.DenNgay = DenNgay;
            input.ChonDangKy = ChonDangKy;
            input.KhoaTkb = KhoaTkb;

        }
    }
}
