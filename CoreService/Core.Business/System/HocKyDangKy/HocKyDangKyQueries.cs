using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxHocKyDangKyQuery : IRequest<List<HocKyDangKySelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y học kỳ đăng ký cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHocKyDangKyQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHocKyDangKyQuery, List<HocKyDangKySelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HocKyDangKySelectItemModel>> Handle(GetComboboxHocKyDangKyQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = HocKyDangKyConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbHocKyDangKys.OrderByDescending(x => x.KyDangKy)
                                select new HocKyDangKySelectItemModel()
                                {
                                    KyDangKy = dt.KyDangKy,
                                    HocKy = dt.HocKy,
                                    NamHoc = dt.NamHoc,
                                    Dot = dt.Dot
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.NamHoc.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterHocKyDangKyQuery : IRequest<PaginationList<HocKyDangKyBaseModel>>
    {
        public HocKyDangKyFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách học kỳ đăng ký có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterHocKyDangKyQuery(HocKyDangKyFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterHocKyDangKyQuery, PaginationList<HocKyDangKyBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<HocKyDangKyBaseModel>> Handle(GetFilterHocKyDangKyQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TkbHocKyDangKys
                            select new HocKyDangKyBaseModel
                            {
                                KyDangKy = dt.KyDangKy,
                                Dot = dt.Dot,
                                HocKy = dt.HocKy,
                                NamHoc = dt.NamHoc,
                                TuNgay = dt.TuNgay,
                                DenNgay = dt.DenNgay,
                                ChonDangKy = dt.ChonDangKy,
                                KhoaTkb = dt.KhoaTkb

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.NamHoc.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<HocKyDangKyBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetHocKyDangKyByIdQuery : IRequest<HocKyDangKyModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin học kỳ đăng ký theo id
        /// </summary>
        /// <param name="id">Id học kỳ đăng ký</param>
        public GetHocKyDangKyByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetHocKyDangKyByIdQuery, HocKyDangKyModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<HocKyDangKyModel> Handle(GetHocKyDangKyByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = HocKyDangKyConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbHocKyDangKys.FirstOrDefaultAsync(x => x.KyDangKy == id);

                    return AutoMapperUtils.AutoMap<TkbHocKyDangKy, HocKyDangKyModel>(entity);
                });
                return item;
            }
        }
    }
}
