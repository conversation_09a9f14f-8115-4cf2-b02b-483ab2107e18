using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateBenhVienCommand : IRequest<Unit>
    {
        public CreateBenhVienModel Model { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Thêm mới bệnh viện
        /// </summary>
        /// <param name="model">Thông tin bệnh viện cần thêm mới</param>
        /// <param name="requestUser">Thông tin người request</param>
        public CreateBenhVienCommand(CreateBenhVienModel model, RequestUser requestUser)
        {
            Model = model;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<CreateBenhVienCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(CreateBenhVienCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var requestUser = request.RequestUser;
                Log.Information($"Create {BenhVienConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateBenhVienModel, SvBenhVien>(model);

                var checkMaBenhVien = await _dataContext.SvBenhViens.AnyAsync(x => x.MaBenhVien == entity.MaBenhVien);
                if (checkMaBenhVien)
                {
                    throw new ArgumentException($"{_localizer["system-application.MaBenhVien.existed"]}");
                }

                await _dataContext.SvBenhViens.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {BenhVienConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới bệnh viện mã: {entity.MaBenhVien}",
                    ObjectCode = BenhVienConstant.CachePrefix,
                    ObjectId = entity.IdBenhVien.ToString()
                });

                //Xóa cache
                _cacheService.Remove(BenhVienConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
