using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteBenhVienCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Xóa bệnh viện theo danh sách truyền vào
        /// </summary>
        /// <param name="id">Id bệnh viện cần xóa</param>
        /// <param name="requestUser">Thông tin người dùng thực hiện thao tác</param>
        public DeleteBenhVienCommand(int id, RequestUser requestUser)
        {
            Id = id;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<DeleteBenhVienCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(DeleteBenhVienCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var requestUser = request.RequestUser;
                Log.Information($"Delete {BenhVienConstant.CachePrefix}: {id}");

                var dt = await _dataContext.SvBenhViens.FirstOrDefaultAsync(x => x.IdBenhVien == id);

                if (dt == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.SvBenhViens.Remove(dt);

                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa bệnh viện mã: {dt.MaBenhVien}",
                    ObjectCode = BenhVienConstant.CachePrefix
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(BenhVienConstant.BuildCacheKey(id.ToString()));
                _cacheService.Remove(BenhVienConstant.BuildCacheKey());

                Log.Information($"Delete {BenhVienConstant.CachePrefix} completed");

                return Unit.Value;
            }
        }
    }
}
