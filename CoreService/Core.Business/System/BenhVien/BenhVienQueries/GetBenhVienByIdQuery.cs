using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetBenhVienByIdQuery : IRequest<BenhVienModel>
    {
        public int IdBenhVien { get; set; }

        /// <summary>
        /// L<PERSON>y thông tin mẫu email theo id
        /// </summary>
        /// <param name="id">Id mẫu email</param>
        public GetBenhVienByIdQuery(int idBenhVien)
        {
            IdBenhVien = idBenhVien;
        }

        public class Handler : IRequestHandler<GetBenhVienByIdQuery, BenhVienModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<BenhVienModel> Handle(GetBenhVienByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.IdBenhVien;
                string cacheKey = BenhVienConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvBenhViens.AsNoTracking().FirstOrDefaultAsync(x => x.IdBenhVien == id);

                    return AutoMapperUtils.AutoMap<SvBenhVien, BenhVienModel>(entity);
                });
                return item;
            }
        }
    }
}
