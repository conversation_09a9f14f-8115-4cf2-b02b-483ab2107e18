using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxHanhViQuery : IRequest<List<HanhViSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy hành vi kỷ luật cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHanhViQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHanhViQuery, List<HanhViSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HanhViSelectItemModel>> Handle(GetComboboxHanhViQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = HanhViKyLuatConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvHanhVis.OrderBy(x => x.HanhVi)
                                select new HanhViSelectItemModel()
                                {
                                    IdHanhVi = dt.IdHanhVi,
                                    MaHanhVi = dt.MaHanhVi,
                                    HanhVi = dt.HanhVi
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.HanhVi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterHanhViQuery : IRequest<PaginationList<HanhViBaseModel>>
    {
        public HanhViFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách hành vi kỷ luật có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterHanhViQuery(HanhViFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterHanhViQuery, PaginationList<HanhViBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<HanhViBaseModel>> Handle(GetFilterHanhViQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvHanhVis
                            select new HanhViBaseModel
                            {
                                IdHanhVi = dt.IdHanhVi,
                                MaHanhVi = dt.MaHanhVi,
                                HanhVi = dt.HanhVi,

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.HanhVi.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<HanhViBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetHanhViByIdQuery : IRequest<HanhViModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin hành vi kỷ luật theo id
        /// </summary>
        /// <param name="id">Id hành vi kỷ luật</param>
        public GetHanhViByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetHanhViByIdQuery, HanhViModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<HanhViModel> Handle(GetHanhViByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = HanhViKyLuatConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvHanhVis.FirstOrDefaultAsync(x => x.IdHanhVi == id);

                    return AutoMapperUtils.AutoMap<SvHanhVi, HanhViModel>(entity);
                });
                return item;
            }
        }
    }
}
