using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdatePhuongAnCommand : IRequest<Unit>
    {
        public UpdatePhuongAnModel Model { get; set; }
        public RequestUser RequestUser { get; set; }

        /// <summary>
        /// Cập nhật phương án
        /// </summary>
        /// <param name="model">Thông tin phương án cần cập nhật</param>
        /// <param name="requestUser">Thông tin người dùng thực hiện thao tác</param>
        public UpdatePhuongAnCommand(UpdatePhuongAnModel model, RequestUser requestUser)
        {
            Model = model;
            RequestUser = requestUser;
        }

        public class Handler : IRequestHandler<UpdatePhuongAnCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }

            public async Task<Unit> Handle(UpdatePhuongAnCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var requestUser = request.RequestUser;
                Log.Information($"Update {PhuongAnConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.SvPhuongAns.FirstOrDefaultAsync(x => x.IdPhuongAn == model.IdPhuongAn);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {PhuongAnConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvPhuongAns.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {PhuongAnConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(PhuongAnConstant.BuildCacheKey(entity.IdPhuongAn.ToString()));
                _cacheService.Remove(PhuongAnConstant.BuildCacheKey());

                requestUser.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập phương án mã: {entity.MaPhuongAn}",
                    ObjectCode = PhuongAnConstant.CachePrefix,
                    ObjectId = entity.IdPhuongAn.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
