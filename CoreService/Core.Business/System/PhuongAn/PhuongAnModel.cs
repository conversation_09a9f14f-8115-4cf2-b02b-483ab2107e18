using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class PhuongAnBaseModel
    {
        public int IdPhuongAn { get; set; }

        [Required(ErrorMessage = "PhuongAn.MaPhuongAn.NotRequire")]
        [MaxLength(50, ErrorMessage = "PhuongAn.MaPhuongAn.MaxLength(50)")]
        public string MaPhuongAn { get; set; }

        [Required(ErrorMessage = "PhuongAn.TenPhuongAn.NotRequire")]
        [MaxLength(255, ErrorMessage = "PhuongAn.TenPhuongAn.MaxLength(255)")]
        public string TenPhuongAn { get; set; }

        public string NoiDung { get; set; }
    }

    public class PhuongAnModel : PhuongAnBaseModel
    {
    }

    public class CreatePhuongAnModel
    {
        [Required(ErrorMessage = "PhuongAn.MaPhuongAn.NotRequire")]
        [MaxLength(50, ErrorMessage = "PhuongAn.MaPhuongAn.MaxLength(50)")]
        public string MaPhuongAn { get; set; }

        [Required(ErrorMessage = "PhuongAn.TenPhuongAn.NotRequire")]
        [MaxLength(255, ErrorMessage = "PhuongAn.TenPhuongAn.MaxLength(255)")]
        public string TenPhuongAn { get; set; }

        public string NoiDung { get; set; }
    }

    public class CreateManyPhuongAnModel
    {
        public List<CreatePhuongAnModel> ListPhuongAn { get; set; }
    }

    public class UpdatePhuongAnModel : CreatePhuongAnModel
    {
        [Required(ErrorMessage = "PhuongAn.IdPhuongAn.NotRequire")]
        public int IdPhuongAn { get; set; }

        public void UpdateEntity(SvPhuongAn entity)
        {
            entity.IdPhuongAn = IdPhuongAn;
            entity.MaPhuongAn = MaPhuongAn;
            entity.TenPhuongAn = TenPhuongAn;
            entity.NoiDung = NoiDung;
        }
    }

    public class PhuongAnSelectItemModel
    {
        public int IdPhuongAn { get; set; }
        public string MaPhuongAn { get; set; }
        public string TenPhuongAn { get; set; }
    }

    public class PhuongAnQueryFilter : BaseQueryFilterModel
    {
    }
}
