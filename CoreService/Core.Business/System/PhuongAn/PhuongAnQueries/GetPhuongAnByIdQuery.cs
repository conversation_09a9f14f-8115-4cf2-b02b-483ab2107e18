using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetPhuongAnByIdQuery : IRequest<PhuongAnModel>
    {
        public int IdPhuongAn { get; set; }

        /// <summary>
        /// Lấy thông tinphương án theo id
        /// </summary>
        /// <param name="id">Id phương án</param>
        public GetPhuongAnByIdQuery(int idPhuongAn)
        {
            IdPhuongAn = idPhuongAn;
        }

        public class Handler : IRequestHandler<GetPhuongAnByIdQuery, PhuongAnModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<PhuongAnModel> Handle(GetPhuongAnByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.IdPhuongAn;
                string cacheKey = PhuongAnConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvPhuongAns.AsNoTracking().FirstOrDefaultAsync(x => x.IdPhuongAn == id);

                    return AutoMapperUtils.AutoMap<SvPhuongAn, PhuongAnModel>(entity);
                });
                return item;
            }
        }
    }
}
