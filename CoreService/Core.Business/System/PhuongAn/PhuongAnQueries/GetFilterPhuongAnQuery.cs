using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetFilterPhuongAnQuery : IRequest<PaginationList<PhuongAnBaseModel>>
    {
        public PhuongAnQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách bệnh viện theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterPhuongAnQuery(PhuongAnQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterPhuongAnQuery, PaginationList<PhuongAnBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<PhuongAnBaseModel>> Handle(GetFilterPhuongAnQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvPhuongAns.AsNoTracking()
                            select dt);

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenPhuongAn.ToLower().Contains(ts) || x.MaPhuongAn.ToLower().Contains(ts));
                }

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                var listResult = AutoMapperUtils.AutoMap<SvPhuongAn, PhuongAnBaseModel>(listData);

                return new PaginationList<PhuongAnBaseModel>()
                {
                    DataCount = listResult.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listResult
                };
            }
        }
    }
}
