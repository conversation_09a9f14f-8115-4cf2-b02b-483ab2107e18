using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{

    public class CreateQuocTichCommand : IRequest<Unit>
    {
        public CreateQuocTichModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateQuocTichCommand(CreateQuocTichModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateQuocTichCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateQuocTichCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {QuocTichConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateQuocTichModel, SvQuocTich>(model);

                var checkCode = await _dataContext.SvQuocTichs.AnyAsync(x => x.IdQuocTich == entity.IdQuocTich || x.QuocTich == entity.QuocTich || x.MaQuocTich == entity.MaQuocTich);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["QuocTich.Existed", entity.QuocTich.ToString()]}");
                }

                await _dataContext.SvQuocTichs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {QuocTichConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới quốc tịch: {entity.QuocTich}",
                    ObjectCode = QuocTichConstant.CachePrefix,
                    ObjectId = entity.IdQuocTich.ToString()
                });

                //Xóa cache
                _cacheService.Remove(QuocTichConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyQuocTichCommand : IRequest<Unit>
    {
        public CreateManyQuocTichModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyQuocTichCommand(CreateManyQuocTichModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyQuocTichCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyQuocTichCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {QuocTichConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listQuocTichAdd = model.listQuocTichModels.Select(x => x.QuocTich).ToList();
                var listMaQuocTichAdd = model.listQuocTichModels.Select(x => x.MaQuocTich).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyQuocTichModel, SvQuocTich>(model);

                // Check data duplicate
                if (listQuocTichAdd.Count() != listQuocTichAdd.Distinct().Count() || listMaQuocTichAdd.Count() != listMaQuocTichAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvQuocTichs.AnyAsync(x => listQuocTichAdd.Contains(x.QuocTich)) || await _dataContext.SvQuocTichs.AnyAsync(x => listMaQuocTichAdd.Contains(x.MaQuocTich)))
                {
                    throw new ArgumentException($"{_localizer["QuocTich.Existed"]}");
                }

                var listEntity = model.listQuocTichModels.Select(x => new SvQuocTich()
                {
                    IdQuocTich = x.IdQuocTich,
                    MaQuocTich = x.MaQuocTich,
                    QuocTich = x.QuocTich

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdQuocTich).ToList();

                Log.Information($"Create many {QuocTichConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import quốc tịch từ file excel",
                    ObjectCode = QuocTichConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(QuocTichConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateQuocTichCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateQuocTichModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateQuocTichCommand(int id, UpdateQuocTichModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateQuocTichCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateQuocTichCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {QuocTichConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvQuocTichs.FirstOrDefaultAsync(dt => dt.IdQuocTich == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                
                var checkCode = await _dataContext.SvQuocTichs.AnyAsync(x => (x.QuocTich == model.QuocTich || x.MaQuocTich == model.MaQuocTich) && x.IdQuocTich != model.IdQuocTich);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["QuocTich.Existed", model.QuocTich.ToString()]}");
                }

                Log.Information($"Before Update {QuocTichConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvQuocTichs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {QuocTichConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {QuocTichConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật quốc tịch: {entity.QuocTich}",
                    ObjectCode = QuocTichConstant.CachePrefix,
                    ObjectId = entity.IdQuocTich.ToString()
                });

                //Xóa cache
                _cacheService.Remove(QuocTichConstant.BuildCacheKey(entity.IdQuocTich.ToString()));
                _cacheService.Remove(QuocTichConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteQuocTichCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteQuocTichCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteQuocTichCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteQuocTichCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {QuocTichConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvQuocTichs.FirstOrDefaultAsync(x => x.IdQuocTich == id);

                _dataContext.SvQuocTichs.Remove(entity);

                Log.Information($"Delete {QuocTichConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa quốc tịch: {entity.QuocTich}",
                    ObjectCode = QuocTichConstant.CachePrefix,
                    ObjectId = entity.IdQuocTich.ToString()
                });

                //Xóa cache
                _cacheService.Remove(QuocTichConstant.BuildCacheKey());
                _cacheService.Remove(QuocTichConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
