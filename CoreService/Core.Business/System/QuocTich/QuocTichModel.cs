using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class QuocTichSelectItemModel
    {
        public int IdQuocTich { get; set; }
        public string MaQuocTich { get; set; }
        public string QuocTich { get; set; }
    }

    public class QuocTichBaseModel : QuocTichSelectItemModel
    {

    }


    public class QuocTichModel : QuocTichSelectItemModel
    {

    }

    public class QuocTichFilterModel : BaseQueryFilterModel
    {
        public QuocTichFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdQuocTich";
        }
    }

    public class CreateQuocTichModel
    {
        [Required(ErrorMessage = "QuocTich.IdQuocTich.NotRequire")]
        public int IdQuocTich { get; set; }

        [MaxLength(10, ErrorMessage = "QuocTich.MaQuocTich.MaxLength(10)")]
        [Required(ErrorMessage = "QuocTich.MaQuocTich.NotRequire")]
        public string MaQuocTich { get; set; }

        [MaxLength(50, ErrorMessage = "QuocTich.QuocTich.MaxLength(50)")]
        [Required(ErrorMessage = "QuocTich.QuocTich.NotRequire")]
        public string QuocTich { get; set; }


    }

    public class CreateManyQuocTichModel
    {
        public List<CreateQuocTichModel> listQuocTichModels { get; set; }
    }

    public class UpdateQuocTichModel : CreateQuocTichModel
    {
        public void UpdateEntity(SvQuocTich input)
        {
            input.IdQuocTich = IdQuocTich;
            input.MaQuocTich = MaQuocTich;
            input.QuocTich = MaQuocTich;
            input.QuocTich = QuocTich;

        }
    }
}
