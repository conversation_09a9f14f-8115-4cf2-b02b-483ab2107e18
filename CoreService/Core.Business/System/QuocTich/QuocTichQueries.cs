
using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxQuocTichQuery : IRequest<List<QuocTichSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y hệ cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxQuocTichQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxQuocTichQuery, List<QuocTichSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<QuocTichSelectItemModel>> Handle(GetComboboxQuocTichQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = QuocTichConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvQuocTichs.OrderBy(x => x.QuocTich)
                                select new QuocTichSelectItemModel()
                                {
                                    IdQuocTich = dt.IdQuocTich,
                                    MaQuocTich = dt.MaQuocTich,
                                    QuocTich = dt.QuocTich
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.QuocTich.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterQuocTichQuery : IRequest<PaginationList<QuocTichBaseModel>>
    {
        public QuocTichFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách quốc tịch có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterQuocTichQuery(QuocTichFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterQuocTichQuery, PaginationList<QuocTichBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<QuocTichBaseModel>> Handle(GetFilterQuocTichQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvQuocTichs
                            select new QuocTichBaseModel
                            {
                                IdQuocTich = dt.IdQuocTich,
                                MaQuocTich = dt.MaQuocTich,
                                QuocTich = dt.QuocTich

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.QuocTich.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<QuocTichBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetQuocTichByIdQuery : IRequest<QuocTichModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin quốc tịch theo id
        /// </summary>
        /// <param name="id">Id quốc tịch</param>
        public GetQuocTichByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetQuocTichByIdQuery, QuocTichModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<QuocTichModel> Handle(GetQuocTichByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = QuocTichConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvQuocTichs.FirstOrDefaultAsync(x => x.IdQuocTich == id);

                    return AutoMapperUtils.AutoMap<SvQuocTich, QuocTichModel>(entity);
                });
                return item;
            }
        }
    }
}
