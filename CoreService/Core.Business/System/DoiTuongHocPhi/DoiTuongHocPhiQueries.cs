using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxDoiTuongHocPhiQuery : IRequest<List<DoiTuongHocPhiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy đôi tượng học phí cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxDoiTuongHocPhiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxDoiTuongHocPhiQuery, List<DoiTuongHocPhiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<DoiTuongHocPhiSelectItemModel>> Handle(GetComboboxDoiTuongHocPhiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = DoiTuongHocPhiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvDoiTuongHocPhis.OrderBy(x => x.DoiTuongHocPhi)
                                select new DoiTuongHocPhiSelectItemModel()
                                {
                                    IdDoiTuongHocPhi = dt.IdDoiTuongHocPhi,
                                    DoiTuongHocPhi = dt.DoiTuongHocPhi
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.DoiTuongHocPhi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterDoiTuongHocPhiQuery : IRequest<PaginationList<DoiTuongHocPhiBaseModel>>
    {
        public DoiTuongHocPhiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách đôi tượng học phí có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterDoiTuongHocPhiQuery(DoiTuongHocPhiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterDoiTuongHocPhiQuery, PaginationList<DoiTuongHocPhiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<DoiTuongHocPhiBaseModel>> Handle(GetFilterDoiTuongHocPhiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvDoiTuongHocPhis
                            select new DoiTuongHocPhiBaseModel
                            {
                                IdDoiTuongHocPhi = dt.IdDoiTuongHocPhi,
                                DoiTuongHocPhi = dt.DoiTuongHocPhi,


                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.DoiTuongHocPhi.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<DoiTuongHocPhiBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetDoiTuongHocPhiByIdQuery : IRequest<DoiTuongHocPhiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin đôi tượng học phí theo id
        /// </summary>
        /// <param name="id">Id đôi tượng học phí</param>
        public GetDoiTuongHocPhiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetDoiTuongHocPhiByIdQuery, DoiTuongHocPhiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<DoiTuongHocPhiModel> Handle(GetDoiTuongHocPhiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = DoiTuongHocPhiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvDoiTuongHocPhis.FirstOrDefaultAsync(x => x.IdDoiTuongHocPhi == id);

                    return AutoMapperUtils.AutoMap<SvDoiTuongHocPhi, DoiTuongHocPhiModel>(entity);
                });
                return item;
            }
        }
    }
}
