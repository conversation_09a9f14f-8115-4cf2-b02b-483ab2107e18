using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DoiTuongHocPhiSelectItemModel
    {
        public int IdDoiTuongHocPhi { get; set; }
        public string DoiTuongHocPhi { get; set; }
    }

    public class DoiTuongHocPhiBaseModel
    {
        public int IdDoiTuongHocPhi { get; set; }
        public string DoiTuongHocPhi { get; set; }
    }


    public class DoiTuongHocPhiModel : DoiTuongHocPhiBaseModel
    {

    }

    public class DoiTuongHocPhiFilterModel : BaseQueryFilterModel
    {
        public DoiTuongHocPhiFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdDoiTuongHocPhi";
        }
    }

    public class CreateDoiTuongHocPhiModel
    {
        [Required(ErrorMessage = "DoiTuongHocPhi.IdDoiTuongHocPhi.NotRequire")]
        public int IdDoiTuongHocPhi { get; set; }



        [MaxLength(50, ErrorMessage = "DoiTuongHocPhi.DoiTuongHocPhi.MaxLength(50)")]
        [Required(ErrorMessage = "DoiTuongHocPhi.DoiTuongHocPhi.NotRequire")]
        public string DoiTuongHocPhi { get; set; }

    }

    public class CreateManyDoiTuongHocPhiModel
    {
        public List<CreateDoiTuongHocPhiModel> listDoiTuongHocPhiModels { get; set; }
    }

    public class UpdateDoiTuongHocPhiModel : CreateDoiTuongHocPhiModel
    {
        public void UpdateEntity(SvDoiTuongHocPhi input)
        {
            input.IdDoiTuongHocPhi = IdDoiTuongHocPhi;
            input.DoiTuongHocPhi = DoiTuongHocPhi;


        }
    }
}
