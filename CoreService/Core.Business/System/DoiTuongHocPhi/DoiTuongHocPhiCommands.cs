using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateDoiTuongHocPhiCommand : IRequest<Unit>
    {
        public CreateDoiTuongHocPhiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateDoiTuongHocPhiCommand(CreateDoiTuongHocPhiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateDoiTuongHocPhiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateDoiTuongHocPhiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {DoiTuongHocPhiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateDoiTuongHocPhiModel, SvDoiTuongHocPhi>(model);

                var checkCode = await _dataContext.SvDoiTuongHocPhis.AnyAsync(x => x.IdDoiTuongHocPhi == entity.IdDoiTuongHocPhi || x.DoiTuongHocPhi == entity.DoiTuongHocPhi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DoiTuongHocPhi.Existed", entity.DoiTuongHocPhi.ToString()]}");
                }

                await _dataContext.SvDoiTuongHocPhis.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {DoiTuongHocPhiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới đôi tượng học phí: {entity.DoiTuongHocPhi}",
                    ObjectCode = DoiTuongHocPhiConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuongHocPhi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongHocPhiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyDoiTuongHocPhiCommand : IRequest<Unit>
    {
        public CreateManyDoiTuongHocPhiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyDoiTuongHocPhiCommand(CreateManyDoiTuongHocPhiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyDoiTuongHocPhiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyDoiTuongHocPhiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {DoiTuongHocPhiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listDoiTuongHocPhiAdd = model.listDoiTuongHocPhiModels.Select(x => x.DoiTuongHocPhi).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyDoiTuongHocPhiModel, SvDoiTuongHocPhi>(model);

                // Check data duplicate
                if (listDoiTuongHocPhiAdd.Count() != listDoiTuongHocPhiAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvDoiTuongHocPhis.AnyAsync(x => listDoiTuongHocPhiAdd.Contains(x.DoiTuongHocPhi)))
                {
                    throw new ArgumentException($"{_localizer["DoiTuongHocPhi.Existed"]}");
                }

                var listEntity = model.listDoiTuongHocPhiModels.Select(x => new SvDoiTuongHocPhi()
                {
                    IdDoiTuongHocPhi = x.IdDoiTuongHocPhi,
                    DoiTuongHocPhi = x.DoiTuongHocPhi,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdDoiTuongHocPhi).ToList();

                Log.Information($"Create many {DoiTuongHocPhiConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import đôi tượng học phí từ file excel",
                    ObjectCode = DoiTuongHocPhiConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongHocPhiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateDoiTuongHocPhiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateDoiTuongHocPhiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateDoiTuongHocPhiCommand(int id, UpdateDoiTuongHocPhiModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateDoiTuongHocPhiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateDoiTuongHocPhiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {DoiTuongHocPhiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvDoiTuongHocPhis.FirstOrDefaultAsync(dt => dt.IdDoiTuongHocPhi == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              
                var checkCode = await _dataContext.SvDoiTuongHocPhis.AnyAsync(x => x.DoiTuongHocPhi == model.DoiTuongHocPhi  && x.IdDoiTuongHocPhi != model.IdDoiTuongHocPhi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["DoiTuongHocPhi.Existed", model.DoiTuongHocPhi.ToString()]}");
                }

                Log.Information($"Before Update {DoiTuongHocPhiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvDoiTuongHocPhis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {DoiTuongHocPhiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {DoiTuongHocPhiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật đôi tượng học phí: {entity.DoiTuongHocPhi}",
                    ObjectCode = DoiTuongHocPhiConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuongHocPhi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongHocPhiConstant.BuildCacheKey(entity.IdDoiTuongHocPhi.ToString()));
                _cacheService.Remove(DoiTuongHocPhiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteDoiTuongHocPhiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteDoiTuongHocPhiCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteDoiTuongHocPhiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteDoiTuongHocPhiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {DoiTuongHocPhiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvDoiTuongHocPhis.FirstOrDefaultAsync(x => x.IdDoiTuongHocPhi == id);

                _dataContext.SvDoiTuongHocPhis.Remove(entity);

                Log.Information($"Delete {DoiTuongHocPhiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa đôi tượng học phí: {entity.DoiTuongHocPhi}",
                    ObjectCode = DoiTuongHocPhiConstant.CachePrefix,
                    ObjectId = entity.IdDoiTuongHocPhi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(DoiTuongHocPhiConstant.BuildCacheKey());
                _cacheService.Remove(DoiTuongHocPhiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
