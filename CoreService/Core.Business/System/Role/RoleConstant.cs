using Core.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class RoleConstant
    {
        public const string CachePrefix = CacheConstants.ROLE;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public const string CachePrefixRoleMapPermission = CacheConstants.ROLE_MAP_PERMISSION;
        public const string CachePrefixRoleMapUser = CacheConstants.ROLE_MAP_USER;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }

        public static string BuildCacheKeyRoleMapPermission(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefixRoleMapPermission}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefixRoleMapPermission}-{id}";
            }
        }

        public static string BuildCacheKeyUserMapRole(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefixRoleMapUser}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefixRoleMapUser}-{id}";
            }
        }
    }
}
