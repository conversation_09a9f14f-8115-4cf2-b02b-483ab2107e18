using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    #region CRUD
    public class GetRoleByIdQuery : IRequest<RoleModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin nhóm người dùng theo id
        /// </summary>
        /// <param name="id">Id nhóm người dùng</param>
        public GetRoleByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetRoleByIdQuery, RoleModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<RoleModel> Handle(GetRoleByIdQuery request, CancellationToken cancellationToken)
            {
                string cacheKey = RoleConstant.BuildCacheKey(request.Id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.Roles.FirstOrDefaultAsync(x => x.Id == request.Id);

                    return AutoMapperUtils.AutoMap<Role, RoleModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetFilterRoleQuery : IRequest<PaginationList<RoleBaseModel>>
    {
        public RoleQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách nhóm người dùng theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterRoleQuery(RoleQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterRoleQuery, PaginationList<RoleBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<RoleBaseModel>> Handle(GetFilterRoleQuery request, CancellationToken cancellationToken)
            {

                var data = (from entity in _dataContext.Roles

                            select new RoleBaseModel()
                            {
                                Id = entity.Id,
                                Name = entity.Name,
                                Description = entity.Description
                            });

                if (!string.IsNullOrEmpty(request.Filter.TextSearch))
                {
                    string ts = request.Filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.Name.ToLower().Contains(ts));
                }

                data = data.OrderByField(request.Filter.PropertyName, request.Filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (request.Filter.PageSize.HasValue && request.Filter.PageNumber.HasValue)
                {
                    if (request.Filter.PageSize <= 0)
                    {
                        request.Filter.PageSize = QueryFilter.DefaultPageSize;
                    }

                    //Calculate nunber of rows to skip on pagesize
                    int excludedRows = (request.Filter.PageNumber.Value - 1) * (request.Filter.PageSize.Value);
                    if (excludedRows <= 0)
                    {
                        excludedRows = 0;
                    }

                    // Query
                    data = data.Skip(excludedRows).Take(request.Filter.PageSize.Value);
                }
                int dataCount = data.Count();

                var listResult = await data.ToListAsync();
                return new PaginationList<RoleBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = request.Filter.PageNumber ?? 0,
                    PageSize = request.Filter.PageSize ?? 0,
                    Data = listResult
                };
            }
        }
    }

    public class GetComboboxRoleQuery : IRequest<List<RoleSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách nhóm người dùng cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxRoleQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxRoleQuery, List<RoleSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<RoleSelectItemModel>> Handle(GetComboboxRoleQuery request, CancellationToken cancellationToken)
            {
                string cacheKey = RoleConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.Roles.OrderBy(x => x.Name)
                                select new RoleSelectItemModel()
                                {
                                    Id = item.Id,
                                    Code = item.Name,
                                    Name = item.Name
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(request.TextSearch))
                {
                    request.TextSearch = request.TextSearch.ToLower().Trim();
                    list = list.Where(x => x.Name.ToLower().Contains(request.TextSearch) || x.Note.ToLower().Contains(request.TextSearch)).ToList();
                }

                if (request.Count > 0)
                {
                    list = list.Take(request.Count).ToList();
                }

                return list;
            }
        }
    }
    #endregion

    #region Advance
    //Lấy danh sách Permission thuộc nhóm người dùng
    public class GetPermissionOfRoleQuery : IRequest<List<int>>
    {
        public int RoleId { get; set; }

        /// <summary>
        /// Lấy danh sách Permission thuộc nhóm người dùng
        /// </summary>
        /// <param name="roleId">Id nhóm người dùng</param>
        public GetPermissionOfRoleQuery(int roleId)
        {
            this.RoleId = roleId;
        }

        public class Handler : IRequestHandler<GetPermissionOfRoleQuery, List<int>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<int>> Handle(GetPermissionOfRoleQuery request, CancellationToken cancellationToken)
            {
                string cacheKey = RoleConstant.BuildCacheKeyRoleMapPermission(request.RoleId.ToString());
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data =
                        from roleMapPermission in _dataContext.RoleMapPermissions
                        where roleMapPermission.RoleId == request.RoleId
                        select roleMapPermission.PermissionId;

                    return await data.ToListAsync();
                });
                return list;
            }
        }
    }

    //Lấy danh sách người dùng thuộc nhóm người dùng
    public class GetUserOfRoleQuery : IRequest<List<int>>
    {
        public int RoleId { get; set; }

        /// <summary>
        /// Lấy danh sách người dùng thuộc nhóm người dùng
        /// </summary>
        /// <param name="roleId">Id nhóm người dùng</param>
        public GetUserOfRoleQuery(int roleId)
        {
            this.RoleId = roleId;
        }

        public class Handler : IRequestHandler<GetUserOfRoleQuery, List<int>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<int>> Handle(GetUserOfRoleQuery request, CancellationToken cancellationToken)
            {
                string cacheKey = RoleConstant.BuildCacheKeyUserMapRole(request.RoleId.ToString());
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data =
                        from userMapRole in _dataContext.UserMapRoles
                        where userMapRole.RoleId == request.RoleId
                        select userMapRole.UserId;

                    return await data.ToListAsync();
                });
                return list;
            }
        }
    }
    #endregion
}
