using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Minio.DataModel;

namespace Core.Business
{
    #region CRUD
    public class CreateRoleCommand : IRequest<Unit>
    {
        public CreateRoleModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới nhóm người dùng
        /// </summary>
        /// <param name="model">Thông tin nhóm người dùng cần thêm mới</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public CreateRoleCommand(CreateRoleModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateRoleCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(CreateRoleCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Create {RoleConstant.CachePrefix}: " + JsonSerializer.Serialize(request.Model));

                var entity = AutoMapperUtils.AutoMap<CreateRoleModel, Role>(request.Model);

                await _dataContext.Roles.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {RoleConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới nhóm người dùng mã: {entity.Name}",
                    ObjectCode = RoleConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(RoleConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }

    public class UpdateRoleCommand : IRequest<Unit>
    {
        public UpdateRoleModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật nhóm người dùng
        /// </summary>
        /// <param name="model">Thông tin nhóm người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateRoleCommand(UpdateRoleModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateRoleCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(UpdateRoleCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Update {RoleConstant.CachePrefix}: {JsonSerializer.Serialize(request.Model)}");

                var entity = await _dataContext.Roles.FirstOrDefaultAsync(x => x.Id == request.Model.Id);
                Log.Information($"Before Update {RoleConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                request.Model.UpdateEntity(entity);

                _dataContext.Roles.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {RoleConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(RoleConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(RoleConstant.BuildCacheKey());

                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập nhóm người dùng mã: {entity.Name}",
                    ObjectCode = RoleConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }

    public class DeleteRoleCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa nhóm người dùng
        /// </summary>
        /// <param name="id">id cần xóa</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public DeleteRoleCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteRoleCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(DeleteRoleCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Delete {RoleConstant.CachePrefix}: {JsonSerializer.Serialize(request.Id)}");

                var entity = await _dataContext.Roles.FindAsync(request.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{RoleConstant.CachePrefix} không tồn tại");
                }
                
                // xóa danh sách permission
                var listRoleMapPermission = await _dataContext.RoleMapPermissions.Where(x => x.RoleId == request.Id).ToListAsync();
                _dataContext.RoleMapPermissions.RemoveRange(listRoleMapPermission);

                // xóa danh sách user
                var listUserMapRole = await _dataContext.UserMapRoles.Where(x => x.RoleId == request.Id).ToListAsync();
                _dataContext.UserMapRoles.RemoveRange(listUserMapRole);

                _dataContext.Roles.Remove(entity);
                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa nhóm người dùng mã: {entity.Name}",
                    ObjectCode = RoleConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(RoleConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(RoleConstant.BuildCacheKeyRoleMapPermission(entity.Id.ToString()));
                foreach (var item in listUserMapRole)
                {
                    _cacheService.Remove(UserConstant.BuildCacheKeyUserMapRole(item.UserId.ToString()));
                }
                _cacheService.Remove(RoleConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
    #endregion

    #region Advance
    public class UpdateRoleMapPermissionCommand : IRequest<Unit>
    {
        public PermissionOfRoleCreateModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật quyền chức năng cho nhóm người dùng
        /// </summary>
        /// <param name="model">Thông tin quyền người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateRoleMapPermissionCommand(PermissionOfRoleCreateModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateRoleMapPermissionCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(UpdateRoleMapPermissionCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Update Permission Of Role {RoleConstant.CachePrefixRoleMapPermission}: " + JsonSerializer.Serialize(request.Model));

                var entity = await _dataContext.Roles.FirstOrDefaultAsync(x => x.Id == request.Model.RoleId);

                var listCurrentPermissionOfRole = _dataContext.RoleMapPermissions.Where(x => x.RoleId == request.Model.RoleId);

                _dataContext.RoleMapPermissions.RemoveRange(listCurrentPermissionOfRole);

                foreach (var item in request.Model.PermissionIds)
                {
                    await _dataContext.RoleMapPermissions.AddAsync(new RoleMapPermission()
                    {
                        RoleId = request.Model.RoleId,
                        PermissionId = item
                    });
                }

                await _dataContext.SaveChangesAsync();

                Log.Information($"Update Permission Of Role {RoleConstant.CachePrefixRoleMapPermission} success: {JsonSerializer.Serialize(request.Model)}");
                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật phân quyền chức năng cho nhóm người dùng: {entity.Name}",
                    ObjectCode = RoleConstant.CachePrefixRoleMapPermission,
                    ObjectId = request.Model.RoleId.ToString()
                });

                _cacheService.Remove(RoleConstant.BuildCacheKeyRoleMapPermission(request.Model.RoleId.ToString()));

                return Unit.Value;
            }
        }
    }

    public class UpdateUserOfRoleCommand : IRequest<Unit>
    {
        public UserOfRoleCreateModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật người dùng thuộc nhóm người dùng
        /// </summary>
        /// <param name="model">Thông tin quyền người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateUserOfRoleCommand(UserOfRoleCreateModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateUserOfRoleCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(UpdateUserOfRoleCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Update User Of Role {UserConstant.CachePrefixUserMapRole}: " + JsonSerializer.Serialize(request.Model));

                var entity = await _dataContext.Roles.FirstOrDefaultAsync(x => x.Id == request.Model.RoleId);
                List<int> listUserIdUpdate = new List<int>();
                var listCurrentUserOfRole = await _dataContext.UserMapRoles.Where(x => x.RoleId == request.Model.RoleId).ToListAsync();

                listUserIdUpdate.AddRange(listCurrentUserOfRole.Select(x => x.UserId));

                _dataContext.UserMapRoles.RemoveRange(listCurrentUserOfRole);
                
                listUserIdUpdate.AddRange(request.Model.UserIds);

                foreach (var item in request.Model.UserIds)
                {
                    await _dataContext.UserMapRoles.AddAsync(new UserMapRole()
                    {
                        RoleId = request.Model.RoleId,
                        UserId = item
                    });
                    _cacheService.Remove(UserConstant.BuildCacheKeyUserMapRole(item.ToString()));
                }

                await _dataContext.SaveChangesAsync();

                Log.Information($"Update User Of Role {UserConstant.CachePrefixUserMapRole} success: {JsonSerializer.Serialize(request.Model)}");
                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật phân quyền cho nhóm người dùng: {entity.Name}",
                    ObjectCode = UserConstant.CachePrefixUserMapRole,
                    ObjectId = request.Model.RoleId.ToString()
                });

                listUserIdUpdate = listUserIdUpdate.Distinct().ToList();
                foreach (var item in listUserIdUpdate)
                {
                    _cacheService.Remove(UserConstant.BuildCacheKeyUserMapRole(item.ToString()));
                }

                _cacheService.Remove(RoleConstant.BuildCacheKeyUserMapRole(request.Model.RoleId.ToString()));
                
                return Unit.Value;
            }
        }
    }
    #endregion
}