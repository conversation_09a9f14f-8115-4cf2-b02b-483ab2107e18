using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;

namespace Core.Business
{
    #region CRUD
    public class RoleBaseModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class RoleModel : RoleBaseModel
    {

    }

    public class CreateRoleModel : RoleModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateRoleModel : RoleModel
    {
        public int? ModifiedUserId { get; set; }

        public void UpdateEntity(Role entity)
        {
            entity.Name = this.Name;
            entity.Description = this.Description;
        }
    }

    public class RoleSelectItemModel : SelectItemModel
    {
    }

    public class RoleQueryFilter
    {
        public string TextSearch { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
        public bool? IsActive { get; set; }
        public string PropertyName { get; set; } = "Name";
        //asc - desc
        public string Ascending { get; set; } = "asc";
        public RoleQueryFilter()
        {
            PageNumber = QueryFilter.DefaultPageNumber;
            PageSize = QueryFilter.DefaultPageSize;
        }
    }
    #endregion

    #region Advance
    public class PermissionOfRoleCreateModel
    {
        public int RoleId { get; set; }
        public List<int> PermissionIds { get; set; }
    }
    public class UserOfRoleCreateModel
    {
        public int RoleId { get; set; }
        public List<int> UserIds { get; set; }
    }
    #endregion
}
