using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxXepLoaiHocBongQuery : IRequest<List<XepLoaiHocBongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy tôn giáo cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepLoaiHocBongQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepLoaiHocBongQuery, List<XepLoaiHocBongSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepLoaiHocBongSelectItemModel>> Handle(GetComboboxXepLoaiHocBongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepLoaiHocBongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepLoaiHocBongs.OrderBy(x => x.TenXepLoai)
                                select new XepLoaiHocBongSelectItemModel()
                                {
                                    IdXepLoaiHb = dt.IdXepLoaiHb,
                                    MaXepLoai = dt.MaXepLoai,
                                    TenXepLoai = dt.TenXepLoai,
                                    IdHe = dt.IdHe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenXepLoai.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepLoaiHocBongQuery : IRequest<PaginationList<XepLoaiHocBongBaseModel>>
    {
        public XepLoaiHocBongFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tôn giáo có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepLoaiHocBongQuery(XepLoaiHocBongFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepLoaiHocBongQuery, PaginationList<XepLoaiHocBongBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepLoaiHocBongBaseModel>> Handle(GetFilterXepLoaiHocBongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepLoaiHocBongs
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            select new XepLoaiHocBongBaseModel
                            {
                                IdXepLoaiHb = dt.IdXepLoaiHb,
                                MaXepLoai = dt.MaXepLoai,
                                TenXepLoai = dt.TenXepLoai,
                                TuDiemHt = dt.TuDiemHt,
                                TuDiemRl = dt.TuDiemRl,
                                TuDiemHt4  = dt.TuDiemHt4,
                                IdHe = dt.IdHe,
                                TenHe = he.TenHe,
                                SoTien = dt.SoTien

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenXepLoai.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<XepLoaiHocBongBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepLoaiHocBongByIdQuery : IRequest<XepLoaiHocBongModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Tôn giáo theo id
        /// </summary>
        /// <param name="id">Id tôn giáo</param>
        public GetXepLoaiHocBongByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepLoaiHocBongByIdQuery, XepLoaiHocBongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepLoaiHocBongModel> Handle(GetXepLoaiHocBongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepLoaiHocBongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepLoaiHocBongs.FirstOrDefaultAsync(x => x.IdXepLoaiHb == id);

                    return AutoMapperUtils.AutoMap<SvXepLoaiHocBong, XepLoaiHocBongModel>(entity);
                });
                return item;
            }
        }
    }
}
