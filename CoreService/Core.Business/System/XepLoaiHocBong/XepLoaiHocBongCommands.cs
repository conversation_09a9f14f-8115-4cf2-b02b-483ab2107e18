using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXepLoaiHocBongCommand : IRequest<Unit>
    {
        public CreateXepLoaiHocBongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXepLoaiHocBongCommand(CreateXepLoaiHocBongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXepLoaiHocBongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXepLoaiHocBongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XepLoaiHocBongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXepLoaiHocBongModel, SvXepLoaiHocBong>(model);

                var checkCode = await _dataContext.SvXepLoaiHocBongs.AnyAsync(x => x.MaXepLoai == entity.MaXepLoai);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepLoaiHocBong.Existed", entity.TenXepLoai.ToString()]}");
                }

                await _dataContext.SvXepLoaiHocBongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XepLoaiHocBongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tôn giáo: {entity.TenXepLoai}",
                    ObjectCode = XepLoaiHocBongConstant.CachePrefix,
                    ObjectId = entity.IdXepLoaiHb.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocBongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXepLoaiHocBongCommand : IRequest<Unit>
    {
        public CreateManyXepLoaiHocBongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXepLoaiHocBongCommand(CreateManyXepLoaiHocBongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXepLoaiHocBongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXepLoaiHocBongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XepLoaiHocBongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listMaXepLoaiAdd = model.listXepLoaiHocBongModels.Select(x => x.MaXepLoai).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXepLoaiHocBongModel, SvXepLoaiHocBong>(model);

                // Check data duplicate
                if ( listMaXepLoaiAdd.Count() != listMaXepLoaiAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if ( await _dataContext.SvXepLoaiHocBongs.AnyAsync(x => listMaXepLoaiAdd.Contains(x.MaXepLoai)))
                {
                    throw new ArgumentException($"{_localizer["XepLoaiHocBong.Existed"]}");
                }

                var listEntity = model.listXepLoaiHocBongModels.Select(x => new SvXepLoaiHocBong()
                {
                    IdXepLoaiHb = x.IdXepLoaiHb,
                    MaXepLoai = x.MaXepLoai,
                    TenXepLoai = x.TenXepLoai,
                    TuDiemHt = x.TuDiemHt,
                    TuDiemRl = x.TuDiemRl,
                    TuDiemHt4 = x.TuDiemHt4,
                    IdHe = x.IdHe,
                    SoTien = x.SoTien

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXepLoaiHb).ToList();

                Log.Information($"Create many {XepLoaiHocBongConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Tôn Giáo từ file excel",
                    ObjectCode = XepLoaiHocBongConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocBongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXepLoaiHocBongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepLoaiHocBongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepLoaiHocBongCommand(int id, UpdateXepLoaiHocBongModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepLoaiHocBongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepLoaiHocBongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepLoaiHocBongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepLoaiHocBongs.FirstOrDefaultAsync(dt => dt.IdXepLoaiHb == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvXepLoaiHocBongs.AnyAsync(x => ( x.MaXepLoai == model.MaXepLoai) && x.IdXepLoaiHb != model.IdXepLoaiHb);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepLoaiHocBong.Existed", model.TenXepLoai.ToString()]}");
                }

                Log.Information($"Before Update {XepLoaiHocBongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepLoaiHocBongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepLoaiHocBongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepLoaiHocBongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tôn giáo: {entity.TenXepLoai}",
                    ObjectCode = XepLoaiHocBongConstant.CachePrefix,
                    ObjectId = entity.IdXepLoaiHb.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocBongConstant.BuildCacheKey(entity.IdXepLoaiHb.ToString()));
                _cacheService.Remove(XepLoaiHocBongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepLoaiHocBongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepLoaiHocBongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepLoaiHocBongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepLoaiHocBongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepLoaiHocBongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepLoaiHocBongs.FirstOrDefaultAsync(x => x.IdXepLoaiHb == id);

                _dataContext.SvXepLoaiHocBongs.Remove(entity);

                Log.Information($"Delete {XepLoaiHocBongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Tôn giáo: {entity.TenXepLoai}",
                    ObjectCode = XepLoaiHocBongConstant.CachePrefix,
                    ObjectId = entity.IdXepLoaiHb.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepLoaiHocBongConstant.BuildCacheKey());
                _cacheService.Remove(XepLoaiHocBongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
