using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class PhongSelectItemModel
    {
        public int IdPhong { get; set; }
        public string Ma<PERSON>hong { get; set; }
        public string Phong { get; set; }
    }

    public class PhongBaseModel
    {
        public int IdPhong { get; set; }
        public string MaPhong { get; set; }
        public string Phong { get; set; }
    }


    public class PhongModel : PhongBaseModel
    {

    }

    public class PhongFilterModel : BaseQueryFilterModel
    {
        public PhongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdPhong";
        }
    }

    public class CreatePhongModel
    {
        [Required(ErrorMessage = "Phong.IdPhong.NotRequire")]
        public int IdPhong { get; set; }

        [MaxLength(5, ErrorMessage = "Phong.MaPhong.MaxLength(5)")]
        [Required(ErrorMessage = "Phong.MaPhong.NotRequire")]
        public string MaPhong { get; set; }

        [MaxLength(100, ErrorMessage = "Phong.Phong.MaxLength(100)")]
        [Required(ErrorMessage = "Phong.Phong.NotRequire")]
        public string Phong { get; set; }


    }

    public class CreateManyPhongModel
    {
        public List<CreatePhongModel> listPhongModels { get; set; }
    }

    public class UpdatePhongModel : CreatePhongModel
    {
        public void UpdateEntity(HtPhong input)
        {
            input.IdPhong = IdPhong;
            input.MaPhong = MaPhong;
            input.Phong = Phong;


        }
    }
}
