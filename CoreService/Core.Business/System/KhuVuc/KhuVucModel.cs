using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class KhuVucSelectItemModel
    {
        public int IdKhuVuc { get; set; }
        public string MaKhuVuc { get; set; }
        public string TenKhuVuc { get; set; }
    }

    public class KhuVucBaseModel
    {
        public int IdKhuVuc { get; set; }
        public string MaKhuVuc { get; set; }
        public string TenKhuVuc { get; set; }
        public decimal? DiemCongKv { get; set; }
    }


    public class KhuVucModel : KhuVucBaseModel
    {

    }

    public class KhuVucFilterModel : BaseQueryFilterModel
    {
        public KhuVucFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdKhuVuc";
        }
    }

    public class CreateKhuVucModel
    {
        [Required(ErrorMessage = "KhuVuc.IdKhuVuc.NotRequire")]
        public int IdKhuVuc { get; set; }

        [MaxLength(10, ErrorMessage = "KhuVuc.MaKhuVuc.MaxLength(10)")]
        [Required(ErrorMessage = "KhuVuc.MaKhuVuc.NotRequire")]
        public string MaKhuVuc { get; set; }

        [MaxLength(50, ErrorMessage = "KhuVuc.TenKhuVuc.MaxLength(50)")]
        [Required(ErrorMessage = "KhuVuc.TenKhuVuc.NotRequire")]
        public string TenKhuVuc { get; set; }

        public decimal DiemCongKv { get; set; }

    }

    public class CreateManyKhuVucModel
    {
        public List<CreateKhuVucModel> listKhuVucModels { get; set; }
    }

    public class UpdateKhuVucModel : CreateKhuVucModel
    {
        public void UpdateEntity(SvKhuVuc input)
        {
            input.IdKhuVuc = IdKhuVuc;
            input.MaKhuVuc = MaKhuVuc;
            input.TenKhuVuc = TenKhuVuc;
            input.DiemCongKv = DiemCongKv;

        }
    }
}
