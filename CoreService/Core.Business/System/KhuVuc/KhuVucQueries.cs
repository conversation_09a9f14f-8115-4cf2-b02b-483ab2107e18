using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxKhuVucQuery : IRequest<List<KhuVucSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Khu vực cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxKhuVucQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxKhuVucQuery, List<KhuVucSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<KhuVucSelectItemModel>> Handle(GetComboboxKhuVucQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = KhuVucConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvKhuVucs.OrderBy(x => x.TenKhuVuc)
                                select new KhuVucSelectItemModel()
                                {
                                    IdKhuVuc = dt.IdKhuVuc,
                                    MaKhuVuc = dt.MaKhuVuc,
                                    TenKhuVuc = dt.TenKhuVuc
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenKhuVuc.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterKhuVucQuery : IRequest<PaginationList<KhuVucBaseModel>>
    {
        public KhuVucFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Khu vực có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterKhuVucQuery(KhuVucFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterKhuVucQuery, PaginationList<KhuVucBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<KhuVucBaseModel>> Handle(GetFilterKhuVucQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvKhuVucs
                            select new KhuVucBaseModel
                            {
                                IdKhuVuc = dt.IdKhuVuc,
                                MaKhuVuc = dt.MaKhuVuc,
                                TenKhuVuc = dt.TenKhuVuc,
                                DiemCongKv = dt.DiemCongKv

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenKhuVuc.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<KhuVucBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetKhuVucByIdQuery : IRequest<KhuVucModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Khu vực theo id
        /// </summary>
        /// <param name="id">Id Khu vực</param>
        public GetKhuVucByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetKhuVucByIdQuery, KhuVucModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<KhuVucModel> Handle(GetKhuVucByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = KhuVucConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvKhuVucs.FirstOrDefaultAsync(x => x.IdKhuVuc == id);

                    return AutoMapperUtils.AutoMap<SvKhuVuc, KhuVucModel>(entity);
                });
                return item;
            }
        }
    }
}
