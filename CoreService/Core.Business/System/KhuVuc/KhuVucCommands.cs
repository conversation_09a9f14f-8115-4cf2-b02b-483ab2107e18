using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateKhuVucCommand : IRequest<Unit>
    {
        public CreateKhuVucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateKhuVucCommand(CreateKhuVucModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateKhuVucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateKhuVucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {KhuVucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateKhuVucModel, SvKhuVuc>(model);

                var checkCode = await _dataContext.SvKhuVucs.AnyAsync(x => x.IdKhuVuc == entity.IdKhuVuc || x.TenKhuVuc == entity.TenKhuVuc || x.MaKhuVuc == entity.MaKhuVuc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["KhuVuc.Existed", entity.TenKhuVuc.ToString()]}");
                }

                await _dataContext.SvKhuVucs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {KhuVucConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới khu vực: {entity.TenKhuVuc}",
                    ObjectCode = KhuVucConstant.CachePrefix,
                    ObjectId = entity.IdKhuVuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhuVucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyKhuVucCommand : IRequest<Unit>
    {
        public CreateManyKhuVucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyKhuVucCommand(CreateManyKhuVucModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyKhuVucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyKhuVucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {KhuVucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listKhuVucAdd = model.listKhuVucModels.Select(x => x.TenKhuVuc).ToList();
                var listMaKhuVucAdd = model.listKhuVucModels.Select(x => x.MaKhuVuc).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyKhuVucModel, SvKhuVuc>(model);

                // Check data duplicate
                if (listKhuVucAdd.Count() != listKhuVucAdd.Distinct().Count() || listMaKhuVucAdd.Count() != listMaKhuVucAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvKhuVucs.AnyAsync(x => listKhuVucAdd.Contains(x.TenKhuVuc)) || await _dataContext.SvKhuVucs.AnyAsync(x => listMaKhuVucAdd.Contains(x.MaKhuVuc)))
                {
                    throw new ArgumentException($"{_localizer["KhuVuc.Existed"]}");
                }

                var listEntity = model.listKhuVucModels.Select(x => new SvKhuVuc()
                {
                    IdKhuVuc = x.IdKhuVuc,
                    MaKhuVuc = x.MaKhuVuc,
                    TenKhuVuc = x.TenKhuVuc,
                    DiemCongKv = x.DiemCongKv,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdKhuVuc).ToList();

                Log.Information($"Create many {KhuVucConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import khu vực từ file excel",
                    ObjectCode = KhuVucConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(KhuVucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateKhuVucCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateKhuVucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateKhuVucCommand(int id, UpdateKhuVucModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateKhuVucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateKhuVucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {KhuVucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvKhuVucs.FirstOrDefaultAsync(dt => dt.IdKhuVuc == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               
                var checkCode = await _dataContext.SvKhuVucs.AnyAsync(x => (x.TenKhuVuc == model.TenKhuVuc || x.MaKhuVuc == model.MaKhuVuc) && x.IdKhuVuc != model.IdKhuVuc);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["KhuVuc.Existed", model.TenKhuVuc.ToString()]}");
                }

                Log.Information($"Before Update {KhuVucConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvKhuVucs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {KhuVucConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {KhuVucConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật khu vực: {entity.TenKhuVuc}",
                    ObjectCode = KhuVucConstant.CachePrefix,
                    ObjectId = entity.IdKhuVuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhuVucConstant.BuildCacheKey(entity.IdKhuVuc.ToString()));
                _cacheService.Remove(KhuVucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteKhuVucCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteKhuVucCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteKhuVucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteKhuVucCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {KhuVucConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvKhuVucs.FirstOrDefaultAsync(x => x.IdKhuVuc == id);

                _dataContext.SvKhuVucs.Remove(entity);

                Log.Information($"Delete {KhuVucConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa khu vực: {entity.TenKhuVuc}",
                    ObjectCode = KhuVucConstant.CachePrefix,
                    ObjectId = entity.IdKhuVuc.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhuVucConstant.BuildCacheKey());
                _cacheService.Remove(KhuVucConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
