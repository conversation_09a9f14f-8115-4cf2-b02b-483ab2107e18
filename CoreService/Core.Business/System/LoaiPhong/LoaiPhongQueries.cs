using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxLoaiPhongQuery : IRequest<List<LoaiPhongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy loại phòng cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiPhongQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiPhongQuery, List<LoaiPhongSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiPhongSelectItemModel>> Handle(GetComboboxLoaiPhongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiPhongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbLoaiPhongs.OrderBy(x => x.TenLoaiPhong)
                                select new LoaiPhongSelectItemModel()
                                {
                                    IdLoaiPhong = dt.IdLoaiPhong,
                                    MaLoaiPhong = dt.MaLoaiPhong,
                                    TenLoaiPhong = dt.TenLoaiPhong
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenLoaiPhong.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLoaiPhongQuery : IRequest<PaginationList<LoaiPhongBaseModel>>
    {
        public LoaiPhongFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách loại phòng có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiPhongQuery(LoaiPhongFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiPhongQuery, PaginationList<LoaiPhongBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiPhongBaseModel>> Handle(GetFilterLoaiPhongQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TkbLoaiPhongs
                            select new LoaiPhongBaseModel
                            {
                                IdLoaiPhong = dt.IdLoaiPhong,
                                MaLoaiPhong = dt.MaLoaiPhong,
                                TenLoaiPhong = dt.TenLoaiPhong,
                                ThucHanh = dt.ThucHanh

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenLoaiPhong.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<LoaiPhongBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiPhongByIdQuery : IRequest<LoaiPhongModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin loại phòng theo id
        /// </summary>
        /// <param name="id">Id loại phòng</param>
        public GetLoaiPhongByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiPhongByIdQuery, LoaiPhongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiPhongModel> Handle(GetLoaiPhongByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiPhongConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbLoaiPhongs.FirstOrDefaultAsync(x => x.IdLoaiPhong == id);

                    return AutoMapperUtils.AutoMap<TkbLoaiPhong, LoaiPhongModel>(entity);
                });
                return item;
            }
        }
    }
}
