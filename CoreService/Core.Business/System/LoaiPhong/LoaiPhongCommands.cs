using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateLoaiPhongCommand : IRequest<Unit>
    {
        public CreateLoaiPhongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateLoaiPhongCommand(CreateLoaiPhongModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateLoaiPhongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateLoaiPhongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {LoaiPhongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateLoaiPhongModel, TkbLoaiPhong>(model);

                var checkCode = await _dataContext.TkbLoaiPhongs.AnyAsync(x => x.IdLoaiPhong == entity.IdLoaiPhong || x.TenLoaiPhong == entity.TenLoaiPhong || x.MaLoaiPhong == entity.MaLoaiPhong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiPhong.Existed", entity.TenLoaiPhong.ToString()]}");
                }

                await _dataContext.TkbLoaiPhongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {LoaiPhongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới loại phòng: {entity.TenLoaiPhong}",
                    ObjectCode = LoaiPhongConstant.CachePrefix,
                    ObjectId = entity.IdLoaiPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiPhongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }


    public class UpdateLoaiPhongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateLoaiPhongModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateLoaiPhongCommand(int id, UpdateLoaiPhongModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateLoaiPhongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateLoaiPhongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {LoaiPhongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbLoaiPhongs.FirstOrDefaultAsync(dt => dt.IdLoaiPhong == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                var checkCode = await _dataContext.TkbLoaiPhongs.AnyAsync(x => (x.TenLoaiPhong == model.TenLoaiPhong || x.MaLoaiPhong == model.MaLoaiPhong) && x.IdLoaiPhong != model.IdLoaiPhong);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["LoaiPhong.Existed", model.TenLoaiPhong.ToString()]}");
                }

                Log.Information($"Before Update {LoaiPhongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbLoaiPhongs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {LoaiPhongConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {LoaiPhongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật loại phòng: {entity.TenLoaiPhong}",
                    ObjectCode = LoaiPhongConstant.CachePrefix,
                    ObjectId = entity.IdLoaiPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiPhongConstant.BuildCacheKey(entity.IdLoaiPhong.ToString()));
                _cacheService.Remove(LoaiPhongConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteLoaiPhongCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteLoaiPhongCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteLoaiPhongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteLoaiPhongCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {LoaiPhongConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbLoaiPhongs.FirstOrDefaultAsync(x => x.IdLoaiPhong == id);

                _dataContext.TkbLoaiPhongs.Remove(entity);

                Log.Information($"Delete {LoaiPhongConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa loại phòng: {entity.TenLoaiPhong}",
                    ObjectCode = LoaiPhongConstant.CachePrefix,
                    ObjectId = entity.IdLoaiPhong.ToString()
                });

                //Xóa cache
                _cacheService.Remove(LoaiPhongConstant.BuildCacheKey());
                _cacheService.Remove(LoaiPhongConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
