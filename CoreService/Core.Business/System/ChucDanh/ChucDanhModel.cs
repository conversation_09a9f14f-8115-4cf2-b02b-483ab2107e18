using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
  
        public class ChucDanhSelectItemModel
        {
            public int IdChucDanh { get; set; }
            public string MaChucDanh { get; set; }
            public string ChucDanh { get; set; }

        }

        public class ChucDanhBaseModel : ChucDanhSelectItemModel
        {

        }


        public class ChucDanhModel : ChucDanhSelectItemModel
        {

        }

        public class ChucDanhFilterModel : BaseQueryFilterModel
        {
            public ChucDanhFilterModel()
            {
                Ascending = "desc";
                PropertyName = "IdChucDanh";
            }
        }

        public class CreateChucDanhModel
        {
            [Required(ErrorMessage = "ChucDanh.IdChucDanh.NotRequire")]
            public int IdChucDanh { get; set; }

            [MaxLength(10, ErrorMessage = "ChucDanh.MaChucDanh.MaxLength(10)")]
            [Required(ErrorMessage = "ChucDanh.MaChucDanh.NotRequire")]
            public string MaChucDanh { get; set; }

            [MaxLength(100, ErrorMessage = "ChucDanh.ChucDanh.MaxLength(100)")]
            [Required(ErrorMessage = "ChucDanh.ChucDanh.NotRequire")]
            public string ChucDanh { get; set; }



    }

        public class CreateManyChucDanhModel
        {
            public List<CreateChucDanhModel> listChucDanhModels { get; set; }
        }

        public class UpdateChucDanhModel : CreateChucDanhModel
        {
            public void UpdateEntity(TkbChucDanh input)
            {
                input.IdChucDanh = IdChucDanh;
                input.MaChucDanh = MaChucDanh;
                input.ChucDanh = ChucDanh;


        }
        }
    }

