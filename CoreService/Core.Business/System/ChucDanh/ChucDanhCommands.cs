using Core.Business.System.ChucDanh;
using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateChucDanhCommand : IRequest<Unit>
    {
        public CreateChucDanhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateChucDanhCommand(CreateChucDanhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateChucDanhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateChucDanhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {ChucDanhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateChucDanhModel, TkbChucDanh>(model);

                var checkCode = await _dataContext.TkbChucDanhs.AnyAsync(x => x.IdChucDanh == entity.IdChucDanh || x.ChucDanh == entity.ChucDanh || x.MaChucDanh == entity.MaChucDanh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChucDanh.Existed", entity.ChucDanh.ToString()]}");
                }

                await _dataContext.TkbChucDanhs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChucDanhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới Chức danh: {entity.ChucDanh}",
                    ObjectCode = ChucDanhConstant.CachePrefix,
                    ObjectId = entity.IdChucDanh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChucDanhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyChucDanhCommand : IRequest<Unit>
    {
        public CreateManyChucDanhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyChucDanhCommand(CreateManyChucDanhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyChucDanhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyChucDanhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {ChucDanhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listChucDanhAdd = model.listChucDanhModels.Select(x => x.ChucDanh).ToList();
                var listMaChucDanhAdd = model.listChucDanhModels.Select(x => x.MaChucDanh).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyChucDanhModel, TkbChucDanh>(model);

                // Check data duplicate
                if (listChucDanhAdd.Count() != listChucDanhAdd.Distinct().Count() || listMaChucDanhAdd.Count() != listMaChucDanhAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbChucDanhs.AnyAsync(x => listChucDanhAdd.Contains(x.ChucDanh)) || await _dataContext.TkbChucDanhs.AnyAsync(x => listMaChucDanhAdd.Contains(x.MaChucDanh)))
                {
                    throw new ArgumentException($"{_localizer["ChucDanh.Existed"]}");
                }

                var listEntity = model.listChucDanhModels.Select(x => new TkbChucDanh()
                {
                    IdChucDanh = x.IdChucDanh,
                    MaChucDanh = x.MaChucDanh,
                    ChucDanh = x.ChucDanh

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdChucDanh).ToList();

                Log.Information($"Create many {ChucDanhConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Chức danh từ file excel",
                    ObjectCode = ChucDanhConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(ChucDanhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateChucDanhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateChucDanhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateChucDanhCommand(int id, UpdateChucDanhModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateChucDanhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateChucDanhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {ChucDanhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbChucDanhs.FirstOrDefaultAsync(dt => dt.IdChucDanh == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              
                var checkCode = await _dataContext.TkbChucDanhs.AnyAsync(x => (x.ChucDanh == model.ChucDanh || x.MaChucDanh == model.MaChucDanh) && x.IdChucDanh != model.IdChucDanh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChucDanh.Existed", model.ChucDanh.ToString()]}");
                }

                Log.Information($"Before Update {ChucDanhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbChucDanhs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ChucDanhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {ChucDanhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Chức danh: {entity.ChucDanh}",
                    ObjectCode = ChucDanhConstant.CachePrefix,
                    ObjectId = entity.IdChucDanh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChucDanhConstant.BuildCacheKey(entity.IdChucDanh.ToString()));
                _cacheService.Remove(ChucDanhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteChucDanhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteChucDanhCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteChucDanhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteChucDanhCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {ChucDanhConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbChucDanhs.FirstOrDefaultAsync(x => x.IdChucDanh == id);

                _dataContext.TkbChucDanhs.Remove(entity);

                Log.Information($"Delete {ChucDanhConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Chức danh: {entity.ChucDanh}",
                    ObjectCode = ChucDanhConstant.CachePrefix,
                    ObjectId = entity.IdChucDanh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChucDanhConstant.BuildCacheKey());
                _cacheService.Remove(ChucDanhConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
