using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Core.Data;

namespace Core.Business
{
    public class HeSelectItemModel
    {
        public int IdHe { get; set; }
        public string MaHe { get; set; }
        public string TenHe { get; set; }
        public string TenHeEn { get; set; }
        public int? QuyChe {  get; set; }
    }

    public class HeBaseModel
    {
        public int IdHe { get; set; }
        public string MaHe { get; set; }
        public string TenHe { get; set; }
        public string TenHeEn { get; set; }
        public int? QuyChe { get; set; }
        public string TenBacDaoTao { get; set; }
        public string TenBacDaoTaoEn { get; set; }
        public string HinhThucDaoTao { get; set; }
        public string HinhThucDaoTaoEn { get; set; }

    }

    public class HeModel: HeBaseModel
    {

    }


    public class HeFilterModel : BaseQueryFilterModel
    {
        public HeFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdHe";
        }
    }

    public class CreateHeModel
    {
        [Required(ErrorMessage = "He.IdHe.NotRequire")]
        public int IdHe { get; set; }

        [MaxLength(50, ErrorMessage = "He.MaHe.MaxLength(50)")]
        [Required(ErrorMessage = "He.MaHe.NotRequire")]
        public string MaHe { get; set; }

        [MaxLength(50, ErrorMessage = "He.TenHe.MaxLength(50)")]
        [Required(ErrorMessage = "He.TenHe.NotRequire")]
        public string TenHe{ get; set; }

        [MaxLength(50, ErrorMessage = "He.TenHeEn.MaxLength(50)")]
        public string TenHeEn { get; set; }

        public int QuyChe { get; set; }

        [MaxLength(50, ErrorMessage = "He.TenBacDaoTao.MaxLength(50)")]
        [Required(ErrorMessage = "He.TenBacDaoTao.NotRequire")]
        public string TenBacDaoTao { get; set; }

        [MaxLength(50, ErrorMessage = "He.TenBacDaoTaoEn.MaxLength(50)")]
        [Required(ErrorMessage = "He.TenBacDaoTaoEn.NotRequire")]
        public string TenBacDaoTaoEn { get; set; }

        [MaxLength(50, ErrorMessage = "He.HinhThucDaoTao.MaxLength(50)")]
        [Required(ErrorMessage = "He.HinhThucDaoTao.NotRequire")]
        public string HinhThucDaoTao { get; set; }

        [MaxLength(50, ErrorMessage = "He.HinhThucDaoTaoEn.MaxLength(50)")]
        [Required(ErrorMessage = "He.HinhThucDaoTaoEn.NotRequire")]
        public string HinhThucDaoTaoEn { get; set; }

       





    }

    public class CreateManyHeModel
    {
        public List<CreateHeModel> listHeModels { get; set; }
    }


    public class UpdateHeModel : CreateHeModel
    {
        public void UpdateEntity(SvHe input)
        {
            input.IdHe = IdHe;
            input.MaHe = MaHe;
            input.TenHe = TenHe;
            input.TenHeEn = TenHeEn;
            input.QuyChe = QuyChe;
            input.TenBacDaoTao = TenBacDaoTao;
            input.TenBacDaoTaoEn = TenBacDaoTaoEn;
            input.HinhThucDaoTao = HinhThucDaoTao;
            input.HinhThucDaoTaoEn = HinhThucDaoTaoEn;


        }
    }
}
