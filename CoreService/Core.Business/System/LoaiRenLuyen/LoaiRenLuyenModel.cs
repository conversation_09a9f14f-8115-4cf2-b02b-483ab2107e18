using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LoaiRenLuyenSelectItemModel
    {
        public int IdLoaiRenLuyen { get; set; }
        public int IdCapRenLuyen { get; set; }
        public string KyHieu { get; set; }
        public string TenLoai { get; set; }
        public int Diem {  get; set; }
        public bool DiemTru { get; set; }
    }

    public class LoaiRenLuyenBaseModel
    {
        public int IdLoaiRenLuyen { get; set; }
        public int IdCapRenLuyen { get; set; }
        public string TenCapRenLuyen { get; set; }
        public string KyHieu { get; set; }
        public string TenLoai { get; set; }
        public int Diem { get; set; }
        public bool DiemTru { get; set; }
        public bool HocTap { get; set; }
        public bool TinhDiem { get; set; }
        public bool HienThi { get; set; }
    }


    public class LoaiRenLuyenModel : LoaiRenLuyenBaseModel
    {

    }

    public class LoaiRenLuyenFilterModel : BaseQueryFilterModel
    {
        public LoaiRenLuyenFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdLoaiRenLuyen";
        }
    }

    public class CreateLoaiRenLuyenModel
    {
        [Required(ErrorMessage = "LoaiRenLuyen.IdLoaiRenLuyen.NotRequire")]
        public int IdLoaiRenLuyen { get; set; }

        [Required(ErrorMessage = "LoaiRenLuyen.IdCapRenLuyen.NotRequire")]
        public int IdCapRenLuyen { get; set; }

        [MaxLength(10, ErrorMessage = "LoaiRenLuyen.KyHieu.MaxLength(10)")]
        [Required(ErrorMessage = "LoaiRenLuyen.KyHieu.NotRequire")]
        public string KyHieu { get; set; }

        [MaxLength(200, ErrorMessage = "LoaiRenLuyen.TenLoai.MaxLength(200)")]
        [Required(ErrorMessage = "LoaiRenLuyen.TenLoai.NotRequire")]
        public string TenLoai { get; set; }

        [Required(ErrorMessage = "LoaiRenLuyen.Diem.NotRequire")]
        public int Diem { get; set; }

        public bool DiemTru { get; set; }

        public bool HocTap { get; set; }

        public bool TinhDiem { get; set; }

        public bool HienThi { get; set; }

    }

    public class CreateManyLoaiRenLuyenModel
    {
        public List<CreateLoaiRenLuyenModel> listLoaiRenLuyenModels { get; set; }
    }

    public class UpdateLoaiRenLuyenModel : CreateLoaiRenLuyenModel
    {
        public void UpdateEntity(SvLoaiRenLuyen input)
        {
            input.IdLoaiRenLuyen = IdLoaiRenLuyen;
            input.IdCapRenLuyen = IdCapRenLuyen;
            input.KyHieu = KyHieu;
            input.TenLoai = TenLoai;
            input.Diem = Diem;
            input.DiemTru = DiemTru;
            input.HocTap = HocTap;
            input.TinhDiem = TinhDiem;
            input.HienThi = HienThi;
        }
    }
}
