using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxLoaiRenLuyenQuery : IRequest<List<LoaiRenLuyenSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy loại rèn luyện cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiRenLuyenQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiRenLuyenQuery, List<LoaiRenLuyenSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiRenLuyenSelectItemModel>> Handle(GetComboboxLoaiRenLuyenQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiRenLuyenConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvLoaiRenLuyens.OrderBy(x => x.TenLoai)
                                select new LoaiRenLuyenSelectItemModel()
                                {
                                    IdLoaiRenLuyen = dt.IdLoaiRenLuyen,
                                    IdCapRenLuyen = dt.IdCapRenLuyen,
                                    KyHieu = dt.KyHieu,
                                    TenLoai = dt.TenLoai,
                                    Diem = dt.Diem,
                                    DiemTru = dt.DiemTru
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenLoai.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLoaiRenLuyenQuery : IRequest<PaginationList<LoaiRenLuyenBaseModel>>
    {
        public LoaiRenLuyenFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách loại rèn luyện có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiRenLuyenQuery(LoaiRenLuyenFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiRenLuyenQuery, PaginationList<LoaiRenLuyenBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiRenLuyenBaseModel>> Handle(GetFilterLoaiRenLuyenQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvLoaiRenLuyens
                            join cap in _dataContext.SvCapRenLuyens on dt.IdCapRenLuyen equals cap.IdCapRenLuyen
                            select new LoaiRenLuyenBaseModel
                            {
                                IdLoaiRenLuyen = dt.IdLoaiRenLuyen,
                                IdCapRenLuyen = dt.IdCapRenLuyen,
                                TenCapRenLuyen = cap.TenCap,
                                KyHieu = dt.KyHieu,
                                TenLoai = dt.TenLoai,
                                Diem = dt.Diem,
                                DiemTru = dt.DiemTru,
                                HocTap = dt.HocTap,
                                TinhDiem = dt.TinhDiem,
                                HienThi = dt.HienThi

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenLoai.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<LoaiRenLuyenBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiRenLuyenByIdQuery : IRequest<LoaiRenLuyenModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin loại rèn luyện theo id
        /// </summary>
        /// <param name="id">Id loại rèn luyện</param>
        public GetLoaiRenLuyenByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiRenLuyenByIdQuery, LoaiRenLuyenModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiRenLuyenModel> Handle(GetLoaiRenLuyenByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiRenLuyenConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvLoaiRenLuyens.FirstOrDefaultAsync(x => x.IdLoaiRenLuyen == id);

                    return AutoMapperUtils.AutoMap<SvLoaiRenLuyen, LoaiRenLuyenModel>(entity);
                });
                return item;
            }
        }
    }
}
