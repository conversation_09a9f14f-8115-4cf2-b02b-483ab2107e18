using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxHocViQuery : IRequest<List<HocViSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy học vị cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHocViQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHocViQuery, List<HocViSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HocViSelectItemModel>> Handle(GetComboboxHocViQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = HocViConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.TkbHocVis.OrderBy(x => x.HocVi)
                                select new HocViSelectItemModel()
                                {
                                    IdHocVi = dt.IdHocVi,
                                    MaHocVi = dt.MaHocVi,
                                    HocVi = dt.HocVi
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.HocVi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterHocViQuery : IRequest<PaginationList<HocViBaseModel>>
    {
        public HocViFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách học vị có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterHocViQuery(HocViFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterHocViQuery, PaginationList<HocViBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<HocViBaseModel>> Handle(GetFilterHocViQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TkbHocVis
                            select new HocViBaseModel
                            {
                                IdHocVi = dt.IdHocVi,
                                MaHocVi = dt.MaHocVi,
                                HocVi = dt.HocVi,
                                HeSoBuKhoaHoc = dt.HeSoBuKhoaHoc

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.HocVi.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<HocViBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetHocViByIdQuery : IRequest<HocViModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin học vị theo id
        /// </summary>
        /// <param name="id">Id học vị</param>
        public GetHocViByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetHocViByIdQuery, HocViModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<HocViModel> Handle(GetHocViByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = HocViConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TkbHocVis.FirstOrDefaultAsync(x => x.IdHocVi == id);

                    return AutoMapperUtils.AutoMap<TkbHocVi, HocViModel>(entity);
                });
                return item;
            }
        }
    }
}
