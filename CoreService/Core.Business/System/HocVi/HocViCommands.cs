using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{

    public class CreateHocViCommand : IRequest<Unit>
    {
        public CreateHocViModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateHocViCommand(CreateHocViModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateHocViCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateHocViCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {HocViConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateHocViModel, TkbHocVi>(model);

                var checkCode = await _dataContext.TkbHocVis.AnyAsync(x => x.IdHocVi == entity.IdHocVi || x.HocVi == entity.HocVi || x.MaHocVi == entity.MaHocVi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HocVi.Existed", entity.HocVi.ToString()]}");
                }

                await _dataContext.TkbHocVis.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {HocViConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới học vị: {entity.HocVi}",
                    ObjectCode = HocViConstant.CachePrefix,
                    ObjectId = entity.IdHocVi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocViConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyHocViCommand : IRequest<Unit>
    {
        public CreateManyHocViModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyHocViCommand(CreateManyHocViModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyHocViCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyHocViCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {HocViConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listHocViAdd = model.listHocViModels.Select(x => x.HocVi).ToList();
                var listMaHocViAdd = model.listHocViModels.Select(x => x.MaHocVi).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyHocViModel, TkbHocVi>(model);

                // Check data duplicate
                if (listHocViAdd.Count() != listHocViAdd.Distinct().Count() || listMaHocViAdd.Count() != listMaHocViAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.TkbHocVis.AnyAsync(x => listHocViAdd.Contains(x.HocVi)) || await _dataContext.TkbHocVis.AnyAsync(x => listMaHocViAdd.Contains(x.MaHocVi)))
                {
                    throw new ArgumentException($"{_localizer["HocVi.Existed"]}");
                }

                var listEntity = model.listHocViModels.Select(x => new TkbHocVi()
                {
                    IdHocVi = x.IdHocVi,
                    MaHocVi = x.MaHocVi,
                    HocVi = x.HocVi,
                    HeSoBuKhoaHoc = x.HeSoBuKhoaHoc,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdHocVi).ToList();

                Log.Information($"Create many {HocViConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import học vị từ file excel",
                    ObjectCode = HocViConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(HocViConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateHocViCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateHocViModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateHocViCommand(int id, UpdateHocViModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateHocViCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateHocViCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {HocViConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbHocVis.FirstOrDefaultAsync(dt => dt.IdHocVi == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              
                var checkCode = await _dataContext.TkbHocVis.AnyAsync(x => (x.HocVi == model.HocVi || x.MaHocVi == model.MaHocVi) && x.IdHocVi != model.IdHocVi);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["HocVi.Existed", model.HocVi.ToString()]}");
                }

                Log.Information($"Before Update {HocViConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbHocVis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {HocViConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {HocViConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật học vị: {entity.HocVi}",
                    ObjectCode = HocViConstant.CachePrefix,
                    ObjectId = entity.IdHocVi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocViConstant.BuildCacheKey(entity.IdHocVi.ToString()));
                _cacheService.Remove(HocViConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteHocViCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteHocViCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteHocViCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteHocViCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {HocViConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbHocVis.FirstOrDefaultAsync(x => x.IdHocVi == id);

                _dataContext.TkbHocVis.Remove(entity);

                Log.Information($"Delete {HocViConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa học vị: {entity.HocVi}",
                    ObjectCode = HocViConstant.CachePrefix,
                    ObjectId = entity.IdHocVi.ToString()
                });

                //Xóa cache
                _cacheService.Remove(HocViConstant.BuildCacheKey());
                _cacheService.Remove(HocViConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
