using Core.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxKhoaHocQuery : IRequest<List<KhoaHocSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy khóa học cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxKhoaHocQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxKhoaHocQuery, List<KhoaHocSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<KhoaHocSelectItemModel>> Handle(GetComboboxKhoaHocQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                // TODO: Khi nào thực hiện CRUD Lớp thì thực hiện cache thông tin này
                //string cacheKey = KhoaHocConstant.BuildCacheKey();
                //var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                //{
                //    var data = (from dt in _dataContext.SvLops.OrderByDescending(x => x.KhoaHoc)
                //                select new KhoaHocSelectItemModel()
                //                {
                //                    KhoaHoc = dt.KhoaHoc,
                //                    IdHe = dt.IdHe,
                //                    IdKhoa = dt.IdKhoa,
                //                })
                //                .Distinct();

                //    return await data.ToListAsync();
                //});

                var list = (from dt in _dataContext.SvLops.OrderByDescending(x => x.KhoaHoc)
                            select new KhoaHocSelectItemModel()
                            {
                                KhoaHoc = dt.KhoaHoc,
                                IdHe = dt.IdHe,
                                IdKhoa = dt.IdKhoa,
                            })
                                .Distinct()
                                .ToList();

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.KhoaHoc.ToString().ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetListRawKhoaHocQuery : IRequest<List<KhoaHocSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách khóa học thô
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetListRawKhoaHocQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetListRawKhoaHocQuery, List<KhoaHocSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<List<KhoaHocSelectItemModel>> Handle(GetListRawKhoaHocQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                List<HeSelectItemModel> listHe = await _mediator.Send(new GetComboboxHeQuery());

                List<KhoaSelectItemModel> listKhoa = await _mediator.Send(new GetComboboxKhoaQuery());

                List<LopSelectItemModel> listLop = await _mediator.Send(new GetComboboxLopQuery());

                var list = (from lop in listLop
                            join he in listHe on lop.IdHe equals he.IdHe
                            join khoa in listKhoa on lop.IdKhoa equals khoa.IdKhoa
                            select new KhoaHocSelectItemModel()
                            {
                                KhoaHoc = lop.KhoaHoc,
                                IdHe = lop.IdHe,
                                IdKhoa = lop.IdKhoa
                            })
                                .OrderByDescending(x => x.KhoaHoc)
                                .DistinctBy(x => new { x.KhoaHoc, x.IdHe, x.IdKhoa })
                                .ToList();

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.KhoaHoc.ToString().ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
