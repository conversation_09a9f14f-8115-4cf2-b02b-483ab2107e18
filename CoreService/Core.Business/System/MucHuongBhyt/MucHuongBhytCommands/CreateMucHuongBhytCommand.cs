using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateMucHuongBhytCommand : IRequest<Unit>
    {
        public CreateMucHuongBhytModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateMucHuongBhytCommand(CreateMucHuongBhytModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateMucHuongBhytCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateMucHuongBhytCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {MucHuongBhytConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateMucHuongBhytModel, SvMucHuongBhyt>(model);

                var checkCode = await _dataContext.SvMucHuongBhyts.AnyAsync(x => x.IdMucHuongBhyt == entity.IdMucHuongBhyt || x.KyHieu == entity.KyHieu);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["MucHuongBhyt.Existed", entity.KyHieu.ToString()]}");
                }

                await _dataContext.SvMucHuongBhyts.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {MucHuongBhytConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới mức hưởng bhyt : {entity.KyHieu}",
                    ObjectCode = MucHuongBhytConstant.CachePrefix,
                    ObjectId = entity.IdMucHuongBhyt.ToString()
                });

                //Xóa cache
                _cacheService.Remove(MucHuongBhytConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }
}
