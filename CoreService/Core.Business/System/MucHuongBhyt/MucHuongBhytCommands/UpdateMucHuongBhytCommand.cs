using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateMucHuongBhytCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateMucHuongBhytModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateMucHuongBhytCommand(int id, UpdateMucHuongBhytModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateMucHuongBhytCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateMucHuongBhytCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {MucHuongBhytConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvMucHuongBhyts.FirstOrDefaultAsync(dt => dt.IdMucHuongBhyt == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvMucHuongBhyts.AnyAsync(x => ( x.KyHieu == model.KyHieu) && x.IdMucHuongBhyt != model.IdMucHuongBhyt);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["MucHuongBhyt.Existed", model.KyHieu.ToString()]}");
                }

                Log.Information($"Before Update {MucHuongBhytConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvMucHuongBhyts.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {MucHuongBhytConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {MucHuongBhytConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật mức hưởng bhyt: {entity.KyHieu}",
                    ObjectCode = MucHuongBhytConstant.CachePrefix,
                    ObjectId = entity.IdMucHuongBhyt.ToString()
                });

                //Xóa cache
                _cacheService.Remove(MucHuongBhytConstant.BuildCacheKey(entity.IdMucHuongBhyt.ToString()));
                _cacheService.Remove(MucHuongBhytConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }
}
