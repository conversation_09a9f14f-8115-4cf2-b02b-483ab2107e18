using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateMucHuongBhytModel
    {
        public int IdMucHuongBhyt { get; set; }
        public string KyHieu { get; set; }
        public string DoiTuongMucHuong { get; set; }
        public int MucHuong { get; set; }
    }
    public class UpdateMucHuongBhytModel : CreateMucHuongBhytModel
    {
        public void UpdateEntity(SvMucHuongBhyt input)
        {
            input.IdMucHuongBhyt = IdMucHuongBhyt;
            input.KyHieu = KyHieu;
            input.DoiTuongMucHuong = DoiTuongMucHuong;
            input.MucHuong = MucHuong;
        }
    }
    public class MucHuongBhytSelectItemModel
    {
        public int IdMucHuongBhyt { get; set; }
        public string KyHieu { get; set; }
        public string DoiTuongMucHuong { get; set; }
    }

    public class MucHuongBhytBaseModel
    {
        public int IdMucHuongBhyt { get; set; }
        public string KyHieu { get; set; }
        public string DoiTuongMucHuong { get; set; }
        public int MucHuong { get; set; }
    }


    public class MucHuongBhytModel : MucHuongBhytBaseModel
    {

    }

    public class MucHuongBhytFilterModel : BaseQueryFilterModel
    {
        public MucHuongBhytFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdMucHuongBhyt";
        }
    }

}
