using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateApiKeyCommand : IRequest<Unit>
    {
        public CreateApiKeyModel Model { get; set; }

        /// <summary>
        /// Thêm mới API Key
        /// </summary>
        /// <param name="model">Thông tin API Key cần thêm mới</param>
        public CreateApiKeyCommand(CreateApiKeyModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateApiKeyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IContextAccessor _contextAccessor;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, Func<IContextAccessor> contextAccessorFactory, IStringLocalizer<Resources> localizer, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _contextAccessor = contextAccessorFactory();
                _localizer = localizer;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(CreateApiKeyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create {ApiKeyConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateApiKeyModel, ApiKey>(model);

                entity.Key = Utils.GenerateUniqueApiKey();
                
                var checkKey = await _dataContext.ApiKeys.AnyAsync(x => x.Key == entity.Key, cancellationToken: cancellationToken);
                if (checkKey)
                {
                    throw new ArgumentException($"{_localizer["api-key.key.existed"]}");
                }

                entity.CreatedUserId = _contextAccessor.UserId;
                entity.CreatedDate = DateTime.Now;

                await _dataContext.ApiKeys.AddAsync(entity, cancellationToken);
                await _dataContext.SaveChangesAsync(cancellationToken);

                Log.Information($"Create {ApiKeyConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới API Key: {entity.Key}",
                    ObjectCode = ApiKeyConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey());
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey(entity.Key));

                return Unit.Value;
            }
        }
    }
}
