using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class RevokeApiKeyCommand : IRequest<Unit>
    {
        public int Id { get; set; }

        /// <summary>
        /// Vô hiệu hóa API Key
        /// </summary>
        /// <param name="id">Id API Key cần vô hiệu hóa</param>
        public RevokeApiKeyCommand(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<RevokeApiKeyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IContextAccessor _contextAccessor;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, Func<IContextAccessor> contextAccessorFactory, IStringLocalizer<Resources> localizer, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _contextAccessor = contextAccessorFactory();
                _localizer = localizer;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(RevokeApiKeyCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                Log.Information($"Revoke {ApiKeyConstant.CachePrefix}: {id}");

                var entity = await _dataContext.ApiKeys.FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                Log.Information($"Before Revoke {ApiKeyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                entity.IsActive = false;
                entity.ModifiedUserId = _contextAccessor.UserId;
                entity.ModifiedDate = DateTime.Now;

                _dataContext.ApiKeys.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Revoke {ApiKeyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey());
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey(entity.Key));

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Vô hiệu hóa API Key: {entity.Key}",
                    ObjectCode = ApiKeyConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
