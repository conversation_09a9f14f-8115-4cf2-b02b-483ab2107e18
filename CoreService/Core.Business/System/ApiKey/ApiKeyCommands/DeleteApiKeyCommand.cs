using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteApiKeyCommand : IRequest<Unit>
    {
        public int Id { get; set; }

        /// <summary>
        /// Xóa API Key
        /// </summary>
        /// <param name="id">Id API Key cần xóa</param>
        public DeleteApiKeyCommand(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<DeleteApiKeyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IContextAccessor _contextAccessor;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, Func<IContextAccessor> contextAccessorFactory, IStringLocalizer<Resources> localizer, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _contextAccessor = contextAccessorFactory();
                _localizer = localizer;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(DeleteApiKeyCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                Log.Information($"Delete {ApiKeyConstant.CachePrefix}: {id}");

                var entity = await _dataContext.ApiKeys.FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                Log.Information($"Delete {ApiKeyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                _dataContext.ApiKeys.Remove(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Delete {ApiKeyConstant.CachePrefix} success: {id}");

                //Xóa cache
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey());
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey(entity.Key));

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa API Key: {entity.Key}",
                    ObjectCode = ApiKeyConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
