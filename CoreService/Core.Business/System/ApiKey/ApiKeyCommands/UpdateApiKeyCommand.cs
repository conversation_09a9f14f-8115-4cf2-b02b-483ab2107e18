using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateApiKeyCommand : IRequest<Unit>
    {
        public UpdateApiKeyModel Model { get; set; }

        /// <summary>
        /// Cập nhật API Key
        /// </summary>
        /// <param name="model">Thông tin API Key cần cập nhật</param>
        public UpdateApiKeyCommand(UpdateApiKeyModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<UpdateApiKeyCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IContextAccessor _contextAccessor;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, Func<IContextAccessor> contextAccessorFactory, IStringLocalizer<Resources> localizer, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _contextAccessor = contextAccessorFactory();
                _localizer = localizer;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(UpdateApiKeyCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Update {ApiKeyConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.ApiKeys.FirstOrDefaultAsync(x => x.Id == model.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                Log.Information($"Before Update {ApiKeyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);
                
                entity.ModifiedUserId = _contextAccessor.UserId;
                _dataContext.ApiKeys.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ApiKeyConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey());
                _cacheService.Remove(ApiKeyConstant.BuildCacheKey(entity.Key));

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật API Key: {entity.Key}",
                    ObjectCode = ApiKeyConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
