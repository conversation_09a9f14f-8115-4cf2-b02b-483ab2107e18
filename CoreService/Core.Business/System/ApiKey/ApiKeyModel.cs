using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class ApiKeyBaseModel
    {
        public int Id { get; set; }

        public int UserId { get; set; }

        public string UserName { get; set; }

        public DateTime ValidFrom { get; set; }

        public DateTime ValidTo { get; set; }
        
        public string Key { get; set; }

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }
        public string Description { get; set; }

        public DateTime? CreatedDate { get; set; }
    }

    public class ApiKeyModel : ApiKeyBaseModel
    {
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    public class CreateApiKeyModel : ApiKeyBaseModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateApiKeyModel : ApiKeyBaseModel
    {
        public int? ModifiedUserId { get; set; }

        public void UpdateEntity(ApiKey entity)
        {
            entity.UserId = this.UserId;
            entity.ValidFrom = this.ValidFrom;
            entity.ValidTo = this.ValidTo;
            entity.IsActive = this.IsActive;
            entity.Order = this.Order;
            entity.Description = this.Description;
            entity.ModifiedDate = DateTime.Now;
            entity.ModifiedUserId = this.ModifiedUserId;
        }
    }

    public class ApiKeySelectItemModel : SelectItemModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Key { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public bool IsActive { get; set; }
    }

    public class ApiKeyQueryFilter : BaseQueryFilterModel
    {
        public int? UserId { get; set; }
        public DateTime? ValidFromStart { get; set; }
        public DateTime? ValidFromEnd { get; set; }
        public DateTime? ValidToStart { get; set; }
        public DateTime? ValidToEnd { get; set; }
        public bool? IsExpired { get; set; }
        public bool? IsValid { get; set; }
    }
}
