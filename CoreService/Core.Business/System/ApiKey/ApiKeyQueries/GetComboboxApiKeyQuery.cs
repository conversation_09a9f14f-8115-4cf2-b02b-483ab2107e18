using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxApiKeyQuery : IRequest<List<ApiKeySelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }
        public int? UserId { get; set; }

        /// <summary>
        /// Lấy danh sách API Key cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        /// <param name="userId">ID người dùng</param>
        public GetComboboxApiKeyQuery(int count = 0, string textSearch = "", int? userId = null)
        {
            this.Count = count;
            this.TextSearch = textSearch;
            this.UserId = userId;
        }

        public class Handler : IRequestHandler<GetComboboxApiKeyQuery, List<ApiKeySelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ApiKeySelectItemModel>> Handle(GetComboboxApiKeyQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                var userId = request.UserId;

                string cacheKey = ApiKeyConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.ApiKeys.AsNoTracking().Where(x => x.IsActive)
                            .OrderBy(x => x.Order).ThenBy(x => x.Key)
                        select new ApiKeySelectItemModel()
                        {
                            Id = item.Id,
                            UserId = item.UserId,
                            Code = item.Key,
                            Name = item.Key,
                            Key = item.Key,
                            ValidFrom = item.ValidFrom,
                            ValidTo = item.ValidTo,
                            IsActive = item.IsActive
                        });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    string ts = textSearch.Trim().ToLower();
                    list = list.Where(x => x.Key.ToLower().Contains(ts)).ToList();
                }

                // Nếu có userId thì lọc ra các API Key của người dùng đó và còn hiệu lực
                if (userId.HasValue)
                {
                    list = list.Where(x =>
                        x.UserId == userId.Value && x.ValidTo >= DateTime.Now && x.ValidFrom <= DateTime.Now).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
