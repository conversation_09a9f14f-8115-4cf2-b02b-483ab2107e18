using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetFilterApiKeyQuery : IRequest<PaginationList<ApiKeyBaseModel>>
    {
        public ApiKeyQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách API Key theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterApiKeyQuery(ApiKeyQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterApiKeyQuery, PaginationList<ApiKeyBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ApiKeyBaseModel>> Handle(GetFilterApiKeyQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.ApiKeys.AsNoTracking()
                            join u in _dataContext.HtUsers on dt.UserId equals u.UserId
                            select new ApiKeyBaseModel
                            {
                                Id = dt.Id,
                                UserId = dt.UserId,
                                UserName = u.UserName,
                                ValidFrom = dt.ValidFrom,
                                ValidTo = dt.ValidTo,
                                Key = dt.Key,
                                IsActive = dt.IsActive,
                                Order = dt.Order,
                                Description = dt.Description,
                                CreatedDate = dt.CreatedDate
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.Key.ToLower().Contains(ts) || 
                                          x.Description.ToLower().Contains(ts));
                }

                if (filter.IsActive.HasValue)
                {
                    data = data.Where(x => x.IsActive == filter.IsActive);
                }

                if (filter.UserId.HasValue)
                {
                    data = data.Where(x => x.UserId == filter.UserId);
                }

                if (filter.ValidFromStart.HasValue)
                {
                    data = data.Where(x => x.ValidFrom >= filter.ValidFromStart);
                }

                if (filter.ValidFromEnd.HasValue)
                {
                    data = data.Where(x => x.ValidFrom <= filter.ValidFromEnd);
                }

                if (filter.ValidToStart.HasValue)
                {
                    data = data.Where(x => x.ValidTo >= filter.ValidToStart);
                }

                if (filter.ValidToEnd.HasValue)
                {
                    data = data.Where(x => x.ValidTo <= filter.ValidToEnd);
                }

                if (filter.IsExpired.HasValue)
                {
                    var now = DateTime.Now;
                    if (filter.IsExpired.Value)
                    {
                        data = data.Where(x => x.ValidTo < now);
                    }
                    else
                    {
                        data = data.Where(x => x.ValidTo >= now);
                    }
                }

                if (filter.IsValid.HasValue)
                {
                    var now = DateTime.Now;
                    if (filter.IsValid.Value)
                    {
                        data = data.Where(x => x.ValidFrom <= now && x.ValidTo >= now && x.IsActive);
                    }
                    else
                    {
                        data = data.Where(x => !(x.ValidFrom <= now && x.ValidTo >= now && x.IsActive));
                    }
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<ApiKeyBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }
}
