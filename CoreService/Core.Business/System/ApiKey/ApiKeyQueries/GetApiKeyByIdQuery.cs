using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetApiKeyByIdQuery : IRequest<ApiKeyModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin API Key theo Id
        /// </summary>
        /// <param name="id">Id API Key</param>
        public GetApiKeyByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetApiKeyByIdQuery, ApiKeyModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ApiKeyModel> Handle(GetApiKeyByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = ApiKeyConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.ApiKeys.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);

                    return AutoMapperUtils.AutoMap<ApiKey, ApiKeyModel>(entity);
                });
                return item;
            }
        }
    }
}
