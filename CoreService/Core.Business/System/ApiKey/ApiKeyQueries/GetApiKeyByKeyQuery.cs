using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetApiKeyByKeyQuery : IRequest<ApiKey>
    {
        public string Key { get; set; }

        /// <summary>
        /// Lấy thông tin API Key theo Key (dùng cho authentication)
        /// </summary>
        /// <param name="key">API Key string</param>
        public GetApiKeyByKeyQuery(string key)
        {
            Key = key;
        }

        public class Handler : IRequestHandler<GetApiKeyByKeyQuery, ApiKey>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ApiKey> Handle(GetApiKeyByKeyQuery request, CancellationToken cancellationToken)
            {
                var key = request.Key;
                
                string cacheKey = ApiKeyConstant.BuildCacheKey(key);
                
                // Tạo cache options cho authentication (15 phút)
                var cacheOptions = new DistributedCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(15));

                var apiKeyEntity = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.ApiKeys
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Key == key);

                    return entity;
                }, cacheOptions);

                return apiKeyEntity;
            }
        }
    }
}
