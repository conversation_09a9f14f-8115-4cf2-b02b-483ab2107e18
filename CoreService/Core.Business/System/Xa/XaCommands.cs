using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXaCommand : IRequest<Unit>
    {
        public CreateXaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXaCommand(CreateXaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXaModel, SvXa>(model);

                var checkCode = await _dataContext.SvXas.AnyAsync(x => x.IdXa == entity.IdXa || (x.TenXa == entity.TenXa && x.IdHuyen == entity.IdHuyen));
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Xa.Existed", entity.TenXa.ToString()]}");
                }

                await _dataContext.SvXas.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XaConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới xã: {entity.TenXa}",
                    ObjectCode = XaConstant.CachePrefix,
                    ObjectId = entity.IdXa.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXaCommand : IRequest<Unit>
    {
        public CreateManyXaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXaCommand(CreateManyXaModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listXaAdd = model.listXaModels.Select(x => new { x.TenXa, x.IdHuyen }).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXaModel, SvXa>(model);

                // Check data duplicate
                if (listXaAdd.Count() != listXaAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                for( var i = 0; i< listXaAdd.Count(); i++)
                {
                    if (await _dataContext.SvXas.AnyAsync(x => x.TenXa == listXaAdd[i].TenXa && x.IdHuyen == listXaAdd[i].IdHuyen ))
                    {
                        throw new ArgumentException($"{_localizer["Xa.Existed"]}");
                    }
                }
              

                var listEntity = model.listXaModels.Select(x => new SvXa()
                {
                    IdXa = x.IdXa,
                    IdHuyen = x.IdHuyen,
                    TenXa = x.TenXa,
                    TenXaEn = x.TenXaEn,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXa).ToList();

                Log.Information($"Create many {XaConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import xã từ file excel",
                    ObjectCode = XaConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXaCommand : IRequest<Unit>
    {
        public string Id { get; set; }
        public UpdateXaModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXaCommand(string id, UpdateXaModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXaCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                string id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XaConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXas.FirstOrDefaultAsync(dt => dt.IdXa == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                
                var checkCode = await _dataContext.SvXas.AnyAsync(x => (x.TenXa == model.TenXa || x.IdHuyen == model.IdHuyen) && x.IdXa != model.IdXa);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Xa.Existed", model.TenXa.ToString()]}");
                }

                Log.Information($"Before Update {XaConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXas.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XaConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XaConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật xã: {entity.TenXa}",
                    ObjectCode = XaConstant.CachePrefix,
                    ObjectId = entity.IdXa.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XaConstant.BuildCacheKey(entity.IdXa.ToString()));
                _cacheService.Remove(XaConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXaCommand : IRequest<Unit>
    {
        public string Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXaCommand(string id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXaCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXaCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XaConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXas.FirstOrDefaultAsync(x => x.IdXa == id);

                _dataContext.SvXas.Remove(entity);

                Log.Information($"Delete {XaConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa xã: {entity.TenXa}",
                    ObjectCode = XaConstant.CachePrefix,
                    ObjectId = entity.IdXa.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XaConstant.BuildCacheKey());
                _cacheService.Remove(XaConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
