using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxXaQuery : IRequest<List<XaSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy xã cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXaQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXaQuery, List<XaSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XaSelectItemModel>> Handle(GetComboboxXaQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XaConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXas.OrderBy(x => x.TenXa)
                                select new XaSelectItemModel()
                                {
                                    IdXa = dt.IdXa,
                                    IdHuyen = dt.IdHuyen,
                                    TenXa = dt.TenXa
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenXa.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXaQuery : IRequest<PaginationList<XaBaseModel>>
    {
        public XaFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách xã có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXaQuery(XaFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXaQuery, PaginationList<XaBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XaBaseModel>> Handle(GetFilterXaQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXas
                            join h in _dataContext.SvHuyens on dt.IdHuyen equals h.IdHuyen
                            select new XaBaseModel
                            {
                                IdXa = dt.IdXa,
                                IdHuyen = dt.IdHuyen,
                                TenHuyen = h.TenHuyen,
                                TenXa = dt.TenXa,
                                TenXaEn = dt.TenXaEn

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenXa.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                return new PaginationList<XaBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXaByIdQuery : IRequest<XaModel>
    {
        public string Id { get; set; }

        /// <summary>
        /// Lấy thông tin xã theo id
        /// </summary>
        /// <param name="id">Id xã</param>
        public GetXaByIdQuery(string id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXaByIdQuery, XaModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XaModel> Handle(GetXaByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XaConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXas.FirstOrDefaultAsync(x => x.IdXa == id);

                    return AutoMapperUtils.AutoMap<SvXa, XaModel>(entity);
                });
                return item;
            }
        }
    }
}
