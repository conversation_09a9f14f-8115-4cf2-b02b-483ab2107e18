using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateTangCommand : IRequest<Unit>
    {
        public CreateTangModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateTangCommand(CreateTangModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateTangCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateTangCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {TangConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateTangModel, TkbTang>(model);

                var checkCode = await _dataContext.TkbTangs.AnyAsync(x => x.IdTang == entity.IdTang || x.TenTang == entity.TenTang || x.MaTang == entity.MaTang);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Tang.Existed", entity.TenTang.ToString()]}");
                }

                await _dataContext.TkbTangs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {TangConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tầng: {entity.TenTang}",
                    ObjectCode = TangConstant.CachePrefix,
                    ObjectId = entity.IdTang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TangConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }


    public class UpdateTangCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateTangModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateTangCommand(int id, UpdateTangModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateTangCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateTangCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {TangConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.TkbTangs.FirstOrDefaultAsync(dt => dt.IdTang == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.TkbTangs.AnyAsync(x => (x.TenTang == model.TenTang || x.MaTang == model.MaTang) && x.IdTang != model.IdTang);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Tang.Existed", model.TenTang.ToString()]}");
                }

                Log.Information($"Before Update {TangConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.TkbTangs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {TangConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {TangConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tầng: {entity.TenTang}",
                    ObjectCode = TangConstant.CachePrefix,
                    ObjectId = entity.IdTang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TangConstant.BuildCacheKey(entity.IdTang.ToString()));
                _cacheService.Remove(TangConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteTangCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteTangCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteTangCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteTangCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {TangConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.TkbTangs.FirstOrDefaultAsync(x => x.IdTang == id);

                _dataContext.TkbTangs.Remove(entity);

                Log.Information($"Delete {TangConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa tầng: {entity.TenTang}",
                    ObjectCode = TangConstant.CachePrefix,
                    ObjectId = entity.IdTang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TangConstant.BuildCacheKey());
                _cacheService.Remove(TangConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
