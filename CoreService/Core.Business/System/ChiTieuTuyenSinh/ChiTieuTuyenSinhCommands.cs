using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateChiTieuTuyenSinhCommand : IRequest<Unit>
    {
        public CreateChiTieuTuyenSinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateChiTieuTuyenSinhCommand(CreateChiTieuTuyenSinhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateChiTieuTuyenSinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateChiTieuTuyenSinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {ChiTieuTuyenSinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateChiTieuTuyenSinhModel, SvChiTieuTuyenSinh>(model);

                var checkCode = await _dataContext.SvChiTieuTuyenSinhs.AnyAsync(x => x.NamTuyenSinh == model.NamTuyenSinh && x.IdHe == model.IdHe && x.IdNganh == model.IdNganh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChiTieuTuyenSinh.Existed", entity.ChiTieuTuyenSinh.ToString()]}");
                }

                await _dataContext.SvChiTieuTuyenSinhs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChiTieuTuyenSinhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới chỉ tiêu tuyển sinh: {entity.ChiTieuTuyenSinh}",
                    ObjectCode = ChiTieuTuyenSinhConstant.CachePrefix,
                    ObjectId = entity.IdChiTieuTuyenSinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChiTieuTuyenSinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }


    public class UpdateChiTieuTuyenSinhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateChiTieuTuyenSinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateChiTieuTuyenSinhCommand(int id, UpdateChiTieuTuyenSinhModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateChiTieuTuyenSinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateChiTieuTuyenSinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {ChiTieuTuyenSinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvChiTieuTuyenSinhs.FirstOrDefaultAsync(dt => dt.IdChiTieuTuyenSinh == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvChiTieuTuyenSinhs.AnyAsync(x => x.NamTuyenSinh == model.NamTuyenSinh && x.IdHe == model.IdHe && x.IdNganh == model.IdNganh && x.IdChiTieuTuyenSinh != model.IdChiTieuTuyenSinh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["ChiTieuTuyenSinh.Existed", model.ChiTieuTuyenSinh.ToString()]}");
                }

                Log.Information($"Before Update {ChiTieuTuyenSinhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvChiTieuTuyenSinhs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ChiTieuTuyenSinhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {ChiTieuTuyenSinhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật chỉ tiêu tuyển sinh: {entity.ChiTieuTuyenSinh}",
                    ObjectCode = ChiTieuTuyenSinhConstant.CachePrefix,
                    ObjectId = entity.IdChiTieuTuyenSinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChiTieuTuyenSinhConstant.BuildCacheKey(entity.IdChiTieuTuyenSinh.ToString()));
                _cacheService.Remove(ChiTieuTuyenSinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteChiTieuTuyenSinhCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteChiTieuTuyenSinhCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteChiTieuTuyenSinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteChiTieuTuyenSinhCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {ChiTieuTuyenSinhConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvChiTieuTuyenSinhs.FirstOrDefaultAsync(x => x.IdChiTieuTuyenSinh == id);

                _dataContext.SvChiTieuTuyenSinhs.Remove(entity);

                Log.Information($"Delete {ChiTieuTuyenSinhConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa chỉ tiêu tuyển sinh: {entity.ChiTieuTuyenSinh}",
                    ObjectCode = ChiTieuTuyenSinhConstant.CachePrefix,
                    ObjectId = entity.IdChiTieuTuyenSinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChiTieuTuyenSinhConstant.BuildCacheKey());
                _cacheService.Remove(ChiTieuTuyenSinhConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
