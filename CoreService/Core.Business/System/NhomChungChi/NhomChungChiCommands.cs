using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateNhomChungChiCommand : IRequest<Unit>
    {
        public CreateNhomChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateNhomChungChiCommand(CreateNhomChungChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateNhomChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateNhomChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {NhomChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateNhomChungChiModel, SvNhomChungChi>(model);

                var checkCode = await _dataContext.SvNhomChungChis.AnyAsync(x => x.KyHieuNhom == entity.KyHieuNhom);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["KyHieuNhom.Existed", entity.KyHieuNhom.ToString()]}");
                }
            
                await _dataContext.SvNhomChungChis.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {NhomChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới nhóm chứng chỉ: {entity.NhomChungChi}",
                    ObjectCode = NhomChungChiConstant.CachePrefix,
                    ObjectId = entity.KyHieuNhom.ToString()
                });

                //Xóa cache
                _cacheService.Remove(NhomChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyNhomChungChiCommand : IRequest<Unit>
    {
        public CreateManyNhomChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyNhomChungChiCommand(CreateManyNhomChungChiModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyNhomChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyNhomChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {NhomChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listKyHieuNhomAdd = model.listNhomChungChiModels.Select(x => x.KyHieuNhom).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyNhomChungChiModel, SvNhomChungChi>(model);

                // Check data duplicate
                if (listKyHieuNhomAdd.Count() != listKyHieuNhomAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvNhomChungChis.AnyAsync(x => listKyHieuNhomAdd.Contains(x.KyHieuNhom)))
                {
                    throw new ArgumentException($"{_localizer["KyHieuNhom.Existed"]}");
                }



                var listEntity = model.listNhomChungChiModels.Select(x => new SvNhomChungChi()
                {
                    KyHieuNhom = x.KyHieuNhom,
                    NhomChungChi = x.NhomChungChi
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdNhomChungChi).ToList();

                Log.Information($"Create many {NhomChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import nhóm chứng chỉ từ file excel",
                    ObjectCode = NhomChungChiConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(NhomChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateNhomChungChiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateNhomChungChiModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateNhomChungChiCommand(int id, UpdateNhomChungChiModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateNhomChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateNhomChungChiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {NhomChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvNhomChungChis.FirstOrDefaultAsync(dt => dt.IdNhomChungChi == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
              

                var checkCode = await _dataContext.SvNhomChungChis.AnyAsync(x => x.IdNhomChungChi != entity.IdNhomChungChi && x.KyHieuNhom ==model.KyHieuNhom);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["KyHieuNhom.Existed",model.KyHieuNhom.ToString()]}");
                }
              


                Log.Information($"Before Update {NhomChungChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvNhomChungChis.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {NhomChungChiConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {NhomChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật nhóm chứng chỉ: {entity.NhomChungChi}",
                    ObjectCode = NhomChungChiConstant.CachePrefix,
                    ObjectId = entity.IdNhomChungChi .ToString()
                });

                //Xóa cache
                _cacheService.Remove(NhomChungChiConstant.BuildCacheKey(entity.IdNhomChungChi .ToString()));
                _cacheService.Remove(NhomChungChiConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteNhomChungChiCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteNhomChungChiCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteNhomChungChiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteNhomChungChiCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {NhomChungChiConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvNhomChungChis.FirstOrDefaultAsync(x => x.IdNhomChungChi  == id);

                _dataContext.SvNhomChungChis.Remove(entity);

                Log.Information($"Delete {NhomChungChiConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa nhóm chứng chỉ: {entity.NhomChungChi}",
                    ObjectCode = NhomChungChiConstant.CachePrefix,
                    ObjectId = entity.IdNhomChungChi .ToString()
                });

                //Xóa cache
                _cacheService.Remove(NhomChungChiConstant.BuildCacheKey());
                _cacheService.Remove(NhomChungChiConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
