using Core.Data;
using Core.Shared;
using MediatR;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Core.Business
{


    public class GetFilterDiemCongThucQuery : IRequest<PaginationList<DiemCongThucBaseModel>>
    {
        public DiemCongThucFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách điểm công thức có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterDiemCongThucQuery(DiemCongThucFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterDiemCongThucQuery, PaginationList<DiemCongThucBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<DiemCongThucBaseModel>> Handle(GetFilterDiemCongThucQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvDiemCongThucs
                            select new DiemCongThucBaseModel
                            {
                                IdCongThuc = dt.IdCongThuc,
                                TenCongThuc = dt.TenCongThuc,
                                CongThucTinhDiemTBCBP = dt.CongThucTinhDiemTBCBP,
                                CongThucTinhDiemTBCHP = dt.CongThucTinhDiemTBCHP

                            });

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<DiemCongThucBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetDiemCongThucByIdQuery : IRequest<DiemCongThucModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin điểm công thức theo id
        /// </summary>
        /// <param name="id">Id điểm công thức</param>
        public GetDiemCongThucByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetDiemCongThucByIdQuery, DiemCongThucModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<DiemCongThucModel> Handle(GetDiemCongThucByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = DiemCongThucConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = _dataContext.SvDiemCongThucs.FirstOrDefault(x => x.IdCongThuc == id);

                    return AutoMapperUtils.AutoMap<SvDiemCongThuc, DiemCongThucModel>(entity);
                });
                return item;
            }
        }
    }
}
