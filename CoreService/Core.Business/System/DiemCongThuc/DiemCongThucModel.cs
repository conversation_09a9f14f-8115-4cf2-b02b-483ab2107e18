using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DiemCongThucSelectItemModel
    {
        public int IdCongThuc { get; set; }
        public string TenCongThuc { get; set; }
        public string DiemCongThuc { get; set; }
    }

    public class DiemCongThucBaseModel
    {
        public int IdCongThuc { get; set; }
        public string TenCongThuc { get; set; }
        public string DiemCongThuc { get; set; }
        public string CongThucTinhDiemTBCHP { get; set; }
        public string CongThucTinhDiemTBCBP { get; set; }
    }


    public class DiemCongThucModel : DiemCongThucBaseModel
    {

    }

    public class DiemCongThucFilterModel : BaseQueryFilterModel
    {
        public DiemCongThucFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdCongThuc";
        }
    }

    public class CreateDiemCongThucModel
    {
        [Required(ErrorMessage = "DiemCongThuc.IdCongThuc.NotRequire")]
        public int IdCongThuc { get; set; }

        [MaxLength(200, ErrorMessage = "DiemCongThuc.TenCongThuc.MaxLength(200)")]
        [Required(ErrorMessage = "DiemCongThuc.TenCongThuc.NotRequire")]
        public string TenCongThuc { get; set; }

        [Required(ErrorMessage = "DiemCongThuc.CongThucTinhDiemTBCBp.NotRequire")]
        public string CongThucTinhDiemTBCBP { get; set; }

        [Required(ErrorMessage = "DiemCongThuc.CongThucTinhDiemTBCHP.NotRequire")]
        public string CongThucTinhDiemTBCHP { get; set; }

    }

    public class CreateManyDiemCongThucModel
    {
        public List<CreateDiemCongThucModel> listDiemCongThucModels { get; set; }
    }

    public class UpdateDiemCongThucModel : CreateDiemCongThucModel
    {
        public void UpdateEntity(SvDiemCongThuc input)
        {
            input.IdCongThuc = IdCongThuc;
            input.TenCongThuc = TenCongThuc;
            input.CongThucTinhDiemTBCBP = CongThucTinhDiemTBCBP;
            input.CongThucTinhDiemTBCHP = CongThucTinhDiemTBCHP;

        }
    }
}
