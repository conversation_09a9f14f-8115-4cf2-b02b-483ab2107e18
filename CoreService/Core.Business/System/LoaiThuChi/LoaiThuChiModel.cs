using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LoaiThuChiSelectItemModel
    {
        public int IdThuChi { get; set; }
        public string MaThuChi { get; set; }
        public string TenThuChi { get; set; }
        public bool? HocPhi { get; set; }
        public long? SoTien { get; set; }
    }

    public class LoaiThuChiBaseModel
    {
        public int IdThuChi { get; set; }
        public string MaThuChi { get; set; }
        public string TenThuChi { get; set; }
        public bool? ThuChi { get; set; }
        public long? SoTien { get; set; }
        public bool? HocLai { get; set; }
        public bool? ThiLai { get; set; }
        public bool? HocPhi { get; set; }
        public bool? KinhPhiDt { get; set; }
        public bool? KhoanThuKtx { get; set; }
        public bool? KhoanThuTienPhong { get; set; }
        public bool? KhoanTienCuoc { get; set; }
        public bool? BaoHiem { get; set; }
    }


    public class LoaiThuChiModel : LoaiThuChiBaseModel
    {

    }

    public class LoaiThuChiFilterModel : BaseQueryFilterModel
    {
        public LoaiThuChiFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdThuChi";
        }
    }

    public class CreateLoaiThuChiModel
    {
        [Required(ErrorMessage = "LoaiThuChi.IdThuChi.NotRequire")]
        public int IdThuChi { get; set; }

        [MaxLength(100, ErrorMessage = "LoaiThuChi.TenThuChi.MaxLength(100)")]
        [Required(ErrorMessage = "LoaiThuChi.TenThuChi.NotRequire")]
        public string TenThuChi { get; set; }

        [Required(ErrorMessage = "LoaiThuChi.ThuChi.NotRequire")]
        public bool? ThuChi { get; set; }

        [Required(ErrorMessage = "LoaiThuChi.SoTien.NotRequire")]
        public long? SoTien { get; set; }

        [Required(ErrorMessage = "LoaiThuChi.HocLai.NotRequire")]
        public bool? HocLai { get; set; }

        [Required(ErrorMessage = "LoaiThuChi.ThiLai.NotRequire")]
        public bool? ThiLai { get; set; }


        [Required(ErrorMessage = "LoaiThuChi.HocPhi.NotRequire")]
        public bool? HocPhi { get; set; }


        [Required(ErrorMessage = "LoaiThuChi.KinhPhi.NotRequire")]
        public bool? KinhPhiDt { get; set; }

        [Required(ErrorMessage = "LoaiThuChi.KhoanThuKtx.NotRequire")]
        public bool? KhoanThuKtx { get; set; }

        [Required(ErrorMessage = "LoaiThuChi.KhoanThuTienPhong.NotRequire")]
        public bool? KhoanThuTienPhong { get; set; }

        [Required(ErrorMessage = "LoaiThuChi.KhoanTienCuoc.NotRequire")]
        public bool? KhoanTienCuoc { get; set; }

        [MaxLength(20, ErrorMessage = "LoaiThuChi.MaThuChi.MaxLength(20)")]
        [Required(ErrorMessage = "LoaiThuChi.MaThuChi.NotRequire")]
        public string MaThuChi { get; set; }

        [Required(ErrorMessage = "LoaiThuChi.BaoHiem.NotRequire")]
        public bool? BaoHiem { get; set; }

    }

    public class CreateManyLoaiThuChiModel
    {
        public List<CreateLoaiThuChiModel> listLoaiThuChiModels { get; set; }
    }

    public class UpdateLoaiThuChiModel : CreateLoaiThuChiModel
    {
        public void UpdateEntity(SvLoaiThuChi input)
        {
            input.IdThuChi = IdThuChi;
            input.TenThuChi = TenThuChi;
            input.ThuChi = ThuChi;
            input.SoTien = SoTien;
            input.HocLai = HocLai;
            input.ThiLai = ThiLai;
            input.KinhPhiDt = KinhPhiDt;
            input.KhoanThuKtx = KhoanThuKtx;
            input.KhoanThuTienPhong = KhoanThuTienPhong;
            input.KhoanTienCuoc = KhoanTienCuoc;
            input.MaThuChi = MaThuChi;
            input.BaoHiem = BaoHiem;
            input.HocPhi = HocPhi;

        }
    }
}
