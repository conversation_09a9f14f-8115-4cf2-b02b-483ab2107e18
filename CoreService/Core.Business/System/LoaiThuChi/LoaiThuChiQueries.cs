using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxLoaiThuChiQuery : IRequest<List<LoaiThuChiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy loại thu chi cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLoaiThuChiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLoaiThuChiQuery, List<LoaiThuChiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LoaiThuChiSelectItemModel>> Handle(GetComboboxLoaiThuChiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LoaiThuChiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvLoaiThuChis.OrderBy(x => x.TenThuChi)
                                select new LoaiThuChiSelectItemModel()
                                {
                                    IdThuChi = dt.IdThuChi,
                                    TenThuChi = dt.TenThuChi,
                                    MaThuChi = dt.MaThuChi,
                                    SoTien = dt.SoTien,
                                    HocPhi = dt.HocPhi

                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenThuChi.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterLoaiThuChiQuery : IRequest<PaginationList<LoaiThuChiBaseModel>>
    {
        public LoaiThuChiFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách loại thu chi có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterLoaiThuChiQuery(LoaiThuChiFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterLoaiThuChiQuery, PaginationList<LoaiThuChiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<LoaiThuChiBaseModel>> Handle(GetFilterLoaiThuChiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvLoaiThuChis
                            select new LoaiThuChiBaseModel
                            {
                                IdThuChi = dt.IdThuChi,
                                TenThuChi = dt.TenThuChi,
                                ThuChi = dt.ThuChi,
                                SoTien = dt.SoTien,
                                HocLai = dt.HocLai,
                                ThiLai = dt.ThiLai,
                                KinhPhiDt = dt.KinhPhiDt,
                                KhoanThuKtx = dt.KhoanThuKtx,
                                KhoanThuTienPhong = dt.KhoanThuTienPhong,
                                KhoanTienCuoc = dt.KhoanTienCuoc,
                                MaThuChi = dt.MaThuChi,
                                BaoHiem = dt.BaoHiem,
                                HocPhi = dt.HocPhi
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenThuChi.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<LoaiThuChiBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetLoaiThuChiByIdQuery : IRequest<LoaiThuChiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin loại thu chi theo id
        /// </summary>
        /// <param name="id">Id loại thu chi</param>
        public GetLoaiThuChiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetLoaiThuChiByIdQuery, LoaiThuChiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<LoaiThuChiModel> Handle(GetLoaiThuChiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = LoaiThuChiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvLoaiThuChis.FirstOrDefaultAsync(x => x.IdThuChi == id);

                    return AutoMapperUtils.AutoMap<SvLoaiThuChi, LoaiThuChiModel>(entity);
                });
                return item;
            }
        }
    }
}
