using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;
using System;
using static System.Runtime.InteropServices.JavaScript.JSType;


namespace Core.Business
{
    public class GetComboboxXepLoaiTotNghiepThang4Query : IRequest<List<XepLoaiTotNghiepThang4SelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy Xếp loại học tập thang 4 cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxXepLoaiTotNghiepThang4Query(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxXepLoaiTotNghiepThang4Query, List<XepLoaiTotNghiepThang4SelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<XepLoaiTotNghiepThang4SelectItemModel>> Handle(GetComboboxXepLoaiTotNghiepThang4Query request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = XepLoaiTotNghiepThang4Constant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvXepHangTotNghieps.OrderBy(x => x.XepHang)
                                select new XepLoaiTotNghiepThang4SelectItemModel()
                                {
                                    IdXepHang = dt.IdXepHang,
                                    XepHang = dt.XepHang,
                                    MaXepHang = dt.MaXepHang,
                                    IdHe = dt.IdHe
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.XepHang.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterXepLoaiTotNghiepThang4Query : IRequest<PaginationList<XepLoaiTotNghiepThang4BaseModel>>
    {
        public XepLoaiTotNghiepThang4FilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang 4 có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterXepLoaiTotNghiepThang4Query(XepLoaiTotNghiepThang4FilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterXepLoaiTotNghiepThang4Query, PaginationList<XepLoaiTotNghiepThang4BaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<XepLoaiTotNghiepThang4BaseModel>> Handle(GetFilterXepLoaiTotNghiepThang4Query request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvXepHangTotNghieps
                            join he in _dataContext.SvHes on dt.IdHe equals he.IdHe
                            select new XepLoaiTotNghiepThang4BaseModel
                            {
                                IdXepHang = dt.IdXepHang,
                                XepHang = dt.XepHang,
                                TuDiem = dt.TuDiem,
                                DenDiem = dt.DenDiem,
                                TuDiemThang10 = dt.TuDiemThang10,
                                DenDiemThang10 = dt.DenDiemThang10,
                                MaXepHang = dt.MaXepHang,
                                XepHangEn = dt.XepHangEn,
                                IdHe = dt.IdHe,
                                TenHe = he.TenHe
                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.XepHang.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<XepLoaiTotNghiepThang4BaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetXepLoaiTotNghiepThang4ByIdQuery : IRequest<XepLoaiTotNghiepThang4Model>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin Xếp loại học tập thang 4 theo id
        /// </summary>
        /// <param name="id">Id Xếp loại học tập thang 4</param>
        public GetXepLoaiTotNghiepThang4ByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetXepLoaiTotNghiepThang4ByIdQuery, XepLoaiTotNghiepThang4Model>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<XepLoaiTotNghiepThang4Model> Handle(GetXepLoaiTotNghiepThang4ByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = XepLoaiTotNghiepThang4Constant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvXepHangTotNghieps.FirstOrDefaultAsync(x => x.IdXepHang == id);

                    return AutoMapperUtils.AutoMap<SvXepHangTotNghiep, XepLoaiTotNghiepThang4Model>(entity);
                });
                return item;
            }
        }
    }
}
