using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxHinhThucHocQuery : IRequest<List<HinhThucHocSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y hình thức học cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxHinhThucHocQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxHinhThucHocQuery, List<HinhThucHocSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<HinhThucHocSelectItemModel>> Handle(GetComboboxHinhThucHocQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = HinhThucHocConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvHinhThucHocs.OrderBy(x => x.TenHinhThucHoc)
                                select new HinhThucHocSelectItemModel()
                                {
                                    IdHinhThucHoc = dt.IdHinhThucHoc,
                                    MaHinhThucHoc = dt.MaHinhThucHoc,
                                    TenHinhThucHoc = dt.TenHinhThucHoc
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenHinhThucHoc.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterHinhThucHocQuery : IRequest<PaginationList<HinhThucHocBaseModel>>
    {
        public HinhThucHocFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách hình thức học có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterHinhThucHocQuery(HinhThucHocFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterHinhThucHocQuery, PaginationList<HinhThucHocBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<HinhThucHocBaseModel>> Handle(GetFilterHinhThucHocQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvHinhThucHocs
                            select new HinhThucHocBaseModel
                            {
                                IdHinhThucHoc = dt.IdHinhThucHoc,
                                MaHinhThucHoc = dt.MaHinhThucHoc,
                                TenHinhThucHoc = dt.TenHinhThucHoc,
                                GhiChu = dt.GhiChu

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenHinhThucHoc.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<HinhThucHocBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetHinhThucHocByIdQuery : IRequest<HinhThucHocModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin hình thức học theo id
        /// </summary>
        /// <param name="id">Id hình thức học</param>
        public GetHinhThucHocByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetHinhThucHocByIdQuery, HinhThucHocModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<HinhThucHocModel> Handle(GetHinhThucHocByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = HinhThucHocConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvHinhThucHocs.FirstOrDefaultAsync(x => x.IdHinhThucHoc == id);

                    return AutoMapperUtils.AutoMap<SvHinhThucHoc, HinhThucHocModel>(entity);
                });
                return item;
            }
        }
    }
}
