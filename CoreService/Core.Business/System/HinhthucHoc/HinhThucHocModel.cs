using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class HinhThucHocSelectItemModel
    {
        public int IdHinhThucHoc { get; set; }
        public string MaHinhThucHoc { get; set; }
        public string TenHinhThucHoc { get; set; }
    }

    public class HinhThucHocBaseModel
    {
        public int IdHinhThucHoc { get; set; }
        public string MaHinhThucHoc { get; set; }
        public string TenHinhThucHoc { get; set; }
        public string GhiChu { get; set; }
    }


    public class HinhThucHocModel : HinhThucHocBaseModel
    {

    }

    public class HinhThucHocFilterModel : BaseQueryFilterModel
    {
        public HinhThucHocFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdHinhThucHoc";
        }
    }

    public class CreateHinhThucHocModel
    {
        [Required(ErrorMessage = "HinhThucHoc.IdHinhThucHoc.NotRequire")]
        public int IdHinhThucHoc { get; set; }

        [MaxLength(50, ErrorMessage = "HinhThucHoc.MaHinhThucHoc.MaxLength(50)")]
        [Required(ErrorMessage = "HinhThucHoc.MaHinhThucHoc.NotRequire")]
        public string MaHinhThucHoc { get; set; }

        [MaxLength(50, ErrorMessage = "HinhThucHoc.TenHinhThucHoc.MaxLength(50)")]
        [Required(ErrorMessage = "HinhThucHoc.TenHinhThucHoc.NotRequire")]
        public string TenHinhThucHoc { get; set; }

        [MaxLength(200, ErrorMessage = "HinhThucHoc.GhiChu.MaxLength(200)")]
        public string GhiChu { get; set; }

    }

    public class CreateManyHinhThucHocModel
    {
        public List<CreateHinhThucHocModel> listHinhThucHocModels { get; set; }
    }

    public class UpdateHinhThucHocModel : CreateHinhThucHocModel
    {
        public void UpdateEntity(SvHinhThucHoc input)
        {
            input.IdHinhThucHoc = IdHinhThucHoc;
            input.MaHinhThucHoc = MaHinhThucHoc;
            input.TenHinhThucHoc = TenHinhThucHoc;
            input.GhiChu = GhiChu;

        }
    }
}
