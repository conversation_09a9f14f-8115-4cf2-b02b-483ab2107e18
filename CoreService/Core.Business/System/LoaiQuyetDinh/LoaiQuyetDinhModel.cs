using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class LoaiQuyetDinhSelectItemModel
    {
        public int IdLoaiQd { get; set; }
        public string MaQd { get; set; }
        public string TenLoaiQd { get; set; }
    }

    public class LoaiQuyetDinhBaseModel
    {
        public int IdLoaiQd { get; set; }
        public string MaQd { get; set; }
        public string TenLoaiQd { get; set; }
        public bool ChuyenLop { get; set; }
        public bool ThoiHoc { get; set; }
        public bool NgungHoc { get; set; }
        public bool HocTiep { get; set; }
        public bool ChuyenTruongDi { get; set; }
        public bool ChuyentruongDen { get; set; }
        public bool ThoiHocQuyChe { get; set; }
        public bool XoaTenkhoiLop { get; set; }

    }


    public class LoaiQuyetDinhModel : LoaiQuyetDinhBaseModel
    {

    }

    public class LoaiQuyetDinhFilterModel : BaseQueryFilterModel
    {
        public LoaiQuyetDinhFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdLoaiQd";
        }
    }

    public class CreateLoaiQuyetDinhModel
    {

        [Required(ErrorMessage = "LoaiQuyetDinh.IdLoaiQd.NotRequire")]
        public int IdLoaiQd { get; set; }


        [MaxLength(10, ErrorMessage = "LoaiQuyetDinh.MaQd.MaxLength(10)")]
        [Required(ErrorMessage = "LoaiQuyetDinh.MaQd.NotRequire")]
        public string MaQd { get; set; }

        [MaxLength(100, ErrorMessage = "LoaiQuyetDinh.TenLoaiQd.MaxLength(100)")]
        [Required(ErrorMessage = "LoaiQuyetDinh.TenLoaiQd.NotRequire")]
        public string TenLoaiQd { get; set; }

        [Required(ErrorMessage = "LoaiQuyetDinh.ChuyenLop.NotRequire")]
        public bool ChuyenLop { get; set; }

        [Required(ErrorMessage = "LoaiQuyetDinh.ThoiHoc.NotRequire")]
        public bool ThoiHoc { get; set; }

        [Required(ErrorMessage = "LoaiQuyetDinh.NgungHoc.NotRequire")]
        public bool NgungHoc { get; set; }

        [Required(ErrorMessage = "LoaiQuyetDinh.HocTiep.NotRequire")]
        public bool HocTiep { get; set; }

        [Required(ErrorMessage = "LoaiQuyetDinh.ChuyenTruongDi.NotRequire")]
        public bool ChuyenTruongDi { get; set; }

        [Required(ErrorMessage = "LoaiQuyetDinh.ChuyentruongDen.NotRequire")]
        public bool ChuyentruongDen { get; set; }

        [Required(ErrorMessage = "LoaiQuyetDinh.ThoiHocQuyChe.NotRequire")]
        public bool ThoiHocQuyChe { get; set; }

        [Required(ErrorMessage = "LoaiQuyetDinh.XoaTenkhoiLop.NotRequire")]
        public bool XoaTenkhoiLop { get; set; }

    }

    public class CreateManyLoaiQuyetDinhModel
    {
        public List<CreateLoaiQuyetDinhModel> listLoaiQuyetDinhModels { get; set; }
    }

    public class UpdateLoaiQuyetDinhModel : CreateLoaiQuyetDinhModel
    {
        public void UpdateEntity(SvLoaiQuyetDinh input)
        {
            input.IdLoaiQd = IdLoaiQd;
            input.MaQd = MaQd;
            input.TenLoaiQd = TenLoaiQd;
            input.ChuyenLop = ChuyenLop;
            input.ThoiHoc = ThoiHoc;
            input.NgungHoc = NgungHoc;
            input.HocTiep = HocTiep;
            input.ChuyenTruongDi = ChuyenTruongDi;
            input.ChuyentruongDen = ChuyentruongDen;
            input.ThoiHocQuyChe = ThoiHocQuyChe;
            input.XoaTenkhoiLop = XoaTenkhoiLop;

        }
    }
}
