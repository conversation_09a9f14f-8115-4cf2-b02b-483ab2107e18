using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DoiTuongSelectItemModel
    {
        public int IdDoiTuong { get; set; }
        public string MaDoiTuong { get; set; }
        public string TenDoiTuong { get; set; }
    }

    public class DoiTuongBaseModel
    {
        public int IdDoiTuong { get; set; }
        public string MaDoiTuong { get; set; }
        public string TenDoiTuong { get; set; }
        public int PhanTramMienGiam { get; set; }
    }


    public class DoiTuongModel : DoiTuongBaseModel
    {

    }

    public class DoiTuongFilterModel : BaseQueryFilterModel
    {
        public DoiTuongFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdDoiTuong";
        }
    }

    public class CreateDoiTuongModel
    {
        [Required(ErrorMessage = "DoiTuong.IdDoiTuong.NotRequire")]
        public int IdDoiTuong { get; set; }

        [MaxLength(5, ErrorMessage = "DoiTuong.MaDoiTuong.MaxLength(5)")]
        [Required(ErrorMessage = "DoiTuong.MaDoiTuong.NotRequire")]
        public string MaDoiTuong { get; set; }

        [MaxLength(50, ErrorMessage = "DoiTuong.DoiTuong.MaxLength(50)")]
        [Required(ErrorMessage = "DoiTuong.DoiTuong.NotRequire")]
        public string TenDoiTuong { get; set; }

        [Required(ErrorMessage = "DoiTuong.PhanTramMienGiam.NotRequire")]
        public int PhanTramMienGiam { get; set; }

    }

    public class CreateManyDoiTuongModel
    {
        public List<CreateDoiTuongModel> listDoiTuongModels { get; set; }
    }

    public class UpdateDoiTuongModel : CreateDoiTuongModel
    {
        public void UpdateEntity(SvDoiTuong input)
        {
            input.IdDoiTuong = IdDoiTuong;
            input.MaDoiTuong = MaDoiTuong;
            input.TenDoiTuong = TenDoiTuong;
            input.PhanTramMienGiam = PhanTramMienGiam;

        }
    }
}
