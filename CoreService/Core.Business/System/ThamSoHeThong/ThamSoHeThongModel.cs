using Core.Data;
using Core.Shared;
using System;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class ThamSoHeThongBaseModel
    {
        [Required(ErrorMessage = "tham-so-he-thong.id-tham-so.required")]
        [MaxLength(200, ErrorMessage = "tham-so-he-thong.id-tham-so.max-length")]
        public string IdThamSo { get; set; }

        [MaxLength(50, ErrorMessage = "tham-so-he-thong.ten-tham-so.max-length")]
        public string TenThamSo { get; set; }

        public int IdPh { get; set; }

        [MaxLength(200, ErrorMessage = "tham-so-he-thong.gia-tri.max-length")]
        public string GiaTri { get; set; }

        [MaxLength(200, ErrorMessage = "tham-so-he-thong.ghi-chu.max-length")]
        public string <PERSON>hi<PERSON>hu { get; set; }

        public bool Active { get; set; } = true;

        [MaxLength(200, ErrorMessage = "tham-so-he-thong.nhom-tham-so.max-length")]
        public string NhomThamSo { get; set; }

        [MaxLength(50, ErrorMessage = "tham-so-he-thong.user-name.max-length")]
        public string UserName { get; set; }

        public DateTime? DateModify { get; set; }
    }

    public class ThamSoHeThongModel : ThamSoHeThongBaseModel
    {
        public string PhanHe { get; set; }
    }

    public class CreateThamSoHeThongModel : ThamSoHeThongModel
    {
    }

    public class UpdateThamSoHeThongModel : ThamSoHeThongModel
    {
        public void UpdateEntity(HtThamSoHeThong entity)
        {
            entity.GiaTri = this.GiaTri;
            entity.GhiChu = this.GhiChu;
            entity.Active = this.Active;
            entity.NhomThamSo = this.NhomThamSo;
            entity.DateModify = DateTime.Now;
        }
    }

    public class ThamSoHeThongSelectItemModel : SelectItemModel
    {
        public new string Id { get; set; }
        public new string Code { get; set; }
        public new string Name { get; set; }
        public new string Note { get; set; } = "";
    }

    public class ThamSoHeThongQueryFilter : BaseQueryFilterModel
    {
        public int? IdPh { get; set; }
        public string NhomThamSo { get; set; }
        public bool? Active { get; set; }
        public bool FilterThamSoWorkFlow { get; set; } = false;
    }
}
