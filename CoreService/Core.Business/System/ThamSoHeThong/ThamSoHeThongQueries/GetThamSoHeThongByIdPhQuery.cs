using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetThamSoHeThongByIdPhQuery : IRequest<List<ThamSoHeThongModel>>
    {
        public int IdPh { get; set; }

        /// <summary>
        /// Lấy danh sách tham số theo id phân hệ
        /// </summary>
        /// <param name="idPh">Id phân hệ</param>
        public GetThamSoHeThongByIdPhQuery(int idPh)
        {
            IdPh = idPh;
        }

        public class Handler : IRequestHandler<GetThamSoHeThongByIdPhQuery, List<ThamSoHeThongModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ThamSoHeThongModel>> Handle(GetThamSoHeThongByIdPhQuery request, CancellationToken cancellationToken)
            {
                var idPh = request.IdPh;
                
                var entity = await _dataContext.HtThamSoHeThongs.Where(x => x.IdPh == idPh).ToListAsync();
                var thamSos = AutoMapperUtils.AutoMap<HtThamSoHeThong, ThamSoHeThongModel>(entity);
                
                return thamSos;
            }
        }
    }
}
