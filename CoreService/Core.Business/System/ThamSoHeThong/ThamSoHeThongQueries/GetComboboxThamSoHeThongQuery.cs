using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxThamSoHeThongQuery : IRequest<List<ThamSoHeThongSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }
        public int? IdPh { get; set; }
        public string NhomThamSo { get; set; }

        /// <summary>
        /// Lấy danh sách tham số hệ thống cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        /// <param name="idPh">ID phân hệ</param>
        /// <param name="nhomThamSo">Nhóm tham số</param>
        public GetComboboxThamSoHeThongQuery(int count = 0, string textSearch = "", int? idPh = null, string nhomThamSo = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
            this.IdPh = idPh;
            this.NhomThamSo = nhomThamSo;
        }

        public class Handler : IRequestHandler<GetComboboxThamSoHeThongQuery, List<ThamSoHeThongSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ThamSoHeThongSelectItemModel>> Handle(GetComboboxThamSoHeThongQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                var idPh = request.IdPh;
                var nhomThamSo = request.NhomThamSo;

                string cacheKey = ThamSoHeThongConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var query = _dataContext.HtThamSoHeThongs.AsNoTracking().Where(x => x.Active);

                    if (idPh.HasValue)
                    {
                        query = query.Where(x => x.IdPh == idPh.Value);
                    }

                    if (!string.IsNullOrEmpty(nhomThamSo))
                    {
                        query = query.Where(x => x.NhomThamSo == nhomThamSo);
                    }

                    var data = (from item in query.OrderBy(x => x.IdThamSo)
                                select new ThamSoHeThongSelectItemModel()
                                {
                                    Id = item.IdThamSo,
                                    Code = item.IdThamSo,
                                    Name = item.TenThamSo ?? item.IdThamSo
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.Name.ToLower().Contains(textSearch) || x.Code.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
