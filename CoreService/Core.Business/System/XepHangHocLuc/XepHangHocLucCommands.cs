using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateXepHangHocLucCommand : IRequest<Unit>
    {
        public CreateXepHangHocLucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateXepHangHocLucCommand(CreateXepHangHocLucModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateXepHangHocLucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateXepHangHocLucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {XepHangHocLucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateXepHangHocLucModel, SvXepHangHocLuc>(model);

                var checkCode = await _dataContext.SvXepHangHocLucs.AnyAsync(x => x.XepHang == entity.XepHang && x.IdHe == entity.IdHe);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepHang.Existed", entity.XepHang.ToString()]}");
                }

                await _dataContext.SvXepHangHocLucs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {XepHangHocLucConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới Xếp hạng học lực: {entity.XepHang}",
                    ObjectCode = XepHangHocLucConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepHangHocLucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyXepHangHocLucCommand : IRequest<Unit>
    {
        public CreateManyXepHangHocLucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyXepHangHocLucCommand(CreateManyXepHangHocLucModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyXepHangHocLucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyXepHangHocLucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {XepHangHocLucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listXepHangAdd = model.listXepHangHocLucModels.Select(x => x.XepHang).ToList();
                var listIdHeAdd = model.listXepHangHocLucModels.Select(x => x.IdHe).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyXepHangHocLucModel, SvXepHangHocLuc>(model);

                // Check data duplicate
                if (listXepHangAdd.Count() != listXepHangAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvXepHangHocLucs.AnyAsync(x => listXepHangAdd.Contains(x.XepHang))
                       && await _dataContext.SvXepHangHocLucs.AnyAsync(x => listIdHeAdd.Contains(x.IdHe)))
                {
                    throw new ArgumentException($"{_localizer["XepHang.Existed"]}");
                }


                var listEntity = model.listXepHangHocLucModels.Select(x => new SvXepHangHocLuc()
                {
                    TuDiem = x.TuDiem,
                    DenDiem = x.DenDiem,
                    XepHang = x.XepHang,
                    XepHangEn = x.XepHangEn,
                    IdHe = x.IdHe,
                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdXepHang).ToList();

                Log.Information($"Create many {XepHangHocLucConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import Xếp hạng học lực từ file excel",
                    ObjectCode = XepHangHocLucConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(XepHangHocLucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateXepHangHocLucCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public UpdateXepHangHocLucModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateXepHangHocLucCommand(int id, UpdateXepHangHocLucModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateXepHangHocLucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateXepHangHocLucCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                int id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {XepHangHocLucConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvXepHangHocLucs.FirstOrDefaultAsync(dt => dt.IdXepHang == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
               

                var checkCode = await _dataContext.SvXepHangHocLucs.AnyAsync(x => x.IdXepHang != entity.IdXepHang && x.XepHang == model.XepHang && x.IdHe == model.IdHe );
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["XepHang.Existed", model.XepHang.ToString()]}");
                }


                Log.Information($"Before Update {XepHangHocLucConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvXepHangHocLucs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {XepHangHocLucConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {XepHangHocLucConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật Xếp hạng học lực: {entity.XepHang}",
                    ObjectCode = XepHangHocLucConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepHangHocLucConstant.BuildCacheKey(entity.IdXepHang.ToString()));
                _cacheService.Remove(XepHangHocLucConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteXepHangHocLucCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteXepHangHocLucCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteXepHangHocLucCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteXepHangHocLucCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {XepHangHocLucConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvXepHangHocLucs.FirstOrDefaultAsync(x => x.IdXepHang == id);

                _dataContext.SvXepHangHocLucs.Remove(entity);

                Log.Information($"Delete {XepHangHocLucConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa Xếp hạng học lực: {entity.XepHang}",
                    ObjectCode = XepHangHocLucConstant.CachePrefix,
                    ObjectId = entity.IdXepHang.ToString()
                });

                //Xóa cache
                _cacheService.Remove(XepHangHocLucConstant.BuildCacheKey());
                _cacheService.Remove(XepHangHocLucConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
