using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class XepHangHocLucSelectItemModel
    {
        public int IdXepHang { get; set; }
        public string XepHang { get; set; }
        public int IdHe { get; set; }
    }

    public class XepHangHocLucBaseModel
    {
        public int IdXepHang { get; set; }

        public float TuDiem { get; set; }

        public float DenDiem { get; set; }

        public string XepHang { get; set; }

        public string XepHangEn { get; set; }

        public int IdHe { get; set; }

        public string TenHe { get; set; }
    }


    public class XepHangHocLucModel : XepHangHocLucBaseModel
    {
      
    }

    public class XepHangHocLucFilterModel : BaseQueryFilterModel
    {
        public XepHangHocLucFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdXepHang";
        }
    }

    public class CreateXepHangHocLucModel
    {
        [Required(ErrorMessage = "XepHangHocLuc.TuDiem.NotRequire")]
        public float TuDiem { get; set; }

        [Required(ErrorMessage = "XepHangHocLuc.DenDiem.NotRequire")]
        public float DenDiem { get; set; }

        [MaxLength(20, ErrorMessage = "XepHangHocLuc.XepHang.MaxLength(20)")]
        [Required(ErrorMessage = "XepHangHocLuc.XepHang.NotRequire")]
        public string XepHang { get; set; }

        [MaxLength(20, ErrorMessage = "XepHangHocLuc.XepHangEn.MaxLength(20)")]
        public string XepHangEn { get; set; }

        [Required(ErrorMessage = "XepHangHocLuc.IdHe.NotRequire")]
        public int IdHe { get; set; }
    }

    public class CreateManyXepHangHocLucModel
    {
        public List<CreateXepHangHocLucModel> listXepHangHocLucModels { get; set; }
    }

    public class UpdateXepHangHocLucModel : CreateXepHangHocLucModel
    {
        public void UpdateEntity(SvXepHangHocLuc input)
        {
            input.TuDiem = TuDiem;
            input.DenDiem = DenDiem;
            input.XepHang = XepHang;
            input.XepHangEn = XepHangEn;
            input.IdHe = IdHe;
        }
    }
}
