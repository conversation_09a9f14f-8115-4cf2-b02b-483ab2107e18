using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Core.Business
{
    public class ValidateUserAuthQuery : IRequest<UserBaseForAuthModel>
    {
        public string UserName { get; set; }
        public string Password { get; set; }
        public SystemLogModel SystemLog { get; set; }

        public ValidateUserAuthQuery(string userName, string password, SystemLogModel systemLog)
        {
            UserName = userName;
            Password = password;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<ValidateUserAuthQuery, UserBaseForAuthModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<UserBaseForAuthModel> Handle(ValidateUserAuthQuery request, CancellationToken cancellationToken)
            {
                var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserName == request.UserName && x.PassWord == XCrypto.MD5(request.Password));

                if (entity != null)
                {
                    request.SystemLog.ActionCode = nameof(LogConstants.ACTION_LOGIN_SUCCESS);
                    request.SystemLog.ActionName = LogConstants.ACTION_LOGIN_SUCCESS;

                    Log.Information($"Login success with username: {request.UserName}");
                    request.SystemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"{entity.UserName} - Đăng nhập thành công sử dụng mật khẩu",
                        ObjectCode = UserConstant.CachePrefix,
                        ObjectId = entity.UserId.ToString()
                    });
                    return AutoMapperUtils.AutoMap<HtUser, UserBaseForAuthModel>(entity);
                }

                request.SystemLog.ActionCode = nameof(LogConstants.ACTION_LOGIN_FAIL);
                request.SystemLog.ActionName = LogConstants.ACTION_LOGIN_FAIL;

                Log.Information($"Login fail with username: {request.UserName}");
                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"{request.UserName} - Đăng nhập thất bại sử dụng tài khoản và mật khẩu",
                    ObjectCode = UserConstant.CachePrefix,
                    ObjectId = string.Empty
                });

                throw new ArgumentException("Thông tin đăng nhập không hợp lệ");
            }
        }
    }

    public class GetUserBaseForAuthQuery : IRequest<UserBaseForAuthModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin người dùng theo id
        /// </summary>
        /// <param name="id">Id dân tộc</param>
        public GetUserBaseForAuthQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetUserBaseForAuthQuery, UserBaseForAuthModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<UserBaseForAuthModel> Handle(GetUserBaseForAuthQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserId == id);

                return AutoMapperUtils.AutoMap<HtUser, UserBaseForAuthModel>(entity);
            }
        }
    }

    public class GetUserBaseForAuthQueryFromUserName : IRequest<UserBaseForAuthModel>
    {
        public string UserName { get; set; }

        /// <summary>
        /// Lấy thông tin người dùng theo userName
        /// </summary>
        /// <param name="userName">UserName người dùng</param>
        public GetUserBaseForAuthQueryFromUserName(string userName)
        {
            UserName = userName;
        }

        public class Handler : IRequestHandler<GetUserBaseForAuthQueryFromUserName, UserBaseForAuthModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<UserBaseForAuthModel> Handle(GetUserBaseForAuthQueryFromUserName request, CancellationToken cancellationToken)
            {
                var userName = request.UserName;

                var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserName == userName);

                return AutoMapperUtils.AutoMap<HtUser, UserBaseForAuthModel>(entity);
            }
        }
    }

    #region CRUD
    public class GetUserByIdQuery : IRequest<UserModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin người dùng theo id
        /// </summary>
        /// <param name="id">Id người dùng</param>
        public GetUserByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetUserByIdQuery, UserModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<UserModel> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = UserConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserId == id);
                    if (entity == null)
                    {
                        throw new ArgumentException("data-not-found");
                    }
                    var rs = AutoMapperUtils.AutoMap<HtUser, UserModel>(entity);
                    rs.IsActive = rs.Active == 1;
                    rs.Password = "";
                    return rs;
                });
                return item;
            }
        }
    }

    public class GetFilterUserQuery : IRequest<PaginationList<UserBaseModel>>
    {
        public UserQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách người dùng theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterUserQuery(UserQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterUserQuery, PaginationList<UserBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<UserBaseModel>> Handle(GetFilterUserQuery request, CancellationToken cancellationToken)
            {

                var data = (from entity in _dataContext.HtUsers
                            select entity);

                if (!string.IsNullOrEmpty(request.Filter.TextSearch))
                {
                    string ts = request.Filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.UserName.ToLower().Contains(ts) || x.FullName.ToLower().Contains(ts));
                }

                if (request.Filter.IsActive.HasValue)
                {
                    if (request.Filter.IsActive.Value)
                    {
                        data = data.Where(x => x.Active == 1);
                    }
                    else
                    {
                        data = data.Where(x => x.Active == 0);
                    }
                }

                data = data.OrderByField(request.Filter.PropertyName, request.Filter.Ascending);

                // Apply pagination with improved performance
                int totalCount;
                List<HtUser> listDt;

                if (request.Filter.PageSize.HasValue && request.Filter.PageNumber.HasValue)
                {
                    if (request.Filter.PageSize <= 0)
                    {
                        request.Filter.PageSize = QueryFilter.DefaultPageSize;
                    }

                    // Get total count asynchronously
                    totalCount = await data.CountAsync();

                    // Calculate number of rows to skip on pagesize
                    int excludedRows = (request.Filter.PageNumber.Value - 1) * request.Filter.PageSize.Value;
                    if (excludedRows <= 0)
                    {
                        excludedRows = 0;
                    }

                    // Apply pagination and get data asynchronously
                    listDt = await data
                        .Skip(excludedRows)
                        .Take(request.Filter.PageSize.Value)
                        .ToListAsync();
                }
                else
                {
                    // No pagination - get all data
                    totalCount = await data.CountAsync();
                    listDt = await data.ToListAsync();
                }

                var listResult = AutoMapperUtils.AutoMap<HtUser, UserBaseModel>(listDt);

                foreach (var item in listResult)
                {
                    item.IsActive = item.Active == 1;
                    item.Password = "";
                }

                return new PaginationList<UserBaseModel>()
                {
                    DataCount = listResult.Count,
                    TotalCount = totalCount,
                    PageNumber = request.Filter.PageNumber ?? 0,
                    PageSize = request.Filter.PageSize ?? 0,
                    Data = listResult
                };

            }
        }
    }

    public class GetComboboxUserQuery : IRequest<List<UserSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách người dùng cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxUserQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxUserQuery, List<UserSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<UserSelectItemModel>> Handle(GetComboboxUserQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = UserConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.HtUsers.OrderBy(x => x.UserName)
                                select new UserSelectItemModel()
                                {
                                    UserId = item.UserId,
                                    IsActive = item.Active == 1,
                                    UserName = item.UserName,
                                    FullName = item.FullName,
                                    MaCB = item.MaCanBoUser
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.UserName.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
    #endregion

    public class GetRoleOfUserQuery : IRequest<List<int>>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy danh sách quyền của người dùng
        /// </summary>
        /// <param name="userId">Id người dùng</param>
        public GetRoleOfUserQuery(int userId)
        {
            Id = userId;
        }

        public class Handler : IRequestHandler<GetRoleOfUserQuery, List<int>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;


            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<List<int>> Handle(GetRoleOfUserQuery request, CancellationToken cancellationToken)
            {
                string cacheKey = UserConstant.BuildCacheKeyUserMapRole(request.Id.ToString());
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data =
                        from userMapRole in _dataContext.UserMapRoles
                        where userMapRole.UserId == request.Id
                        select userMapRole.RoleId;

                    return await data.ToListAsync();
                });
                return list;
            }
        }
    }

    public class GetStringRoleOfUserQuery : IRequest<List<string>>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy danh sách quyền của người dùng
        /// </summary>
        /// <param name="userId">Id người dùng</param>
        public GetStringRoleOfUserQuery(int userId)
        {
            Id = userId;
        }

        public class Handler : IRequestHandler<GetStringRoleOfUserQuery, List<string>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;


            public Handler(SystemReadDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<List<string>> Handle(GetStringRoleOfUserQuery request, CancellationToken cancellationToken)
            {
                string cacheKey = UserConstant.BuildCacheKeyUserMapRole(request.Id.ToString());
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data =
                        from userMapRole in _dataContext.UserMapRoles
                        where userMapRole.UserId == request.Id
                        select userMapRole.RoleId.ToString();

                    return await data.ToListAsync();
                });
                return list;
            }
        }
    }

    public class GetUserPermissionQuery : IRequest<UserPermissionModel>
    {
        public int UserId { get; set; }
        public int PhanHeId { get; set; }

        /// <summary>
        /// Lấy thông tin quyền người dùng
        /// </summary>
        /// <param name="id">Id người dùng</param>
        public GetUserPermissionQuery(int userId, int phanHeId)
        {
            UserId = userId;
            PhanHeId = phanHeId;
        }

        public class Handler : IRequestHandler<GetUserPermissionQuery, UserPermissionModel>
        {
            private readonly IMediator _mediator;
            private readonly IConfiguration _config;

            public Handler(IMediator mediator, IConfiguration config)
            {
                _config = config;
                _mediator = mediator;
            }

            public async Task<UserPermissionModel> Handle(GetUserPermissionQuery request, CancellationToken cancellationToken)
            {
                UserPermissionModel userPermission = new UserPermissionModel();

                var user = await _mediator.Send(new GetUserByIdQuery(request.UserId));

                userPermission.IsActive = user.Active == 1;

                if (!userPermission.IsActive)
                {
                    return userPermission;
                }

                var lsRole = await _mediator.Send(new GetComboboxRoleQuery());
                var lsPermission = await _mediator.Send(new GetComboboxPermissionQuery(idPhanHe: request.PhanHeId));

                var adminUser = _config["Authentication:AdminUserName"];
                if (!string.IsNullOrEmpty(adminUser) && user.UserName.Equals(adminUser, StringComparison.OrdinalIgnoreCase))
                {
                    return new UserPermissionModel()
                    {
                        IsActive = true,
                        Roles = lsRole.Select(x => x.Code).ToList(),
                        Permissions = lsPermission.Select(x => x.Code).ToList()
                    };
                }

                // Lấy danh sách role thuộc người dùng
                var lsUserRole = await _mediator.Send(new GetRoleOfUserQuery(request.UserId));
                var lsUserRoleDetail = lsRole.Where(x => lsUserRole.Contains(x.Id)).Distinct().ToList();

                userPermission.Roles = lsUserRoleDetail.Select(x => x.Code).ToList();

                // Lấy danh sách Permission thuộc người dùng
                userPermission.Permissions = new List<string>();
                foreach (var item in lsUserRoleDetail)
                {
                    var lsPermissionOfRole = await _mediator.Send(new GetPermissionOfRoleQuery(item.Id));
                    var lsPermissionOfRoleDetail = lsPermission.Where(x => lsPermissionOfRole.Contains(x.Id)).ToList();

                    userPermission.Permissions.AddRange(lsPermissionOfRoleDetail.Select(x => x.Code).ToList());
                }
                userPermission.Permissions = userPermission.Permissions.Distinct().OrderBy(x => x).ToList();

                return userPermission;
            }
        }
    }

    //Lấy danh sách người dùng thuộc nhóm người dùng theo tên nhóm người dùng
    public class GetUserOfRoleNameQuery : IRequest<List<int>>
    {
        public string RoleName { get; set; }

        public GetUserOfRoleNameQuery(string roleName)
        {
            this.RoleName = roleName;
        }

        public class Handler : IRequestHandler<GetUserOfRoleNameQuery, List<int>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<int>> Handle(GetUserOfRoleNameQuery request, CancellationToken cancellationToken)
            {
                var role = await _dataContext.Roles.FirstOrDefaultAsync(x => x.Name == request.RoleName);

                if (role == null)
                {
                    return new List<int>();
                }
                var data =
                        from userMapRole in _dataContext.UserMapRoles
                        where userMapRole.RoleId == role.Id
                        select userMapRole.UserId;

                return await data.ToListAsync();
            }
        }
    }

    //Lấy danh sách người dùng thuộc nhóm người dùng theo tên quyền
    public class GetUserOfPermissionCodeQuery : IRequest<List<int>>
    {
        public string PermissionCode { get; set; }

        public GetUserOfPermissionCodeQuery(string permission)
        {
            this.PermissionCode = permission;
        }

        public class Handler : IRequestHandler<GetUserOfPermissionCodeQuery, List<int>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<int>> Handle(GetUserOfPermissionCodeQuery request, CancellationToken cancellationToken)
            {
                var permission = await _dataContext.Permissions.FirstOrDefaultAsync(x => x.Code == request.PermissionCode);

                var roleHasPermission = await _dataContext.RoleMapPermissions
                    .Where(x => x.PermissionId == permission.Id)
                    .Select(x => x.RoleId)
                    .ToListAsync();

                if (!roleHasPermission.Any())
                {
                    return new List<int>();
                }
                var data =
                        from userMapRole in _dataContext.UserMapRoles
                        where roleHasPermission.Contains(userMapRole.RoleId)
                        select userMapRole.UserId;

                return await data.ToListAsync();
            }
        }
    }
}
