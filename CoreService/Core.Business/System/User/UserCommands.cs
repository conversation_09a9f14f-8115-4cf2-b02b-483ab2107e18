using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using System.Collections.Generic;
using Core.DataLog;
using Core.Business.Core.LockDynamic;
using Core.Shared.EmailTemplate;

namespace Core.Business
{
    #region CRUD
    public class CreateUserCommand : IRequest<Unit>
    {
        public CreateUserModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới người dùng
        /// </summary>
        /// <param name="model">Thông tin người dùng cần thêm mới</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public CreateUserCommand(CreateUserModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateUserCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(CreateUserCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Create {UserConstant.CachePrefix}: " + JsonSerializer.Serialize(request.Model));
                var thamSo = await _dataContext.HtThamSoHeThongs.FirstOrDefaultAsync(x => x.IdThamSo == "Them_moi_giao_vien");
                var modelGiaoVien = new GiaoVienBaseModel
                {
                    HoTen = request.Model.FullName,
                    MaCB = request.Model.MaCanBoUser,
                    Email = request.Model.Email,
                    TenDangNhap = request.Model.UserName,
                    MatKhau = request.Model.Password,
                    Ten = ""
                };
                var entity = AutoMapperUtils.AutoMap<CreateUserModel, HtUser>(request.Model);
                var entityGiaoVien = AutoMapperUtils.AutoMap<GiaoVienBaseModel, TKBGiaoVien>(modelGiaoVien);

                var checkCode = await _dataContext.HtUsers.AnyAsync(x => x.UserName == entity.UserName);
                var checkMaGiaoVien = await _dataContext.TKBGiaoViens.AnyAsync(x => x.MaCB == entityGiaoVien.MaCB);
                if (checkCode)
                {
                    throw new ArgumentException("Tên đăng nhập đã tồn tại trong hệ thống!");
                }

                if (!string.IsNullOrEmpty(request.Model.Password))
                {
                    entity.PassWord = XCrypto.MD5(request.Model.Password);
                }
                if (thamSo != null && thamSo.Active && !checkMaGiaoVien)
                {
                    await _dataContext.TKBGiaoViens.AddAsync(entityGiaoVien);

                }

                await _dataContext.HtUsers.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {UserConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới người dùng mã: {entity.UserName}",
                    ObjectCode = UserConstant.CachePrefix,
                    ObjectId = entity.UserId.ToString()
                });

                //Xóa cache
                _cacheService.Remove(UserConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }

    public class UpdateUserCommand : IRequest<Unit>
    {
        public UpdateUserModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật người dùng
        /// </summary>
        /// <param name="model">Thông tin người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateUserCommand(UpdateUserModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateUserCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Update {UserConstant.CachePrefix}: {JsonSerializer.Serialize(request.Model)}");

                var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserId == request.Model.UserId);
                Log.Information($"Before Update {UserConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                request.Model.UpdateEntity(entity);

                _dataContext.HtUsers.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {UserConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(UserConstant.BuildCacheKey(entity.UserId.ToString()));
                _cacheService.Remove(UserConstant.BuildCacheKey());

                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập người dùng mã: {entity.UserName}",
                    ObjectCode = UserConstant.CachePrefix,
                    ObjectId = entity.UserId.ToString()
                });

                return Unit.Value;
            }
        }
    }

    public class UpdateCurrentUserCommand : IRequest<Unit>
    {
        public UserCurrentUpdateModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật người dùng
        /// </summary>
        /// <param name="model">Thông tin người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateCurrentUserCommand(UserCurrentUpdateModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateCurrentUserCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(UpdateCurrentUserCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Update {UserConstant.CachePrefix}: {JsonSerializer.Serialize(request.Model)}");

                var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserId == request.Model.UserId);

                request.Model.UpdateUserInfo(entity);

                _dataContext.HtUsers.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Update completed");

                //Xóa cache
                _cacheService.Remove(UserConstant.BuildCacheKey(entity.UserId.ToString()));
                _cacheService.Remove(UserConstant.BuildCacheKey());

                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"{entity.UserName} cập nhập thông tin cá nhân",
                    ObjectCode = UserConstant.CachePrefix,
                    ObjectId = entity.UserId.ToString()
                });

                return Unit.Value;
            }
        }
    }

    public class DeleteUserCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Xóa người dùng theo danh sách truyền vào
        /// </summary>
        /// <param name="id">Danh sách id cần xóa</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public DeleteUserCommand(int id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<DeleteUserCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Delete {UserConstant.CachePrefix}: {request.Id}");
                var entity = await _dataContext.HtUsers.FindAsync(request.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{UserConstant.CachePrefix} không tồn tại");
                }
                else
                {
                    // Cho phép xóa thông tin Người dùng khi chưa phân quyền
                    var ck = await _dataContext.UserMapRoles.AnyAsync(x => x.UserId == request.Id);
                    if (ck)
                    {
                        throw new Exception("Không thể xóa vì tài khoản đã được phân quyền");
                    }
                    _dataContext.HtUsers.Remove(entity);
                    request.SystemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Xóa người dùng mã: {entity.UserName}",
                        ObjectCode = UserConstant.CachePrefix,
                        ObjectId = entity.UserId.ToString()
                    });

                    //Xóa cache
                    _cacheService.Remove(UserConstant.BuildCacheKey(request.Id.ToString()));
                }
                await _dataContext.SaveChangesAsync();

                _cacheService.Remove(UserConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
    #endregion

    #region Advance

    public class LockAndUnlockUserCommand : IRequest<Unit>
    {
        public int Id { get; set; }
        public int ModifyUserId { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Khóa/mở khóa người dùng
        /// </summary>
        /// <param name="id">Id người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public LockAndUnlockUserCommand(int id, int modifyUserId, SystemLogModel systemLog)
        {
            Id = id;
            ModifyUserId = modifyUserId;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<LockAndUnlockUserCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(LockAndUnlockUserCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Update lock/unlock status {UserConstant.CachePrefix}: {JsonSerializer.Serialize(request.Id)}");

                var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserId == request.Id);
                Log.Information($"Before Update {UserConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                string des = "Mở khóa";
                if (entity.Active == 1)
                {
                    des = "Khóa";
                }

                entity.Active = entity.Active == 0 ? 1 : 0;

                _dataContext.HtUsers.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {UserConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(UserConstant.BuildCacheKey(entity.UserId.ToString()));
                _cacheService.Remove(UserConstant.BuildCacheKey());

                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"{des} người dùng mã: {entity.UserName}",
                    ObjectCode = UserConstant.CachePrefix,
                    ObjectId = entity.UserId.ToString()
                });

                return Unit.Value;
            }
        }
    }

    public class UpdateRoleOfUserCommand : IRequest<Unit>
    {
        public RoleOfUserCreateModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Cập nhật nhóm người dùng thuộc người dùng
        /// </summary>
        /// <param name="model">Thông tin quyền người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdateRoleOfUserCommand(RoleOfUserCreateModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdateRoleOfUserCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<Unit> Handle(UpdateRoleOfUserCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Update Role Of User {UserConstant.CachePrefixUserMapRole}: " + JsonSerializer.Serialize(request.Model));

                var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserId == request.Model.UserId);

                var listCurrentRoleOfUser = await _dataContext.UserMapRoles.Where(x => x.UserId == request.Model.UserId).ToListAsync();

                foreach (var item in listCurrentRoleOfUser)
                {
                    _cacheService.Remove(RoleConstant.BuildCacheKeyUserMapRole(item.RoleId.ToString()));
                }

                _dataContext.UserMapRoles.RemoveRange(listCurrentRoleOfUser);

                foreach (var item in request.Model.RoleIds)
                {
                    await _dataContext.UserMapRoles.AddAsync(new UserMapRole()
                    {
                        UserId = request.Model.UserId,
                        RoleId = item
                    });
                    _cacheService.Remove(RoleConstant.BuildCacheKeyUserMapRole(item.ToString()));
                }

                await _dataContext.SaveChangesAsync();

                Log.Information($"Update Role Of User {UserConstant.CachePrefixUserMapRole} success: {JsonSerializer.Serialize(request.Model)}");
                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật nhóm người dùng cho người dùng: {entity.UserName}",
                    ObjectCode = UserConstant.CachePrefixUserMapRole,
                    ObjectId = request.Model.UserId.ToString()
                });

                //TODO: Xóa cache
                _cacheService.Remove(UserConstant.BuildCacheKeyUserMapRole(request.Model.UserId.ToString()));

                return Unit.Value;
            }
        }
    }

    #endregion

    #region Cập nhật mật khẩu cá nhân

    public class ChangePasswordUserCommand : IRequest<Unit>
    {
        public UserChangePasswordModel Data { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thay đổi mật khẩu (dùng cho người dùng thay đổi mật khẩu cá nhân)
        /// </summary>
        /// <param name="id">Id người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public ChangePasswordUserCommand(UserChangePasswordModel data, SystemLogModel systemLog)
        {
            Data = data;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<ChangePasswordUserCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ILockDynamicHandler _lockDynamicHandler;

            public Handler(SystemDataContext dataContext, ILockDynamicHandler lockDynamicHandler)
            {
                _dataContext = dataContext;
                _lockDynamicHandler = lockDynamicHandler;
            }

            public async Task<Unit> Handle(ChangePasswordUserCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Change user password UserId: {request.Data.UserId}");

                var data = request.Data;
                if (data.UserId == 0)
                    throw new ArgumentException("Không tìm thấy người dùng");

                // Nếu người dùng đã nhập mật khẩu sai quá 5 lần trong 15p thì thực hiện cancel request

                return await _lockDynamicHandler.LimitNumberOfFailuresAsync(async () =>
                {
                    // Kiểm tra dữ liệu đầu vào
                    if (data.NewPassword != data.ConfirmNewPassword)
                    {
                        throw new ArgumentException("Mật khẩu mới không trùng khớp");
                    }

                    //if (!Utils.IsValidPassword(data.NewPassword))
                    //{
                    //    throw new ArgumentException("Mật khẩu mới phải đảm bảo các tiêu chí: tối thiểu 8 ký tự, có chữ hoa, chữ thường, số và ký tự đặc biệt");
                    //}

                    var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserId == data.UserId);

                    if (entity == null)
                        throw new ArgumentException("Không tìm thấy người dùng đang thao tác");

                    // Kiểm tra mật khẩu cũ
                    if (entity.PassWord != XCrypto.MD5(data.OldPassword))
                    {
                        throw new ArgumentException("Mật khẩu cũ không đúng");
                    }

                    entity.PassWord = XCrypto.MD5(data.NewPassword);

                    _dataContext.HtUsers.Update(entity);
                    await _dataContext.SaveChangesAsync();

                    Log.Information($"Update user password complete UserId: {request.Data.UserId}");

                    request.SystemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Người dùng: {entity.UserName} cập nhật lại mật khẩu cá nhân thành công",
                        ObjectCode = UserConstant.CachePrefix,
                        ObjectId = entity.UserId.ToString()
                    });

                    return Unit.Value;
                }, new LockDynamicModel()
                {
                    Key = data.UserId.ToString(),
                    ServiceName = "ChangePassword",
                    TimesToFail = 5,
                    LockInSeconds = 900
                });
            }
        }
    }

    #endregion

    #region Quên mật khẩu

    // Gửi mail đường dẫn truy cập quên mật khẩu
    public class SendEmailForgotPasswordCommand : IRequest<bool>
    {
        public SendEmailForgotPasswordModel Data { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Gửi email để cập nhật mật khẩu mới (trường hợp quên mật khẩu)
        /// </summary>
        /// <param name="id">Id người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public SendEmailForgotPasswordCommand(SendEmailForgotPasswordModel data, SystemLogModel systemLog)
        {
            Data = data;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<SendEmailForgotPasswordCommand, bool>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IMediator _mediator;
            private readonly ILockDynamicHandler _lockDynamicHandler;

            public Handler(SystemDataContext dataContext, IMediator mediator, ILockDynamicHandler lockDynamicHandler)
            {
                _dataContext = dataContext;
                _mediator = mediator;
                _lockDynamicHandler = lockDynamicHandler;
            }

            public async Task<bool> Handle(SendEmailForgotPasswordCommand request, CancellationToken cancellationToken)
            {
                var data = request.Data;
                if (string.IsNullOrEmpty(data.UserName))
                    throw new ArgumentException("Tài khoản không hợp lệ");

                Log.Information($"Send email forgot password UserName: {data.UserName}");

                // Nếu người dùng đã yêu cầu mật khẩu mới quá 3 lần trong 30p thì thực hiện cancel request
                return await _lockDynamicHandler.LimitNumberOfTimesAsync(async () =>
                {
                    var user = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserName == data.UserName && x.Active == 1);

                    if (user == null)
                        throw new ArgumentException("Không tìm thấy thông tin tài khoản hợp lệ");

                    if (string.IsNullOrEmpty(user.Email))
                        throw new ArgumentException("Tài khoản có email không hợp lệ");

                    if (!Utils.IsValidEmail(user.Email))
                        throw new ArgumentException("Tài khoản có email không hợp lệ");

                    // Tạo token
                    var token = Utils.PassowrdRandomString(100, false);

                    // Lưu dữ liệu vào MongoDB
                    var dt = await _mediator.Send(new ForgotPasswordLogCreateCommand(new ForgotPasswordLog()
                    {
                        Token = token,
                        UserId = user.UserId,
                        UserName = user.UserName,
                        UserEmail = user.Email,
                        CreatedDate = DateTime.Now,
                        IsUpdatedPassword = false,
                        ExpireTime = DateTime.Now.AddHours(1),
                        TraceId = request.SystemLog.TraceId
                    }));

                    // Thực hiện gửi mail
                    SendMailUsingTemplateModel sendMailTemplate = new SendMailUsingTemplateModel()
                    {
                        EmailTemplateCode = EmailTemplateEnum.SENDMAIL_FORGOT_PASS.ToString(),
                        To = new List<string> { user.Email },
                        Data = new
                        {
                            user.UserName,
                            user.FullName,
                            Token = token,
                            SecretKey = ForgotPasswordLogConstant.BuildSecretKey(user.UserName, token),
                            ReturnUrl = data.ReturnUrl
                        }
                    };
                    var result = await _mediator.Send(new SendMailUsingEmailTemplateQuery(sendMailTemplate, request.SystemLog));

                    return result;
                }, new LockDynamicModel()
                {
                    Key = data.UserName,
                    ServiceName = "ForgotPassword",
                    IsIgnoreCheckCache = data.IsIgnoreCheckCache,
                    TimesToFail = 3,
                    LockInSeconds = 1800
                });
            }
        }

    }

    // Đổi mật khẩu từ đường dẫn email
    public class UpdatePasswordUserFromForgotPassCommand : IRequest<Unit>
    {
        public UserUpdatePasswordFromForgotPassModel Data { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thay đổi mật khẩu (dùng cho người dùng thay đổi mật khẩu cá nhân)
        /// </summary>
        /// <param name="id">Id người dùng cần cập nhật</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public UpdatePasswordUserFromForgotPassCommand(UserUpdatePasswordFromForgotPassModel data, SystemLogModel systemLog)
        {
            Data = data;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<UpdatePasswordUserFromForgotPassCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IMediator _mediator;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IMediator mediator)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _mediator = mediator;
            }

            public async Task<Unit> Handle(UpdatePasswordUserFromForgotPassCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Update user password with token: {request.Data.Token}");

                var data = request.Data;
                if (string.IsNullOrEmpty(data.Token) || string.IsNullOrEmpty(data.SecretKey))
                    throw new ArgumentException("Mã xác thực không được để trống");

                // Kiểm tra dữ liệu đầu vào
                if (data.NewPassword != data.ConfirmNewPassword)
                {
                    throw new ArgumentException("Mật khẩu mới không trùng khớp");
                }

                if (!Utils.IsValidPassword(data.NewPassword))
                {
                    throw new ArgumentException("Mật khẩu mới phải đảm bảo các tiêu chí: tối thiểu 8 ký tự, có chữ hoa, chữ thường, số và ký tự đặc biệt");
                }

                // Lấy thông tin từ mongoDB theo token xem có tồn tại hay không
                var log = await _mediator.Send(new ForgotPasswordLogGetByTokenQuery(data.Token));
                if (log == null)
                {
                    throw new ArgumentException("Yêu cầu không hợp lệ");
                }

                if (log.ExpireTime.ToLocalTime() < DateTime.Now)
                {
                    throw new ArgumentException("Yêu cầu đã hết hạn");
                }

                // Kiểm tra secret key xem có hợp lệ hay không
                if (data.SecretKey != ForgotPasswordLogConstant.BuildSecretKey(log.UserName, data.Token))
                {
                    throw new ArgumentException("Mã bảo mật không hợp lệ");
                }

                var entity = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserId == log.UserId);

                if (entity == null)
                    throw new ArgumentException("Không tìm thấy người dùng đang thao tác");

                entity.PassWord = XCrypto.MD5(data.NewPassword);

                _dataContext.HtUsers.Update(entity);
                await _dataContext.SaveChangesAsync();

                await _mediator.Send(new ForgotPasswordLogUpdateCompletedCommand(data.Token)).ConfigureAwait(false);

                Log.Information($"Update user password compelte with token: {request.Data.Token}");

                request.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Người dùng: {entity.UserName} thực hiện cập nhật khẩu khẩu qua phương thức quên mật khẩu",
                    ObjectCode = UserConstant.CachePrefix,
                    ObjectId = entity.UserId.ToString()
                });

                return Unit.Value;
            }
        }
    }
    #endregion
}
