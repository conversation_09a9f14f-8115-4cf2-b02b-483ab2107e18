using Core.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UserConstant
    {
        public const string CachePrefix = CacheConstants.USER;
        public const string CachePrefixUserMapRole = CacheConstants.USER_MAP_ROLE;
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }

        public static string BuildCacheKeyUserMapRole(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefixUserMapRole}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefixUserMapRole}-{id}";
            }
        }
    }
}
