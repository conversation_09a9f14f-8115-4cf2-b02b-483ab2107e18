using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Data;


namespace Core.Business
{
    public class GetComboboxTinhQuery : IRequest<List<TinhSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy tỉnh cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxTinhQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxTinhQuery, List<TinhSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TinhSelectItemModel>> Handle(GetComboboxTinhQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = TinhConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvTinhs.OrderBy(x => x.TenTinh)
                                select new TinhSelectItemModel()
                                {
                                    IdTinh = dt.IdTinh,
                                    TenTinh = dt.TenTinh
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.TenTinh.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterTinhQuery : IRequest<PaginationList<TinhBaseModel>>
    {
        public TinhFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tỉnh có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTinhQuery(TinhFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTinhQuery, PaginationList<TinhBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TinhBaseModel>> Handle(GetFilterTinhQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvTinhs
                            select new TinhBaseModel
                            {
                                IdTinh = dt.IdTinh,
                                TenTinh = dt.TenTinh,
                                TenTinhEn = dt.TenTinhEn

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.TenTinh.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<TinhBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetTinhByIdQuery : IRequest<TinhModel>
    {
        public string Id { get; set; }

        /// <summary>
        /// Lấy thông tin tỉnh theo id
        /// </summary>
        /// <param name="id">Id tỉnh</param>
        public GetTinhByIdQuery(string id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTinhByIdQuery, TinhModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TinhModel> Handle(GetTinhByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = TinhConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvTinhs.FirstOrDefaultAsync(x => x.IdTinh == id);

                    return AutoMapperUtils.AutoMap<SvTinh, TinhModel>(entity);
                });
                return item;
            }
        }
    }
}
