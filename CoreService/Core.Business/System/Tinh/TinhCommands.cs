using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{


    public class CreateTinhCommand : IRequest<Unit>
    {
        public CreateTinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateTinhCommand(CreateTinhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateTinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateTinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {TinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateTinhModel, SvTinh>(model);

                var checkCode = await _dataContext.SvTinhs.AnyAsync(x => x.IdTinh == entity.IdTinh || x.TenTinh == entity.TenTinh );
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Tinh.Existed", entity.TenTinh.ToString()]}");
                }

                await _dataContext.SvTinhs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {TinhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tỉnh: {entity.TenTinh}",
                    ObjectCode = TinhConstant.CachePrefix,
                    ObjectId = entity.IdTinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class CreateManyTinhCommand : IRequest<Unit>
    {
        public CreateManyTinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public CreateManyTinhCommand(CreateManyTinhModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<CreateManyTinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(CreateManyTinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create many {TinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var listTinhAdd = model.listTinhModels.Select(x => x.TenTinh).ToList();
                var listIdTinhAdd = model.listTinhModels.Select(x => x.IdTinh).ToList();
                var entity = AutoMapperUtils.AutoMap<CreateManyTinhModel, SvTinh>(model);

                // Check data duplicate
                if (listTinhAdd.Count() != listTinhAdd.Distinct().Count() || listIdTinhAdd.Count() != listIdTinhAdd.Distinct().Count())
                {
                    throw new ArgumentException($"{_localizer["InsertData.Duplicate"]}");
                }
                // Check data exits DB
                if (await _dataContext.SvTinhs.AnyAsync(x => listTinhAdd.Contains(x.TenTinh)) || await _dataContext.SvTinhs.AnyAsync(x => listIdTinhAdd.Contains(x.IdTinh)))
                {
                    throw new ArgumentException($"{_localizer["Tinh.Existed"]}");
                }

                var listEntity = model.listTinhModels.Select(x => new SvTinh()
                {
                    IdTinh = x.IdTinh,
                    TenTinh = x.TenTinh,
                    TenTinhEn = x.TenTinhEn,

                }).ToList();

                await _dataContext.AddRangeAsync(listEntity);
                await _dataContext.SaveChangesAsync();

                var createdIds = listEntity.Select(e => e.IdTinh).ToList();

                Log.Information($"Create many {TinhConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Import tỉnh từ file excel",
                    ObjectCode = TinhConstant.CachePrefix,
                    ObjectId = JsonSerializer.Serialize(createdIds)
                });

                //Xóa cache
                _cacheService.Remove(TinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class UpdateTinhCommand : IRequest<Unit>
    {
        public string Id { get; set; }
        public UpdateTinhModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public UpdateTinhCommand(string id, UpdateTinhModel model, SystemLogModel systemLog)
        {
            Id = id;
            Model = model;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<UpdateTinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(UpdateTinhCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                string id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Update {TinhConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = await _dataContext.SvTinhs.FirstOrDefaultAsync(dt => dt.IdTinh == id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                var checkCode = await _dataContext.SvTinhs.AnyAsync(x => x.TenTinh == model.TenTinh  && x.IdTinh != model.IdTinh);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["Tinh.Existed", model.TenTinh.ToString()]}");
                }

                Log.Information($"Before Update {TinhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);

                _dataContext.SvTinhs.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {TinhConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                Log.Information($"Update {TinhConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật tỉnh: {entity.TenTinh}",
                    ObjectCode = TinhConstant.CachePrefix,
                    ObjectId = entity.IdTinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TinhConstant.BuildCacheKey(entity.IdTinh.ToString()));
                _cacheService.Remove(TinhConstant.BuildCacheKey());
                return Unit.Value;
            }
        }
    }

    public class DeleteTinhCommand : IRequest<Unit>
    {
        public string Id { get; set; }
        public SystemLogModel SystemLog { get; set; }
        public DeleteTinhCommand(string id, SystemLogModel systemLog)
        {
            Id = id;
            SystemLog = systemLog;
        }
        public class Handler : IRequestHandler<DeleteTinhCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
            }
            public async Task<Unit> Handle(DeleteTinhCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var systemLog = request.SystemLog;
                Log.Information($"Delete {TinhConstant.CachePrefix}: " + JsonSerializer.Serialize(id));

                var entity = await _dataContext.SvTinhs.FirstOrDefaultAsync(x => x.IdTinh == id);

                _dataContext.SvTinhs.Remove(entity);

                Log.Information($"Delete {TinhConstant.CachePrefix} success: {JsonSerializer.Serialize(id)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa tỉnh: {entity.TenTinh}",
                    ObjectCode = TinhConstant.CachePrefix,
                    ObjectId = entity.IdTinh.ToString()
                });

                //Xóa cache
                _cacheService.Remove(TinhConstant.BuildCacheKey());
                _cacheService.Remove(TinhConstant.BuildCacheKey(id.ToString()));
                await _dataContext.SaveChangesAsync();

                return Unit.Value;
            }
        }
    }
}
