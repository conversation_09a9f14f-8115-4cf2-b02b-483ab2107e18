using Core.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DanTocSelectItemModel
    {
        public int IdDanToc { get; set; }
        public string MaDanToc { get; set; }
        public string DanToc { get; set; }
        public string DanTocEn { get; set; }
    }

    public class DanTocBaseModel : DanTocSelectItemModel
    {

    }


    public class DanTocModel : DanTocSelectItemModel
    {

    }

    public class DanTocFilterModel : BaseQueryFilterModel
    {
        public DanTocFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdDanToc";
        }
    }

    public class CreateDanTocModel
    {
        [Required(ErrorMessage = "DanToc.IdDanToc.NotRequire")]
        public int IdDanToc { get; set; }

        [MaxLength(5, ErrorMessage = "DanToc.MaDanToc.MaxLength(50)")]
        [Required(ErrorMessage = "DanToc.MaDanToc.NotRequire")]
        public string MaDanToc { get; set; }

        [MaxLength(50, ErrorMessage = "DanToc.DanToc.MaxLength(50)")]
        [Required(ErrorMessage = "DanToc.DanToc.NotRequire")]
        public string DanToc { get; set; }

        [MaxLength(50, ErrorMessage = "DanToc.DanTocEn.MaxLength(50)")]
        public string DanTocEn { get; set; }

    }

    public class CreateManyDanTocModel
    {
        public List<CreateDanTocModel> listDanTocModels { get; set; }
    }

    public class UpdateDanTocModel : CreateDanTocModel
    {
        public void UpdateEntity(SvDanToc input)
        {
            input.IdDanToc = IdDanToc;
            input.MaDanToc = MaDanToc;
            input.DanToc = DanToc;
            input.DanTocEn = DanTocEn;

        }
    }
}
