using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxDanTocQuery : IRequest<List<DanTocSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy dân tộc cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxDanTocQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxDanTocQuery, List<DanTocSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<DanTocSelectItemModel>> Handle(GetComboboxDanTocQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = DanTocConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from dt in _dataContext.SvDanTocs.OrderBy(x => x.DanToc)
                                select new DanTocSelectItemModel()
                                {
                                    IdDanToc = dt.IdDanToc,
                                    MaDanToc = dt.MaDanToc,
                                    DanToc = dt.DanToc
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.DanToc.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetFilterDanTocQuery : IRequest<PaginationList<DanTocBaseModel>>
    {
        public DanTocFilterModel Filter { get; set; }

        /// <summary>
        /// Lấy danh sách dân tộc có lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterDanTocQuery(DanTocFilterModel filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterDanTocQuery, PaginationList<DanTocBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<DanTocBaseModel>> Handle(GetFilterDanTocQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SvDanTocs
                            select new DanTocBaseModel
                            {
                                IdDanToc = dt.IdDanToc,
                                MaDanToc = dt.MaDanToc,
                                DanToc = dt.DanToc,
                                DanTocEn = dt.DanTocEn

                            });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.DanToc.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                return new PaginationList<DanTocBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }

    public class GetDanTocByIdQuery : IRequest<DanTocModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin dân tộc theo id
        /// </summary>
        /// <param name="id">Id dân tộc</param>
        public GetDanTocByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetDanTocByIdQuery, DanTocModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<DanTocModel> Handle(GetDanTocByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;

                string cacheKey = DanTocConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SvDanTocs.FirstOrDefaultAsync(x => x.IdDanToc == id);

                    return AutoMapperUtils.AutoMap<SvDanToc, DanTocModel>(entity);
                });
                return item;
            }
        }
    }
}
