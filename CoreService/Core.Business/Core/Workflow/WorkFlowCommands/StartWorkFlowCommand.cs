using Core.Business.Workflow;
using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.Extensions.Localization;
using MongoDB.Driver;
using OptimaJet.Workflow.Core.Runtime;
using Serilog;
using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class StartWorkFlowCommand : IRequest<GetInfoWorkFlowModel>
    {
        public StartWorkFlowModel Model { get; set; }
        /// <summary>
        /// Xử lý quy trình
        /// </summary>
        /// <param name="model"></param>
        public StartWorkFlowCommand(StartWorkFlowModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<StartWorkFlowCommand, GetInfoWorkFlowModel>
        {
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<GetInfoWorkFlowModel> Handle(StartWorkFlowCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var result = new GetInfoWorkFlowModel();
                result.ProcessId = Guid.NewGuid();
                Log.Information($"Start workflow {WorkflowConstant.CachePrefix}: {result.ProcessId}");

                // Nếu bị trùng id quy trình thì yêu cầu khách hàng thao tác lại
                if (await WorkflowInit.Runtime.IsProcessExistsAsync(result.ProcessId))
                    throw new ArgumentException($"{_localizer["bai-bao.vui-long-thao-tac-lai"]}");

                // Lấy thông tin workflow name mặc định từ cấu hình hoặc do người dùng chọn
                if (string.IsNullOrEmpty(model.WorkflowName))
                {
                    // Tên quy trình được lấy trong chức năng quy trình trên trang system
                    // TODO: Thực hiện gán thông tin này vào cấu hình
                    throw new ArgumentException($"{_localizer["bai-bao.vui-long-thao-tac-lai"]}");
                }

                // Gán quy trình
                string userId = _contextAccessor.UserId.HasValue ? _contextAccessor.UserId.Value.ToString() : string.Empty;
                await WorkflowInit.Runtime.CreateInstanceAsync(
                    model.WorkflowName,
                    result.ProcessId,
                    identityId: userId,
                    userId
                );

                // Lấy state hiện tại gán vào dữ liệu
                var workflowState = await WorkflowInit.Runtime.GetCurrentStateNameAsync(result.ProcessId);
                result.State = workflowState;
                result.AuthorId = userId;

                Log.Information($"Start workflow {WorkflowConstant.CachePrefix} success: {JsonSerializer.Serialize(result)}");

                return result;
            }
        }
    }
}
