using Core.Business.Workflow;
using Core.DataLog;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.Extensions.Localization;
using MongoDB.Driver;
using OptimaJet.Workflow.Core.Runtime;
using Serilog;
using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class ProcessWorkFlowCommand : IRequest<CommandExecutionResult>
    {
        public ProcessWorkFlowModel Model { get; set; }
        /// <summary>
        /// Xử lý quy trình
        /// </summary>
        /// <param name="model"></param>
        public ProcessWorkFlowCommand(ProcessWorkFlowModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<ProcessWorkFlowCommand, CommandExecutionResult>
        {
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<CommandExecutionResult> Handle(ProcessWorkFlowCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;

                Log.Information($"Process workflow {WorkflowConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                if (string.IsNullOrEmpty(model.CommandName))
                {
                    throw new ArgumentException($"{_localizer["bai-bao.quy-trinh-xu-ly-khong-hop-le"]}");
                }

                string userId = _contextAccessor.UserId.HasValue ? _contextAccessor.UserId.Value.ToString() : string.Empty;
                
                var commands = await WorkflowInit.Runtime.GetAvailableCommandsAsync(model.ProcessId, userId);

                var command =
                commands.FirstOrDefault(
                        c => c.CommandName.Equals(model.CommandName, StringComparison.CurrentCultureIgnoreCase));

                if (command == null)
                    throw new ArgumentException($"{_localizer["bai-bao.quy-trinh-xu-ly-khong-hop-le"]}");

                // Comment approval
                if(!string.IsNullOrEmpty(model.Commentary))
                    command.SetParameter("Comment", model.Commentary);
                
                var rsWF = await WorkflowInit.Runtime.ExecuteCommandAsync(command, userId, userId);

                Log.Information($"Process workflow {WorkflowConstant.CachePrefix} success: {JsonSerializer.Serialize(model)}");

                return rsWF;
            }
        }
    }
}
