using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Minio.Exceptions;
using Serilog.Context;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Authentication;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Core.Business.Core.LockDynamic
{
    public class LockDynamicHandler : ILockDynamicHandler
    {
        private readonly ICacheService _cacheService;
        private readonly IMediator _mediator;

        public LockDynamicHandler(ICacheService cacheService, IMediator mediator)
        {
            _cacheService = cacheService;
            _mediator = mediator;
        }

        private string BuildCacheKeyLockDynamicService(LockDynamicModel lockDynamic)
        {
            //Cache cho item
            return $"{lockDynamic.ServiceName}:{lockDynamic.Key}";
        }

        /// <summary>
        /// Kh<PERSON>a theo số lần gọi (<PERSON><PERSON><PERSON> mail, gửi sms, gọi api,...)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="func"></param>
        /// <param name="lockDynamic"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<T> LimitNumberOfTimesAsync<T>(Func<Task<T>> func, LockDynamicModel lockDynamic)
        {
            // Kiểm tra dữ liệu cache xem đã đạt đến giới hạn khóa chưa
            var cacheKey = BuildCacheKeyLockDynamicService(lockDynamic);
            var itemCache = await _cacheService.GetOrCreate(cacheKey, () => new LockDynamicCacheModel());

            if (lockDynamic.IsIgnoreCheckCache)
            {
                itemCache = new LockDynamicCacheModel();
            }

            if (itemCache != null && itemCache.Key == lockDynamic.Key)
            {
                if (itemCache.CreatedTime.AddSeconds(lockDynamic.LockInSeconds) < DateTime.Now)
                {
                    itemCache.Times = 1;
                    itemCache.CreatedTime = DateTime.Now;
                    _cacheService.Remove(cacheKey);
                }
                else if (itemCache.Times >= lockDynamic.TimesToFail)
                {
                    throw new ArgumentException("Bạn đã yêu cầu quá nhiều lần, vui lòng thử lại sau");
                }
            }

            itemCache.Key = lockDynamic.Key;
            itemCache.Times++;
            itemCache.CreatedTime = DateTime.Now;

            await _cacheService.Set(cacheKey, itemCache, new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromMinutes(120)));

            return await func();
        }

        /// <summary>
        /// Khóa theo số lần gọi thất bại (Đăng nhập thất bại, sai thông tin xác thực...)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="func"></param>
        /// <param name="lockDynamic"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<T> LimitNumberOfFailuresAsync<T>(Func<Task<T>> func, LockDynamicModel lockDynamic)
        {
            // Kiểm tra dữ liệu cache xem đã đạt đến giới hạn khóa chưa
            var cacheKey = BuildCacheKeyLockDynamicService(lockDynamic);
            var itemCache = await _cacheService.GetOrCreate(cacheKey, () => new LockDynamicCacheModel());

            if (lockDynamic.IsIgnoreCheckCache)
            {
                itemCache = new LockDynamicCacheModel();
            }

            if (itemCache != null && itemCache.Key == lockDynamic.Key)
            {
                if (itemCache.CreatedTime.AddSeconds(lockDynamic.LockInSeconds) < DateTime.Now)
                {
                    itemCache.Times = 1;
                    itemCache.CreatedTime = DateTime.Now;
                    _cacheService.Remove(cacheKey);
                }
                else if (itemCache.Times >= lockDynamic.TimesToFail)
                {
                    throw new ArgumentException("Bạn đã yêu cầu quá nhiều lần, vui lòng thử lại sau");
                }
            }
            
            try
            {
                var rs = await func();
                _cacheService.Remove(cacheKey);
                return rs;
            }
            catch (Exception)
            {
                itemCache.Key = lockDynamic.Key;
                itemCache.Times++;
                itemCache.CreatedTime = DateTime.Now;

                await _cacheService.Set(cacheKey, itemCache, new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromMinutes(120)));
                throw;
            }
        }
    }
}
