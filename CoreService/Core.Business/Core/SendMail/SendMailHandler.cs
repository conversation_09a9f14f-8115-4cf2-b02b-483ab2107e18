using Core.Shared;
using RabbitMQ.Client;
using System;
using System.Net.Mail;
using System.Net.Mime;
using System.Net;
using System.Text;
using System.Text.Json;
using FluentEmail.Core;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Core.Business.Core
{
    public class SendMailHandler : ISendMailHandler
    {
        private readonly string _exchange = string.Empty;
        private readonly string _queue = string.Empty;
        private readonly string _routingKey = string.Empty;

        private readonly EmailConfigurationModel _mailConfig = new EmailConfigurationModel();

        private readonly IConfiguration _config;
        private readonly IFluentEmail _fluentEmail;

        public SendMailHandler(IFluentEmail fluentEmail, IConfiguration config)
        {
            _config = config;
            _exchange = _queue = _routingKey = Utils.GetEventNameAttribute(typeof(SendMailModel));

            _mailConfig.MailConfigSmtp = _config["EmailSettings:Host"];
            _mailConfig.MailConfigPort = _config["EmailSettings:Port"];
            _mailConfig.MailConfigSsl = _config["EmailSettings:SSL"] == "1";
            _mailConfig.MailConfigFrom = _config["EmailSettings:UserName"];
            _mailConfig.MailConfigUser = _config["EmailSettings:DefaultFromName"];
            _mailConfig.MailConfigPassword = _config["EmailSettings:Password"];

            _fluentEmail = fluentEmail
                ?? throw new ArgumentNullException(nameof(fluentEmail));
        }

        public void SendMailGoogle(SendMailModel model)
        {
            SmtpClient smtpClient = new SmtpClient();
            MailMessage mail = new MailMessage();
            
            mail.BodyEncoding = Encoding.UTF8;
            mail.Priority = MailPriority.Normal;

            smtpClient.UseDefaultCredentials = false;
            smtpClient.EnableSsl = _mailConfig.MailConfigSsl;

            mail.From = new MailAddress(_mailConfig.MailConfigFrom, _mailConfig.MailConfigUser);

            smtpClient.Timeout = 10000;
            
            // set port
            int port;
            if (int.TryParse(_mailConfig.MailConfigPort, out port))
                smtpClient.Port = port;

            // set host
            if (!string.IsNullOrEmpty(_mailConfig.MailConfigSmtp))
                smtpClient.Host = _mailConfig.MailConfigSmtp;

            // set credential
            if (!string.IsNullOrEmpty(_mailConfig.MailConfigPassword) && !string.IsNullOrEmpty(_mailConfig.MailConfigFrom))
                smtpClient.Credentials = new NetworkCredential(_mailConfig.MailConfigFrom, _mailConfig.MailConfigPassword);

            if (model.ToEmails != null && model.ToEmails.Count > 0)
            {
                foreach (string email in model.ToEmails)
                {
                    if (IsValidEmail(email))
                        mail.To.Add(email);
                }
            }

            mail.Subject = model.Title;
            mail.Body = model.Body;
            mail.AlternateViews.Add(AlternateView.CreateAlternateViewFromString(model.Body, new ContentType("text/html")));

            if (_mailConfig.MailConfigSendType == "sync" || _mailConfig.MailConfigSendType == "both")
                smtpClient.Send(mail);

            if (_mailConfig.MailConfigSendType == "async" || _mailConfig.MailConfigSendType == "both")
                smtpClient.SendAsync(mail, Guid.NewGuid());
        }

        public void SendMailQueue(SendMailModel model)
        {
            RabbitMQUtils.PublishMessage(_config["RabbitMQ:Uri"], typeof(SendMailModel), model, Guid.NewGuid().ToString());
        }

        public async Task SendMailFluentEmail(EmailMetadata emailMetadata)
        {
            await _fluentEmail.To(emailMetadata.ToEmail)
                .Subject(emailMetadata.Subject)
                .Body(emailMetadata.Body)
                .SendAsync();
        }

        public async Task SendMailUsingTemplate(EmailMetadata emailMetadata, string template, object data)
        {
            await _fluentEmail.To(emailMetadata.ToEmail)
                .Subject(emailMetadata.Subject)
                .UsingTemplate(template, data, true)
                .SendAsync();
        }

        private bool IsValidEmail(string emailAddress)
        {
            try
            {
                MailAddress m = new MailAddress(emailAddress);
                return true;
            }
            catch (FormatException)
            {
                return false;
            }
        }
    }
}
