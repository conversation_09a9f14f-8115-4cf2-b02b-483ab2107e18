using Core.Shared;
using System.Collections.Generic;

namespace Core.Business.Core
{
    public class EmailConfigurationModel
    {
        public string MailConfigSmtp { get; set; }
        public string MailConfigPort { get; set; }
        public bool MailConfigSsl { get; set; }
        public string MailConfigSendType { get; set; } = "sync";
        public string MailConfigFrom { get; set; }
        public string MailConfigUser { get; set; }
        public string MailConfigPassword { get; set; }
    }

    [EventName("send-mail-message")]
    public class SendMailModel
    {
        public List<string> ToEmails { get; set; }
        public string Title { get; set; }
        public string Body { get; set; }
    }

    public class EmailMetadata
    {
        public string ToEmail { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public string Template { get; set; }
        public object Data { get; set; }
        public EmailMetadata(string to, string subject)
        {
            ToEmail = to;
            Subject = subject;
        }
    }
}
