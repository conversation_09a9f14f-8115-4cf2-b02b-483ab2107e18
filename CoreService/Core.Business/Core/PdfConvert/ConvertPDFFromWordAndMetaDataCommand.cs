using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Core.Shared;
using Core.Shared.Model;
using MediatR;
using Microsoft.Extensions.Localization;
using Serilog;

namespace Core.Business.System.Pdf
{
    public class ConvertPDFFromWordAndMetaDataCommand : IRequest<FileBase64Response>
    {
        public PdfConvertFromWordBase64Model Input { get; set; }
        public ConvertPDFFromWordAndMetaDataCommand(PdfConvertFromWordBase64Model input)
        {
            Input = input;
        }
        public class Handler : IRequestHandler<ConvertPDFFromWordAndMetaDataCommand, FileBase64Response>
        {
            public async Task<FileBase64Response> Handle(ConvertPDFFromWordAndMetaDataCommand request, CancellationToken cancellationToken)
            {
                var model = request.Input;
                if (string.IsNullOrEmpty(model.FileBase64))
                    throw new ArgumentException($"Content file Word is null or empty");

                Log.Information($"Convert PDF from Docx and Meta-data: {model.FileName}");

                var pdfGenerateResult = OfficeUtils.GeneratePdfFromWordBase64MetaData(model);
                Log.Information($"Completed Convert PDF from Docx and Meta-data: {model.FileName}");

                return pdfGenerateResult;
            }
        }
    }
}
