using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateFileAttachmentCommand : IRequest<FileAttachment>
    {
        public FileAttachmentModel Model { get; set; }
        public SystemLogModel SystemLog { get; set; }

        /// <summary>
        /// Thêm mới file đính kèm
        /// </summary>
        /// <param name="model">Thông tin file đính kèm cần thêm mới</param>
        /// <param name="systemLog">Thông tin lưu log</param>
        public CreateFileAttachmentCommand(FileAttachmentModel model, SystemLogModel systemLog)
        {
            Model = model;
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<CreateFileAttachmentCommand, FileAttachment>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<FileAttachment> Handle(CreateFileAttachmentCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var systemLog = request.SystemLog;
                Log.Information($"Create {FileAttachmentConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<FileAttachmentModel, FileAttachment>(model);

                entity.CreateDate = DateTime.UtcNow;
                entity.CreateUserName = model.CreateUserName;
                await _dataContext.FileAttachments.AddAsync(entity, cancellationToken);
                await _dataContext.SaveChangesAsync(cancellationToken);

                Log.Information($"Create {FileAttachmentConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                systemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới file đính kèm: {entity.FileName}",
                    ObjectCode = FileAttachmentConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(FileAttachmentConstant.BuildCacheKey());

                return entity;
            }
        }
    }
}
