using Core.Shared;

namespace Core.Business
{
    public class FileAttachmentConstant
    {
        public const string SelectItemCacheSubfix = CacheConstants.LIST_SELECT;
        public const string CachePrefix = CacheConstants.FILE_ATTACHMENT;

        public static string BuildCacheKey(string id = "")
        {
            if (string.IsNullOrEmpty(id))
            {
                //Cache cho danh sách combobox
                return $"{CachePrefix}-{SelectItemCacheSubfix}";
            }
            else
            {
                //Cache cho item
                return $"{CachePrefix}-{id}";
            }
        }
    }
}
