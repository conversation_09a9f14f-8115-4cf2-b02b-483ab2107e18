using Core.Shared;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Lấy All dữ liệu Action Code
    /// </summary>
    /// <returns>Danh sách Action Code và Action Name</returns>
    public class SystemLogGetActionCodeForComboboxQuery : IRequest<List<ActionCodeForComboboxModel>>
    {
        public class Handler : IRequestHandler<SystemLogGetActionCodeForComboboxQuery, List<ActionCodeForComboboxModel>>
        {
            public async Task<List<ActionCodeForComboboxModel>> Handle(SystemLogGetActionCodeForComboboxQuery request, CancellationToken cancellationToken)
            {
                var result = new List<ActionCodeForComboboxModel>();

                Type type = typeof(LogConstants);
                var flags = BindingFlags.Static | BindingFlags.Public;
                var fields = type.GetFields(flags).Where(x => x.IsLiteral && !x.IsInitOnly).ToList();

                foreach (var field in fields)
                {
                    var actionCodeModel = new ActionCodeForComboboxModel();
                    actionCodeModel.ActionName = (string)field.GetRawConstantValue();
                    actionCodeModel.ActionCode = field.Name;

                    result.Add(actionCodeModel);
                }

                return result;
            }
        }
    }
}
