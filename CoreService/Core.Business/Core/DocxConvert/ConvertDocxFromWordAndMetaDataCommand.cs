using System;
using System.Threading;
using System.Threading.Tasks;
using Core.Shared.Model;
using MediatR;
using Serilog;

namespace Core.Business.System.Docx
{
    public class ConvertDocxFromWordAndMetaDataCommand : IRequest<FileBase64Response>
    {
        public DocxConvertFromWordBase64Model Input { get; set; }
        public ConvertDocxFromWordAndMetaDataCommand(DocxConvertFromWordBase64Model input)
        {
            Input = input;
        }
        public class Handler : IRequestHandler<ConvertDocxFromWordAndMetaDataCommand, FileBase64Response>
        {
            public async Task<FileBase64Response> Handle(ConvertDocxFromWordAndMetaDataCommand request, CancellationToken cancellationToken)
            {
                var model = request.Input;
                if (string.IsNullOrEmpty(model.FileBase64))
                    throw new ArgumentException($"Content file Word is null or empty");

                Log.Information($"Convert Docx from Docx and Meta-data: {model.FileName}");

                var docxGenerateResult = OfficeUtils.GenerateDocxFromWordBase64MetaData(model);
                Log.Information($"Completed Convert Docx from Docx and Meta-data: {model.FileName}");

                return docxGenerateResult;
            }
        }
    }
}
