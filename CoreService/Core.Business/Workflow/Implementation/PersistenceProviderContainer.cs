using Core.Business.Workflow.Interface;
using Core.Shared;
using Microsoft.Extensions.Configuration;
using OptimaJet.Workflow.Core.Persistence;
using OptimaJet.Workflow.DbPersistence;
using System;
using System.Configuration;

namespace Core.Business.Workflow.Implementation
{
    public class PersistenceProviderContainer : IPersistenceProviderContainer
    {
        public PersistenceProviderContainer(IConfiguration config)
        {
            var connectionString = String.Format(config["Database:Workflow:ConnectionString:MSSQLDatabase"],
                AesEncryption.Decrypt(config, config["Database:UserId"]),
                AesEncryption.Decrypt(config, config["Database:Password"]));
            Provider = new MSSQLProvider(connectionString);
        }

        public IWorkflowProvider Provider { get; private set; }
    }
}
