using System;
using System.Reflection;
using System.Xml.Linq;
using OptimaJet.Workflow.Core.Builder;
using OptimaJet.Workflow.Core.Parser;
using OptimaJet.Workflow.Core.Runtime;
using System.Threading.Tasks;
using System.Collections.Generic;
using OptimaJet.Workflow.Core.Model;
using OptimaJet.Workflow.Core.Persistence;
using OptimaJet.Workflow.Plugins;
using Core.Business.Workflow.Interface;
using OptimaJet.Workflow.Migrator;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using Serilog;
using Newtonsoft.Json;

namespace Core.Business.Workflow
{
    public static class WorkflowInit
    {
        private static volatile WorkflowRuntime _runtime;

        private static readonly object _sync = new object();

        public static IServiceProvider ServiceProvider { get; set; }
        public static IPersistenceProviderContainer PersistenceProviderContainer { get; private set; }

        public static WorkflowRuntime Create(IPersistenceProviderContainer persistenceProviderContainer)
        {
            PersistenceProviderContainer = persistenceProviderContainer;
            CreateRuntime();
            return Runtime;
        }

        private static void CreateRuntime()
        {
            if (_runtime == null)
            {
                lock (_sync)
                {
                    if (_runtime == null)
                    {
                        Log.Debug("Creating WorkflowRuntime");
                        var approvalPlugin = new ApprovalPlugin();

                        #region ApprovalPlugin Settings

                        // approvalPlugin.AutoApprovalHistory = true;
                        approvalPlugin.NameParameterForComment = "Comment";
                        approvalPlugin.GetUserNamesByIds += GeUserNamesByIds;
                        #endregion ApprovalPlugin Settings

                        var basicPlugin = new BasicPlugin();

                        #region BasicPlugin Settings

                        //Settings for SendEmail actions
                        // basicPlugin.Setting_Mailserver = "smtp.yourserver.com";
                        // basicPlugin.Setting_MailserverPort = 25;
                        // basicPlugin.Setting_MailserverFrom = "<EMAIL>";
                        // basicPlugin.Setting_MailserverLogin = "<EMAIL>";
                        // basicPlugin.Setting_MailserverPassword = "password";
                        // basicPlugin.Setting_MailserverSsl = true;

                        //not implemented
                        basicPlugin.ApproversInStageAsync += ApproversInStageAsync;

                        basicPlugin.UsersInRoleAsync += UsersInRoleAsync;
                        basicPlugin.CheckPredefinedActorAsync += CheckPredefinedActorAsync;
                        basicPlugin.GetPredefinedIdentitiesAsync += GetPredefinedIdentitiesAsync;
                        basicPlugin.UpdateDocumentStateAsync += UpdateDocumentStateAsync;

                        basicPlugin.WithActors(new List<string>() { "Author", "Manager" });
                        #endregion BasicPlugin Settings

                        var provider = PersistenceProviderContainer.Provider;

                        var builder = new WorkflowBuilder<XElement>(provider, new XmlWorkflowParser(), provider).WithDefaultCache();
                        _runtime = new WorkflowRuntime();

                        _runtime.GetUsersWithIdentitiesAsync += GetUsersWithIdentitiesAsync;
                        _runtime.GetUserByIdentityAsync += GetUserById;
                        _runtime.GetCustomTimerValueAsync += GetCustomTimerValueAsync;

                        _runtime
                            .WithBuilder(builder)
                            .WithActionProvider(new ActionProvider())
                            .WithRuleProvider(new RuleProvider())
                            .WithDesignerAutocompleteProvider(new AutoCompleteProvider())
                            .WithPersistenceProvider(provider)
                            .SwitchAutoUpdateSchemeBeforeGetAvailableCommandsOn()
                            .RegisterAssemblyForCodeActions(Assembly.GetExecutingAssembly())
                            .WithPlugins(null, basicPlugin, approvalPlugin)
                            .CodeActionsDebugOn()
                            .AsSingleServer()
                            .RunMigrations()
                            //.AsMultiServer()
                            //    .WithConsoleAllLogger()
                            .Start();

                        Log.Debug("WorkflowRuntime is created");
                    }
                }
            }
        }

        public static async Task<DateTime?> GetCustomTimerValueAsync(string value, string name)
        {
            // Log.Debug($"GetCustomTimerValueAsync - value: {value} - name: {name}");
            return value == "ten seconds" ? Runtime.RuntimeDateTimeNow.AddSeconds(10) : (DateTime?)null;
        }

        public static async Task<string> GetUserById(string identity)
        {
            // Log.Debug($"GetUserById - identity: {identity}");
            var mediator = ServiceProvider.GetService<IMediator>();
            var user = await mediator.Send(new GetUserByIdQuery(int.Parse(identity)));

            return user?.UserName;
        }

        public static async Task<IEnumerable<UserIdentity>> GetUsersWithIdentitiesAsync(string userName = null, SortDirection sortDirection = SortDirection.Asc, Paging paging = null)
        {
            // Log.Debug($"GetUsersWithIdentitiesAsync - userName: {userName}");
            var result = new List<UserIdentity>();
            var mediator = ServiceProvider.GetService<IMediator>();
            var users = await mediator.Send(new GetFilterUserQuery(new UserQueryFilter
            {
                Ascending = sortDirection.ToString(),
                PropertyName = "UserName",
                TextSearch = userName,
            }));

            foreach (var user in users.Data)
            {
                result.Add(new UserIdentity() { Identity = user.UserId.ToString(), UserName = user.UserName });
            }

            return result;
        }

        public static async Task<IEnumerable<string>> UsersInRoleAsync(string roleName, ProcessInstance processInstance)
        {
            // Log.Debug($"UsersInRoleAsync - roleName: {roleName}");
            // Log.Debug($"UsersInRoleAsync - processInstance: {JsonConvert.SerializeObject(processInstance)}");
            var mediator = ServiceProvider.GetService<IMediator>();

            var userInRoles = await mediator.Send(new GetUserOfRoleNameQuery(roleName));

            return userInRoles.Select(x => x.ToString()).ToList();
        }

        public static async Task<bool> CheckPredefinedActorAsync(ProcessInstance processInstance, WorkflowRuntime runtime, string parameter, string identityId)
        {
            // Log.Debug($"CheckPredefinedActorAsync - identityId: {identityId}");
            // Log.Debug($"CheckPredefinedActorAsync - parameter: {parameter}");
            // Log.Debug($"CheckPredefinedActorAsync - processInstance: {JsonConvert.SerializeObject(processInstance)}");
            var mediator = ServiceProvider.GetService<IMediator>();
            var userInRoles = await mediator.Send(new GetUserOfRoleNameQuery(parameter));

            if (userInRoles == null || userInRoles.Count == 0)
                return false;

            if (userInRoles.Contains(int.Parse(identityId)))
                return true;

            return false;
        }

        public static async Task<IEnumerable<string>> GetPredefinedIdentitiesAsync(ProcessInstance processInstance, WorkflowRuntime runtime, string parameter)
        {
            // Log.Debug($"GetPredefinedIdentitiesAsync - parameter: {parameter}");
            // Log.Debug($"GetPredefinedIdentitiesAsync - processInstance: {JsonConvert.SerializeObject(processInstance)}");
            var mediator = ServiceProvider.GetService<IMediator>();
            var userInRoles = await mediator.Send(new GetUserOfRoleNameQuery(parameter));

            if (userInRoles == null || userInRoles.Count == 0)
                return new List<string>();

            return userInRoles.Select(x => x.ToString()).ToList();
        }


        public static async Task<IEnumerable<string>> ApproversInStageAsync(string stageName, ProcessInstance processInstance)
        {
            // Log.Debug($"ApproversInStageAsync - stageName: {stageName}");
            // Log.Debug($"ApproversInStageAsync - processInstance: {JsonConvert.SerializeObject(processInstance)}");
            throw new NotImplementedException();
        }

        public static List<string> GeUserNamesByIds(List<string> ids)
        {
            // Log.Debug($"GeUserNamesByIds - ids: {JsonConvert.SerializeObject(ids)}");
            var mediator = ServiceProvider.GetService<IMediator>();
            var userCaches = (mediator.Send(new GetComboboxUserQuery())).Result;

            return userCaches
                .Where(x => ids.Contains(x.UserId.ToString()))
                .Select(x => x.UserName)
                .ToList();
        }

        public static async Task UpdateDocumentStateAsync(ProcessInstance processInstance, string stateName, string localizedStateName)
        {
            // Log.Debug($"UpdateDocumentStateAsync - stateName: {stateName}");
            // Log.Debug($"UpdateDocumentStateAsync - localizedStateName: {localizedStateName}");
            // Log.Debug($"UpdateDocumentStateAsync - processInstance: {JsonConvert.SerializeObject(processInstance)}");
            // Thực hiện các hàm cập nhật trạng thái của tài liệu
        }
        public static WorkflowRuntime Runtime => _runtime;

    }
}
