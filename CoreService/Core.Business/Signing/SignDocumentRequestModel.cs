using Core.Shared.Model;

namespace Core.Business.Signing;

public class SignDocumentRequestModel
{
    /// <summary>
    /// File cần ký
    /// </summary>
    public string FileBase64 { get; set; }
    
    /// <summary>
    /// Id chứng thư số thực hiện ký
    /// </summary>
    public int ChungThuSoId { get; set; }
    
    /// <summary>
    /// Id mẫu chữ ký của người dùng
    /// </summary>
    public int? MauChuKyId { get; set; }
    
    /// <summary>
    /// Cấu hình hiển thị chữ ký trên tài liệu cần ký
    /// </summary>
    public SignatureAppearanceConfiguration Appearance { get; set; }
}

public class SignPDFRequestModel
{
    public string Base64Pdf { get; set; }
    public ChungThuSoModel ChungThuSo { get; set; }
    public MauChuKyModel MauChuKy { get; set; }
    
    public SignatureAppearanceConfiguration Appearance { get; set; }
}
