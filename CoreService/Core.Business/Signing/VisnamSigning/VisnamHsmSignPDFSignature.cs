using iText.Kernel.Crypto;
using iText.Signatures;
using System;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Core.Shared;
using Core.Shared.Enums;
using MediatR;
using VisnamSignServerClient;
using Serilog;

namespace Core.Business.Signing
{
    public class VisnamHsmSignPDFSignature
    {
        private readonly VisnamSignServerApiClient _client;
        private readonly IMediator _mediator;

        public VisnamHsmSignPDFSignature(VisnamSignServerApiClient client, IMediator mediator)
        {
            _client = client;
            _mediator = mediator;
        }

        public async Task<string> SignPDF(SignPDFRequestModel request)
        {
            #region Validate CTS
            var visnamAccount = await _mediator.Send(new GetVisnamTaiKhoanKetNoiByIdQuery(request.ChungThuSo.ReferenceId));
            if (visnamAccount == null)
            {
                throw new ArgumentException(
                    $"Không tìm thấy tài khoản kết nối Visnam với ReferenceId: {request.ChungThuSo.ReferenceId}");
            }

            // <PERSON>ểm tra dữ liệu cần thiết
            if (string.IsNullOrEmpty(visnamAccount.Key))
            {
                throw new ArgumentException("Key của tài khoản Visnam không được để trống");
            }

            if (string.IsNullOrEmpty(visnamAccount.Secret))
            {
                throw new ArgumentException("Secret của tài khoản Visnam không được để trống");
            }

            // Thử decrypt secret với error handling
            string decryptedSecret;
            try
            {
                decryptedSecret = AesEncryption.Decrypt(visnamAccount.Secret, AesEncryption.KeyDefault);
                if (string.IsNullOrEmpty(decryptedSecret))
                {
                    throw new Exception("Secret sau khi decrypt bị rỗng");
                }
            }
            catch (Exception ex)
            {
                throw new CryptographicException($"Lỗi khi decrypt secret: {ex.Message}. " +
                                                 $"Secret length: {visnamAccount.Secret?.Length ?? 0}, " +
                                                 $"Secret preview: {visnamAccount.Secret?.Substring(0, Math.Min(20, visnamAccount.Secret?.Length ?? 0))}...",
                    ex);
            }

            #endregion
            
            // Tạo request cho SignPDFSync
            var signPdfRequest = new SignPDFSyncRequestModel
            {
                Base64Pdf = request.Base64Pdf,
                Base64Image = request.MauChuKy?.ImageBase64,
                UserId = visnamAccount.Key,
                UserKey = decryptedSecret,
                // TextOut = "SubjectName", // Sử dụng SubjectName của chứng thư số làm text hiển thị
                SignatureName = $"Signature-{DateTime.Now:yyyyMMddHHmmss}",
                // AppendDateSign = true,
                PageSign = request.Appearance.PageNumber,
                TypeSignature = 2,
                TextLocationIdentifier = "SIGN"
            };

            // Thiết lập vị trí và kích thước chữ ký dựa trên loại ký
            if (request.MauChuKy != null && request.Appearance != null)
            {
                // if (request.MauChuKy.LoaiKySuDung == (short)LoaiKyEnum.KyNhay)
                // {
                //     signPdfRequest.XPoint = 500;
                //     signPdfRequest.YPoint = 190;
                //     signPdfRequest.Width = 100;
                //     signPdfRequest.Height = 50;
                // }
                // else
                // {
                //     signPdfRequest.XPoint = 395;
                //     signPdfRequest.YPoint = 100;
                //     signPdfRequest.Width = 200;
                //     signPdfRequest.Height = 100;
                // }
                signPdfRequest.XPoint = request.Appearance.XPoint;
                signPdfRequest.YPoint = request.Appearance.YPoint;
                signPdfRequest.Width = request.Appearance.Width;
                signPdfRequest.Height = request.Appearance.Height;
            }

            // Gọi hàm SignPDFSync
            var signResult = _client.SignPDFSync(signPdfRequest);

            // Kiểm tra kết quả
            if (signResult.Status != 0)
            {
                throw new Exception($"Ký PDF thất bại: {signResult.Error} (Status: {signResult.Status})");
            }

            if (string.IsNullOrEmpty(signResult.Obj))
            {
                throw new Exception("API trả về file PDF đã ký rỗng");
            }

            return signResult.Obj;
        }
    }
}