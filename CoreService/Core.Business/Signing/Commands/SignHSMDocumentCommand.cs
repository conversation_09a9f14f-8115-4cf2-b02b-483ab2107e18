using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using Core.Shared;
using Core.Shared.Enums;
using MediatR;
using Org.BouncyCastle.X509;
using Serilog;
using VisnamSignServerClient;

namespace Core.Business.Signing;

public class SignHSMDocumentCommand : IRequest<SignDocumentResponseModel>
{
    public SignDocumentRequestModel SignDocumentRequestModel { get; set; }

    public SignHSMDocumentCommand(SignDocumentRequestModel signDocumentRequestModel)
    {
        SignDocumentRequestModel = signDocumentRequestModel;
    }

    public class Handler : IRequestHandler<SignHSMDocumentCommand, SignDocumentResponseModel>
    {
        private readonly IMediator _mediator;
        private readonly VisnamHsmSignPDFSignature _hsmSignPdf;

        public Handler(IMediator mediator, VisnamHsmSignPDFSignature hsmSignPdf)
        {
            _hsmSignPdf = hsmSignPdf;
            _mediator = mediator;
        }

        public async Task<SignDocumentResponseModel> Handle(SignHSMDocumentCommand request,
            CancellationToken cancellationToken)
        {
            var model = request.SignDocumentRequestModel;
            Log.Information($"Ký HSM: {model.ChungThuSoId} - {model.MauChuKyId}");

            if (string.IsNullOrEmpty(model.FileBase64))
            {
                throw new ArgumentException("File ký không được để trống");
            }

            // Lấy thông tin CTS  
            var cts = await _mediator.Send(new GetChungThuSoByIdQuery(model.ChungThuSoId));
            if (cts == null)
            {
                throw new ArgumentException("Chứng thư số không tồn tại");
            }

            // Nếu không truyền cert sang thì thấy thông tin cert từ RA Service
            X509CertificateParser parser = new X509CertificateParser();
            X509Certificate[] chain;

            // Tạo certificate chain với kích thước động dựa trên số lượng certificate có sẵn
            var certificateList = new List<X509Certificate>();

            // Thêm certificate chính (bắt buộc)
            certificateList.Add(parser.ReadCertificate(Convert.FromBase64String(cts.CertificateBase64)));

            // Thêm CA certificate nếu có
            if (!string.IsNullOrEmpty(cts.CACertificateBase64))
            {
                certificateList.Add(parser.ReadCertificate(Convert.FromBase64String(cts.CACertificateBase64)));
            }

            // Thêm Root certificate nếu có
            if (!string.IsNullOrEmpty(cts.RootCertificateBase64))
            {
                certificateList.Add(parser.ReadCertificate(Convert.FromBase64String(cts.RootCertificateBase64)));
            }

            // Chuyển đổi thành mảng
            chain = certificateList.ToArray();

            // Kiểm tra cert  
            foreach (var item in chain)
            {
                // Kiểm tra thời gian hiệu lực  
                if (!item.IsValidNow)
                {
                    Log.Information($"Chứng thư số hết hiệu lực");
                    throw new ArgumentException("Chứng thư số đã hết hiệu lực");
                }
            }

            MauChuKyModel mauChuKy = null;
            if (model.Appearance.IsVisible && model.MauChuKyId == null)
            {
                throw new ArgumentException("Mẫu chữ ký không được để trống");
            }
            else
            {
                // Lấy thông tin mẫu chữ ký  
                mauChuKy = await _mediator.Send(new GetMauChuKyByIdQuery(model.MauChuKyId.Value));
                if (mauChuKy == null)
                {
                    throw new ArgumentException("Mẫu chữ ký không tồn tại");
                }
            }

            #region Thực hiện ký số

            var pdfSigned = await _hsmSignPdf.SignPDF(new SignPDFRequestModel()
            {
                Base64Pdf = model.FileBase64,
                ChungThuSo = cts,
                MauChuKy = mauChuKy,
                Appearance = model.Appearance
            });

            return new SignDocumentResponseModel()
            {
                FileBase64 = pdfSigned
            };
            
            #endregion
        }
    }
}