using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Business.Service.CheckConnectionService
{
    public class ConnectionCheckModel
    {
        public bool EnabledVault { get; set; }
        public bool EnableSwagger { get; set; }
        public bool Redis { get; set; }
        public bool RabbitMQ { get; set; }
        public bool SQLServer { get; set; }
        public bool MongoDB { get; set; }
        public bool IdentityServer { get; set; }
    }
}
