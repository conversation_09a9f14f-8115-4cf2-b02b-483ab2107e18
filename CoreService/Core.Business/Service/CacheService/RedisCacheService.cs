using Amazon.Runtime.Internal.Util;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Core.Shared;
using Serilog;
using StackExchange.Redis;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Core.Business
{
    public class RedisCacheService : ICacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly IConfiguration _config;

        // Cache expire trong 300s
        private int timeLive = 300;
        private string redisConnectionString = "";

        public RedisCacheService(IDistributedCache distributedCache, IConfiguration config)
        {
            _config = config;
            _distributedCache = distributedCache;
            int.TryParse(_config["redis:timeLive"], out timeLive);
            if (timeLive <= 0)
            {
                timeLive = 300;
            }
            redisConnectionString = _config["redis:configuration"];
        }

        public async Task<TItem> GetOrCreate<TItem>(string key, Func<Task<TItem>> createItem, DistributedCacheEntryOptions options = null)
        {
            if (_config["AppSettings:EnableCache"] == "true")
            {
                TItem cacheEntry;
                try
                {
                    var itemString = _distributedCache.GetString(key);
                    if (string.IsNullOrEmpty(itemString))
                    {
                        // Cache expire trong 300s
                        if (options == null)
                            options = new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromSeconds(timeLive));

                        cacheEntry = await createItem();
                        var item = JsonSerializer.Serialize(cacheEntry);

                        // Nạp lại giá trị mới cho cache
                        _distributedCache.SetString(key, item, options);
                        //var dataCache = _distributedCache.GetString(key);
                    }
                    else
                    {
                        cacheEntry = JsonSerializer.Deserialize<TItem>(itemString);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error($"Có lỗi xảy ra khi xử lý dữ liệu từ redis - {ex.ToString()}");
                    cacheEntry = await createItem();
                }
                return cacheEntry;
            }
            else
            {
                return await createItem();
            }
        }

        public async Task<TItem> GetOrCreate<TItem>(string key, Func<TItem> createItem, DistributedCacheEntryOptions options = null)
        {
            if (_config["AppSettings:EnableCache"] == "true")
            {
                TItem cacheEntry;
                try
                {
                    var itemString = _distributedCache.GetString(key);
                    if (string.IsNullOrEmpty(itemString))
                    {
                        // Cache expire trong 300s
                        if (options == null)
                            options = new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromSeconds(timeLive));

                        cacheEntry = createItem();
                        var item = JsonSerializer.Serialize(cacheEntry);

                        // Nạp lại giá trị mới cho cache
                        await _distributedCache.SetStringAsync(key, item, options);

                        //var dataCache = _distributedCache.GetString(key);
                    }
                    else
                    {
                        cacheEntry = JsonSerializer.Deserialize<TItem>(itemString);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error($"Có lỗi xảy ra khi xử lý dữ liệu từ redis - {ex.ToString()}");
                    cacheEntry = createItem();
                }
                return cacheEntry;
            }
            else
            {
                return createItem();
            }
        }

        public async Task Set<TItem>(string key, Func<Task<TItem>> createItem, DistributedCacheEntryOptions options = null)
        {
            if (_config["AppSettings:EnableCache"] == "true")
            {
                TItem cacheEntry;
                try
                {
                    // Cache expire trong 300s
                    if (options == null)
                        options = new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromSeconds(timeLive));

                    cacheEntry = await createItem();
                    var item = JsonSerializer.Serialize(cacheEntry);

                    // Nạp lại giá trị mới cho cache
                    _distributedCache.SetString(key, item, options);

                }
                catch (Exception ex)
                {
                    Log.Error($"Có lỗi xảy ra khi xử lý dữ liệu từ redis - {ex.ToString()}");
                }
            }
        }

        public async Task Set<TItem>(string key, Func<TItem> createItem, DistributedCacheEntryOptions options = null)
        {
            if (_config["AppSettings:EnableCache"] == "true")
            {
                TItem cacheEntry;
                try
                {
                    // Cache expire trong 300s
                    if (options == null)
                        options = new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromSeconds(timeLive));

                    cacheEntry = createItem();
                    var item = JsonSerializer.Serialize(cacheEntry);

                    // Nạp lại giá trị mới cho cache
                    _distributedCache.SetString(key, item, options);

                }
                catch (Exception ex)
                {
                    Log.Error($"Có lỗi xảy ra khi xử lý dữ liệu từ redis - {ex.ToString()}");
                }
            }
        }

        public async Task Set<TItem>(string key, TItem createItem, DistributedCacheEntryOptions options = null)
        {
            if (_config["AppSettings:EnableCache"] == "true")
            {
                try
                {
                    // Cache expire trong 300s
                    if (options == null)
                        options = new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromSeconds(timeLive));

                    var item = JsonSerializer.Serialize(createItem);

                    // Nạp lại giá trị mới cho cache
                    _distributedCache.SetString(key, item, options);

                }
                catch (Exception ex)
                {
                    Log.Error($"Có lỗi xảy ra khi xử lý dữ liệu từ redis - {ex.ToString()}");
                }
            }
        }

        public void Remove(string key)
        {
            if (_config["AppSettings:EnableCache"] == "true")
                _distributedCache.Remove(key);
        }

        public async void RemoveAll()
        {
            if (_config["AppSettings:EnableCache"] == "true")
            {
                var options = ConfigurationOptions.Parse(redisConnectionString);
                options.AllowAdmin = true;
                var redis = ConnectionMultiplexer.Connect(options);

                var endpoints = redis.GetEndPoints();
                var server = redis.GetServer(endpoints.First());
                await server.FlushDatabaseAsync();

                //If FlushDatabase didn't work: got error admin mode not enabled error
                //server.FlushDatabase();
                var keys = server.Keys();
                foreach (var key in keys)
                {
                    //Console.WriteLine("Removing Key {0} from cache", key.ToString());
                    _distributedCache.Remove(key);
                }

                //string host = _configuration.GetValue("RedisCache:Host");
                //string port = _configuration.GetValue("RedisCache:Port");
                //var iServer = _redisCache.GetServer(host, port);
                //await iServer.FlushDatabaseAsync();
            }
        }

        public void RemoveAllWithPrefix(string prefix)
        {
            if (_config["AppSettings:EnableCache"] == "true")
            {
                var options = ConfigurationOptions.Parse(redisConnectionString);
                options.AllowAdmin = true;
                var redis = ConnectionMultiplexer.Connect(options);

                var endpoints = redis.GetEndPoints();
                var server = redis.GetServer(endpoints.First());

                var keys = server.Keys();
                foreach (RedisKey key in keys)
                {
                    if (key.ToString().StartsWith(prefix))
                    {
                        //Console.WriteLine("Removing Key {0} from cache", key.ToString());
                        _distributedCache.RemoveAsync(key);
                    }
                }
            }
        }
    }
}
