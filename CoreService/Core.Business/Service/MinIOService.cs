using MimeMapping;
using Minio;
using Minio.DataModel;
using Core.Shared;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Core.Business
{
    public class MinIOService
    {
        private readonly MinioClient _minio;
        private readonly IConfiguration _config;

        //Use https instead of http
        private readonly bool _useSSL;

        //endPoint is an URL, domain name, IPv4 address or IPv6 address
        private readonly string _endpoint;

        //accessKey is like user-id that uniquely identifies your account
        private readonly string _accessKey;

        //secretKey is the password to your account
        private readonly string _secretKey;

        //default bucketname
        private readonly string _defaultBucketName;

        public MinIOService(IConfiguration config)
        {
            _config = config;

            _useSSL = _config["minio:enableSsl"] == "true";

            _endpoint = _config["minio:endpoint"];

            _accessKey = _config["minio:accesskey"];

            _secretKey = _config["minio:secretKey"];

            _defaultBucketName = _config["minio:defaultBucketName"];

            _minio = new MinioClient()
                            .WithEndpoint(_endpoint)
                            .WithCredentials(_accessKey, _secretKey);
            if (_useSSL)
                _minio.WithSSL();
            _minio.Build();
        }

        /// <summary>
        /// Uploads contents from a stream to objectName
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<MinIOFileUploadResult> UploadObjectAsync(string bucketName, string fileName, Stream data, bool isCustomSubFolder = true)
        {
            bucketName = string.IsNullOrEmpty(bucketName) ? _defaultBucketName: bucketName;
            bucketName = bucketName.ToLower();
            if (bucketName.Length < 3)
            {
                bucketName = "bn-" + bucketName;
            }
            fileName = RenameFile(fileName);
            string objectName = "";
            if (isCustomSubFolder)
            {
                var time = DateTime.Now;
                var subFolder = $"{time.Year}/{time.Month}/{time.Day}/";
                objectName = subFolder + fileName;
            }
            else
            {
                objectName = fileName;
            }

            var contentType = MimeUtility.GetMimeMapping(fileName);
            data.Position = 0;

            // Make a bucket on the server, if not already present.

            var found = await _minio.BucketExistsAsync(new BucketExistsArgs().WithBucket(bucketName));

            if (!found)
            {
                await _minio.MakeBucketAsync(new MakeBucketArgs()
                    .WithBucket(bucketName));
            }
            if (_useSSL)
            {
                //Server - side encryption with customer provided keys(SSE - C)
                Aes aesEncryption = Aes.Create();
                aesEncryption.KeySize = 256;
                aesEncryption.GenerateKey();
                var sse = new SSEC(aesEncryption.Key);

                var args = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithStreamData(data)
                    .WithObjectSize(data.Length)
                    .WithContentType(contentType)
                    .WithHeaders(null)
                    .WithServerSideEncryption(sse);
                await _minio.PutObjectAsync(args);
            }
            else
            {
                var args = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithStreamData(data)
                    .WithObjectSize(data.Length)
                    .WithContentType(contentType);
                await _minio.PutObjectAsync(args);
            }

            return new MinIOFileUploadResult
            {
                BucketName = bucketName,
                ObjectName = objectName,
                FileName = fileName
            };
        }

        /// <summary>
        ///  Downloads an object
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        public async Task<MemoryStream> DownloadObjectAsync(string bucketName, string objectName)
        {
            MemoryStream memory = new MemoryStream();
            // Check whether the object exists using statObject().
            // If the object is not found, statObject() throws an exception,
            // else it means that the object exists.
            // Execution is successful.
            bucketName = bucketName ?? _defaultBucketName;

            var args = new StatObjectArgs()
            .WithBucket(bucketName)
            .WithObject(objectName);

            await _minio.StatObjectAsync(args);

            var argsGetObj = new GetObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithCallbackStream((stream) =>
                {
                    stream.CopyTo(memory);
                });
            await _minio.GetObjectAsync(argsGetObj);
            return memory;
        }

        /// <summary>
        ///  Downloads an object and return base64
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        public async Task<string> DownloadObjectReturnBase64Async(string bucketName, string objectName)
        {
            MemoryStream memory = new MemoryStream();
            // Check whether the object exists using statObject().
            // If the object is not found, statObject() throws an exception,
            // else it means that the object exists.
            // Execution is successful.
            bucketName = bucketName ?? _defaultBucketName;
            var args = new StatObjectArgs()
           .WithBucket(bucketName)
           .WithObject(objectName);

            await _minio.StatObjectAsync(args);

            var argsGetObj = new GetObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithCallbackStream((stream) =>
                {
                    stream.CopyTo(memory);
                });
            await _minio.GetObjectAsync(argsGetObj);
            return Base64Convert.ConvertMemoryStreamToBase64(memory);
        }

        /// <summary>
        /// Generates a presigned URL for HTTP GET operations
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <param name="expiresInt"></param>
        /// <returns></returns>
        public async Task<string> GetObjectPresignUrlAsync(string bucketName, string objectName, int expiresInt = 3600)
        {
            if (expiresInt <= 0)
            {
                expiresInt = 3600;
            }
            if (expiresInt > 604800)
            {
                expiresInt = 604800;
            }
            bucketName = bucketName ?? _defaultBucketName;

            var args = new PresignedGetObjectArgs()
                  .WithBucket(bucketName)
                  .WithObject(objectName)
                  .WithHeaders(null)
                  .WithExpiry(expiresInt)
                  .WithRequestDate(null);

            string url = await _minio.PresignedGetObjectAsync(args);

            return url;
        }

        public async Task RemoveObjectAsync(string bucketName, string objectName)
        {
            bucketName = bucketName ?? _defaultBucketName;
            await _minio.RemoveObjectAsync(new RemoveObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName));
        }

        public string RenameFile(string filename)
        {
            var dateNow = DateTime.Now;
            filename = Regex.Replace(filename, @"\s", "_");
            // Generate new file name
            var fileSubfix = $"{dateNow:yyMMddHHmmss}" + new Random(DateTime.Now.Millisecond).Next(10, 99);
            var newFileName = Path.GetFileNameWithoutExtension(filename) + "_" + fileSubfix +
                              Path.GetExtension(filename);
            // Check length
            if (newFileName.Length > 255)
            {
                var withoutExtName = Path.GetFileNameWithoutExtension(filename);
                var extName = Path.GetExtension(filename);
                var trimmed = withoutExtName.Substring(0,
                    withoutExtName.Length - (255 - fileSubfix.Length - extName.Length));
                newFileName = trimmed + "_" + fileSubfix + "_" + Path.GetExtension(filename);
            }
            if (!string.IsNullOrEmpty(Path.GetDirectoryName(filename)))
            {
                return Path.GetDirectoryName(filename).Replace("\\", "/") + "/" + newFileName;
            }
            return newFileName;
        }
    }

    public class MinIOFileUploadResult
    {
        public Guid Id { get; set; }
        public string BucketName { get; set; }
        public string ObjectName { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
    }

    /// <summary>
    /// Model for MinIO file upload result (old version) - Dùng cho HRM
    /// </summary>
    public class MinIOFileUploadResultOldModel
    {
        public int Id { get; set; }
        public string BucketName { get; set; }
        public string ObjectName { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
    }
}
