using AutoMapper;
using AutoMapper.EquivalencyExpression;
using System.Collections.Generic;
namespace Core.Business
{
    public class SystemAutoMapper
    {
        private static IMapper GetMapper<TSource, TDestination>()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddCollectionMappers();
                //cfg.ValidateInlineMaps = false;
                cfg.AllowNullCollections = true;
                cfg.AllowNullDestinationValues = true;

                // config map
                #region mapper
                //cfg.CreateMap<ChuongTrinhDaoTao, CreateChuongTrinhDaoModel> ()
                //     .ForMember(dest => dest.IdDt, opt => opt.MapFrom(src => src.ID_dt))
                //     .ForMember(dest => dest.IdHe, opt => opt.MapFrom(src => src.ID_he))
                //     .ForMember(dest => dest.IdKhoa, opt => opt.MapFrom(src => src.ID_khoa))
                //     .ForMember(dest => dest.KhoaHoc, opt => opt.MapFrom(src => src.Khoa_hoc))
                //     .ForMember(dest => dest.IdNganh, opt => opt.MapFrom(src => src.ID_nganh))
                //     .ForMember(dest => dest.IdChuyenNganh, opt => opt.MapFrom(src => src.ID_chuyen_nganh))
                //     .ForMember(dest => dest.SoTinChi, opt => opt.MapFrom(src => src.So_tin_chi))
                //     .ForMember(dest => dest.SoTinChiDinhMuc, opt => opt.MapFrom(src => src.So_tin_chi_dinh_muc))
                //     .ForMember(dest => dest.SoKyHoc, opt => opt.MapFrom(src => src.So_ky_hoc))
                //     .ForMember(dest => dest.So, opt => opt.MapFrom(src => src.So))
                //     .ForMember(dest => dest.TenHe, opt => opt.MapFrom(src => src.Ten_he))
                //     .ForMember(dest => dest.TenKhoa, opt => opt.MapFrom(src => src.Ten_khoa))
                //     .ForMember(dest => dest.TenNganh, opt => opt.MapFrom(src => src.Ten_nganh))
                //     .ForMember(dest => dest.ChuyenNganh, opt => opt.MapFrom(src => src.Chuyen_nganh)).ReverseMap();

                //cfg.CreateMap<ChuongTrinhDaoTaoChiTiet, CreateChuongTrinhDaoTaoChiTietModel>()
                //     .ForMember(dest => dest.IdDt, opt => opt.MapFrom(src => src.ID_dt))
                //     .ForMember(dest => dest.IdMon, opt => opt.MapFrom(src => src.ID_mon))
                //     .ForMember(dest => dest.KyThu, opt => opt.MapFrom(src => src.Ky_thu))
                //     .ForMember(dest => dest.SoHocTrinh, opt => opt.MapFrom(src => src.So_hoc_trinh))
                //     .ForMember(dest => dest.LyThuyet, opt => opt.MapFrom(src => src.Ly_thuyet))
                //     .ForMember(dest => dest.ThucHanh, opt => opt.MapFrom(src => src.Thuc_hanh))
                //     .ForMember(dest => dest.BaiTap, opt => opt.MapFrom(src => src.Bai_tap))
                //     .ForMember(dest => dest.BaiTapLon, opt => opt.MapFrom(src => src.Bai_tap_lon))
                //     .ForMember(dest => dest.ThucTap, opt => opt.MapFrom(src => src.Thuc_tap))
                //     .ForMember(dest => dest.TuChon, opt => opt.MapFrom(src => src.Tu_chon))
                //     .ForMember(dest => dest.SttMon, opt => opt.MapFrom(src => src.STT_mon))
                //     .ForMember(dest => dest.HeSo, opt => opt.MapFrom(src => src.He_so))
                //     .ForMember(dest => dest.KienThuc, opt => opt.MapFrom(src => src.Kien_thuc))
                //     .ForMember(dest => dest.KhongTinhTBCHT, opt => opt.MapFrom(src => src.Khong_tinh_TBCHT))
                //     .ForMember(dest => dest.NhomTuChon, opt => opt.MapFrom(src => src.Nhom_tu_chon))
                //     .ForMember(dest => dest.TuHoc, opt => opt.MapFrom(src => src.Tu_hoc))
                //     .ForMember(dest => dest.SoTinChiTienQuyet, opt => opt.MapFrom(src => src.So_tin_chi_tien_quyet))
                //     .ForMember(dest => dest.MaKhoaPhuTrach, opt => opt.MapFrom(src => src.Ma_khoa_phu_trach))
                //     .ForMember(dest => dest.MonThucHanh, opt => opt.MapFrom(src => src.Mon_thuc_hanh))
                //     .ForMember(dest => dest.MonTotNghiep, opt => opt.MapFrom(src => src.Mon_tot_nghiep))
                //     .ForMember(dest => dest.MonDkTn, opt => opt.MapFrom(src => src.Mon_dk_tn)).ReverseMap();
                #endregion


            });

            IMapper mapper = new Mapper(config);
            return mapper;
        }
        private static IMapper GetMapper<TSource, TDestination>(string idString)
        {
            var config = new MapperConfiguration(cfg => {
                cfg.AddCollectionMappers();
                //cfg.ValidateInlineMaps = false;
                cfg.AllowNullCollections = true;
                cfg.AllowNullDestinationValues = true;
                cfg.CreateMap<TSource, TDestination>(MemberList.None).ForSourceMember(idString, s => s.DoNotValidate()).ForMember(idString, s => s.Ignore());
            });
            IMapper mapper = new Mapper(config);
            return mapper;
        }
      
        #region Single
        public static TDestination AutoMap<TSource, TDestination>(TSource source)
        {
            var mapper = GetMapper<TSource, TDestination>();
            TDestination dest = mapper.Map<TDestination>(source);
            return dest;
        }

        public static TDestination AutoMap<TSource, TDestination>(TSource source, string idString)
        {
            var mapper = GetMapper<TSource, TDestination>(idString);
            TDestination dest = mapper.Map<TDestination>(source);
            return dest;
        }
        public static TDestination AutoMap<TSource, TDestination>(TSource source, TDestination dest)
        {
            var mapper = GetMapper<TSource, TDestination>();
            dest = mapper.Map(source, dest);
            return dest;
        }
        
        public static TDestination AutoMap<TSource, TDestination>(TSource source, TDestination dest, string idString)
        {
            var mapper = GetMapper<TSource, TDestination>(idString);
            dest = mapper.Map(source, dest);
            return dest;
        }
        #endregion
  
        #region List
        public static List<TDestination> AutoMap<TSource, TDestination>(List<TSource> source)
        {
            var mapper = GetMapper<TSource, TDestination>();
            List<TDestination> dest = mapper.Map<List<TDestination>>(source);
            return dest;
        }

        public static List<TDestination> AutoMap<TSource, TDestination>(List<TSource> source, string idString)
        {
            var mapper = GetMapper<TSource, TDestination>(idString);
            List<TDestination> dest = mapper.Map<List<TDestination>>(source);
            return dest;
        }
        public static List<TDestination> AutoMap<TSource, TDestination>(List<TSource> source, List<TDestination> dest)
        {
            var mapper = GetMapper<TSource, TDestination>();
            dest = mapper.Map(source, dest);
            return dest;
        }

        public static List<TDestination> AutoMap<TSource, TDestination>(List<TSource> source, List<TDestination> dest, string idString)
        {
            var mapper = GetMapper<TSource, TDestination>(idString);
            dest = mapper.Map(source, dest);
            return dest;
        }
        #endregion
    }
}
