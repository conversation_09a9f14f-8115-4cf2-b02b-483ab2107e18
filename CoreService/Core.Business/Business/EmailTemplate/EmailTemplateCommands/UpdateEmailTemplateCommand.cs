using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateEmailTemplateCommand : IRequest<Unit>
    {
        public UpdateEmailTemplateModel Model { get; set; }

        /// <summary>
        /// Cập nhật mẫu email
        /// </summary>
        /// <param name="model">Thông tin mẫu email cần cập nhật</param>
        public UpdateEmailTemplateCommand(UpdateEmailTemplateModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<UpdateEmailTemplateCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(UpdateEmailTemplateCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Update {EmailTemplateConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.EmailTemplates.FirstOrDefaultAsync(x => x.Id == model.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {EmailTemplateConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);
                
                entity.ModifiedUserId = _contextAccessor.UserId;
                _dataContext.EmailTemplates.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {EmailTemplateConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(EmailTemplateConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(EmailTemplateConstant.BuildCacheKey());

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhập mẫu email mã: {entity.Code}",
                    ObjectCode = EmailTemplateConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
