using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Thêm mới nhật ký gửi mail
    /// </summary>
    /// <param name="model">Model thêm mới nhật ký gửi mail</param>
    /// <returns>Id nhật ký gửi mail</returns>
    public class SendMailLogCreateCommand : IRequest<Unit>
    {
        public SendMailLog Model { get; set; }

        public SendMailLogCreateCommand(SendMailLog model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<SendMailLogCreateCommand, Unit>
        {
            private readonly IMongoCollection<SendMailLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<SendMailLog>(MongoCollections.SendMailLog);
                }
            }

            public async Task<Unit> Handle(SendMailLogCreateCommand request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    request.Model.Id = string.Empty;
                    await _logs.InsertOneAsync(request.Model).ConfigureAwait(false);
                }
                else
                {
                    var dt = AutoMapperUtils.AutoMap<SendMailLog, SendMailLogEntity>(request.Model);
                    dt.Id = 0;
                    _dataContext.SendMailLogs.Add(dt);
                    await _dataContext.SaveChangesAsync(cancellationToken);
                }
                return Unit.Value;
            }
        }
    }
}
