using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{

    /// <summary>
    /// Cập nhật nhật ký quên mật khẩu
    /// </summary>
    /// <param name="model">Model cập nhật nhật ký quên mật khẩu</param>
    /// <returns>Id nhật ký quên mật khẩu</returns>
    public class ForgotPasswordLogUpdateCommand : IRequest<Unit>
    {
        public ForgotPasswordLog Model { get; set; }

        public ForgotPasswordLogUpdateCommand(ForgotPasswordLog model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<ForgotPasswordLogUpdateCommand, Unit>
        {
            private readonly IMongoCollection<ForgotPasswordLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<ForgotPasswordLog>(MongoCollections.ForgotPasswordLog);
                }
            }

            public async Task<Unit> Handle(ForgotPasswordLogUpdateCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    await _logs.ReplaceOneAsync(log => log.Id == model.Id, model).ConfigureAwait(false);
                }
                else
                {
                    var dt = AutoMapperUtils.AutoMap<ForgotPasswordLog, ForgotPasswordLogEntity>(model);
                    var entity = await _dataContext.ForgotPasswordLogs
                        .FirstOrDefaultAsync(x => x.Id == dt.Id, cancellationToken);
                    if (entity != null)
                    {
                        AutoMapperUtils.AutoMap(dt, entity);
                        _dataContext.ForgotPasswordLogs.Update(entity);
                        await _dataContext.SaveChangesAsync(cancellationToken);
                    }
                }
                return Unit.Value;
            }
        }
    }
}
