using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Thêm mới nhật ký quên mật khẩu
    /// </summary>
    /// <param name="model">Model thêm mới nhật ký quên mật khẩu</param>
    /// <returns>Id nhật ký quên mật khẩu</returns>
    public class ForgotPasswordLogCreateCommand : IRequest<Unit>
    {
        public ForgotPasswordLog Model { get; set; }

        public ForgotPasswordLogCreateCommand(ForgotPasswordLog model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<ForgotPasswordLogCreateCommand, Unit>
        {
            private readonly IMongoCollection<ForgotPasswordLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<ForgotPasswordLog>(MongoCollections.ForgotPasswordLog);
                }
            }

            public async Task<Unit> Handle(ForgotPasswordLogCreateCommand request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    request.Model.Id = string.Empty;
                    await _logs.InsertOneAsync(request.Model).ConfigureAwait(false);
                }
                else
                {
                    var dt = AutoMapperUtils.AutoMap<ForgotPasswordLog, ForgotPasswordLogEntity>(request.Model);
                    dt.Id = 0;
                    _dataContext.ForgotPasswordLogs.Add(dt);
                    await _dataContext.SaveChangesAsync(cancellationToken);
                }
                return Unit.Value;
            }
        }
    }
}
