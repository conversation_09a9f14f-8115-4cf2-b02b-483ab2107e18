using Core.DataLog;
using Core.Shared;
using System;
using System.Collections.Generic;

namespace Core.Business
{
    public class ForgotPasswordLogQueryFilter
    {
        public string TextSearch { get; set; }
        public string TradeId { get; set; }
        public string UserName { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public ForgotPasswordLogQueryFilter()
        {
            PageNumber = QueryFilter.DefaultPageNumber;
            PageSize = QueryFilter.DefaultPageSize;
        }
    }
}
