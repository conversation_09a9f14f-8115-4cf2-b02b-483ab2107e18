using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// L<PERSON>y danh sách nhật ký quên mật khẩu theo điều kiện lọc
    /// </summary>
    /// <param name="filter">Model điều kiện lọc</param>
    /// <returns>Danh sách nhật ký quên mật khẩu</returns>
    public class ForgotPasswordLogFilterQuery : IRequest<PaginationList<ForgotPasswordLog>>
    {
        public ForgotPasswordLogQueryFilter Filter { get; set; }

        public ForgotPasswordLogFilterQuery(ForgotPasswordLogQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<ForgotPasswordLogFilterQuery, PaginationList<ForgotPasswordLog>>
        {
            private readonly IMongoCollection<ForgotPasswordLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemReadDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemReadDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<ForgotPasswordLog>(MongoCollections.ForgotPasswordLog);
                }
            }

            public async Task<PaginationList<ForgotPasswordLog>> Handle(ForgotPasswordLogFilterQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                // set time StartDate = 0h and time EndDate = 24h
                if (filter.StartDate.HasValue && filter.EndDate.HasValue)
                {
                    filter.StartDate = filter.StartDate.Value.Date;
                    filter.EndDate = filter.EndDate.Value.AddDays(1).Date;
                }

                if (string.IsNullOrEmpty(filter.TextSearch))
                {
                    filter.TextSearch = "";
                }

                if (string.IsNullOrEmpty(filter.UserName))
                {
                    filter.UserName = "";
                }

                List<ForgotPasswordLog> listResult = new List<ForgotPasswordLog>();
                long dataCount = 0;
                long totalCount = 0;

                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    var builder = Builders<ForgotPasswordLog>.Filter.And(
                        Builders<ForgotPasswordLog>.Filter.Where(p => p.UserName.ToLower().Contains(filter.TextSearch.ToLower())
                            || string.IsNullOrEmpty(filter.TextSearch)
                        ),
                        Builders<ForgotPasswordLog>.Filter.Where(p => p.TraceId.Equals(filter.TradeId) || string.IsNullOrEmpty(filter.TradeId)),
                        Builders<ForgotPasswordLog>.Filter.Where(p => p.UserName.ToLower().Equals(filter.UserName.ToLower()) || string.IsNullOrEmpty(filter.UserName)),
                        Builders<ForgotPasswordLog>.Filter.Where(p => (filter.StartDate.HasValue && filter.EndDate.HasValue && p.CreatedDate >= filter.StartDate && p.CreatedDate < filter.EndDate)
                            || (!filter.StartDate.HasValue && !filter.EndDate.HasValue))
                    );

                    // Thêm logic để chỉ chọn các trường cần thiết
                    var projection = Builders<ForgotPasswordLog>.Projection.Include(p => p.Id)
                                                                      .Include(p => p.TraceId)
                                                                      .Include(p => p.UserId)
                                                                      .Include(p => p.UserName)
                                                                      .Include(p => p.UserEmail)
                                                                      .Include(p => p.IsUpdatedPassword)
                                                                      .Include(p => p.CreatedDate); // Chọn các trường cần thiết

                    IFindFluent<ForgotPasswordLog, ForgotPasswordLog> data = _logs.Find(builder).Project<ForgotPasswordLog>(projection).Sort(Builders<ForgotPasswordLog>.Sort.Descending(x => x.CreatedDate));

                    totalCount = await data.CountDocumentsAsync();

                    // Pagination
                    if (filter.PageSize.HasValue && filter.PageNumber.HasValue)
                    {
                        if (filter.PageSize <= 0)
                        {
                            filter.PageSize = QueryFilter.DefaultPageSize;
                        }

                        //Calculate nunber of rows to skip on pagesize
                        int excludedRows = (filter.PageNumber.Value - 1) * (filter.PageSize.Value);
                        if (excludedRows <= 0)
                        {
                            excludedRows = 0;
                        }

                        // Query
                        data = data.Skip(excludedRows).Limit(filter.PageSize.Value);
                    }
                    dataCount = await data.CountDocumentsAsync();

                    listResult = await data.ToListAsync();
                }
                // Nếu không có cấu hình MongoDB thì lấy từ SQL
                else
                {
                    var data = (from dt in _dataContext.ForgotPasswordLogs.AsNoTracking()
                                select dt);

                    if (!string.IsNullOrEmpty(filter.TextSearch))
                    {
                        string ts = filter.TextSearch.Trim().ToLower();
                        data = data.Where(x => x.UserName.ToLower().Contains(ts)
                            || x.TraceId.ToLower().Contains(ts)
                            || x.UserName.ToLower().Contains(ts));
                    }

                    data = data.OrderByDescending(x => x.Id);

                    // Apply pagination with improved performance
                    if (filter.PageSize <= 0)
                    {
                        filter.PageSize = QueryFilter.DefaultPageSize;
                    }

                    // Get total count asynchronously
                    totalCount = await data.CountAsync();

                    // Calculate number of rows to skip on pagesize
                    int excludedRows = (filter.PageNumber.Value - 1) * filter.PageSize.Value;
                    if (excludedRows <= 0)
                    {
                        excludedRows = 0;
                    }

                    // Apply pagination and get data asynchronously
                    var listData = await data
                        .Skip(excludedRows)
                        .Take(filter.PageSize.Value)
                        .ToListAsync();

                    listResult = AutoMapperUtils.AutoMap<ForgotPasswordLogEntity, ForgotPasswordLog>(listData);
                    dataCount = listResult.Count;
                }

                return new PaginationList<ForgotPasswordLog>()
                {
                    DataCount = (int)dataCount,
                    TotalCount = (int)totalCount,
                    PageNumber = filter.PageNumber ?? 0,
                    PageSize = filter.PageSize ?? 0,
                    Data = listResult
                };

            }
        }
    }
}
