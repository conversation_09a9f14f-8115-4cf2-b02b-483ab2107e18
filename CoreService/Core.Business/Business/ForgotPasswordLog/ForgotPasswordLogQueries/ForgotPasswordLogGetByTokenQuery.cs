using Core.Data;
using Core.DataLog;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Lấy nhật ký quên mật khẩu theo token và chưa thực hiện cập nhật mật khẩu
    /// </summary>
    /// <param name="token">Token nhật ký quên mật khẩu</param>
    /// <returns>Thông tin nhật ký quên mật khẩu</returns>
    public class ForgotPasswordLogGetByTokenQuery : IRequest<ForgotPasswordLog>
    {
        public string Token { get; set; }

        public ForgotPasswordLogGetByTokenQuery(string token)
        {
            Token = token;
        }

        public class Handler : IRequestHandler<ForgotPasswordLogGetByTokenQuery, ForgotPasswordLog>
        {
            private readonly IMongoCollection<ForgotPasswordLog> _logs;
            private readonly IMongoDBDatabaseSettings _settings;
            private readonly SystemReadDataContext _dataContext;

            public Handler(IMongoDBDatabaseSettings settings, SystemReadDataContext dataContext)
            {
                _settings = settings;
                _dataContext = dataContext;
                if (!string.IsNullOrEmpty(settings.ConnectionString))
                {
                    var client = new MongoClient(settings.ConnectionString);
                    var database = client.GetDatabase(settings.DatabaseName);

                    _logs = database.GetCollection<ForgotPasswordLog>(MongoCollections.ForgotPasswordLog);
                }
            }

            public async Task<ForgotPasswordLog> Handle(ForgotPasswordLogGetByTokenQuery request, CancellationToken cancellationToken)
            {
                // Có sử dụng MongoDB
                if (!string.IsNullOrEmpty(_settings.ConnectionString))
                {
                    return await _logs.Find(log => log.Token == request.Token && !log.IsUpdatedPassword).FirstOrDefaultAsync();
                }
                else
                {
                    var dt = await _dataContext.ForgotPasswordLogs.AsNoTracking().FirstOrDefaultAsync(x => x.Token == request.Token && !x.IsUpdatedPassword);
                    return AutoMapperUtils.AutoMap<ForgotPasswordLogEntity, ForgotPasswordLog>(dt);
                }
            }
        }
    }
}
