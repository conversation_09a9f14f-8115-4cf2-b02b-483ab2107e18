//using MediatR;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.Extensions.Localization;
//using Core.Data;
//using Core.Shared;
//using Serilog;
//using System;
//using System.Text.Json;
//using System.Threading;
//using System.Threading.Tasks;

//namespace Core.Business
//{
//    public class CreateTKBGiaoVienCommand : IRequest<Unit>
//    {
//        public CreateTKBGiaoVienModel Model { get; set; }
//        public SystemLogModel SystemLog { get; set; }

//        /// <summary>
//        /// Thêm mới tài khoản giáo viên
//        /// </summary>
//        /// <param name="model">Thông tin tài khoản giáo viên cần thêm mới</param>
//        /// <param name="systemLog">Thông tin lưu log</param>
//        public CreateTKBGiaoVienCommand(CreateTKBGiaoVienModel model, SystemLogModel systemLog)
//        {
//            Model = model;
//            SystemLog = systemLog;
//        }

//        public class Handler : IRequestHandler<CreateTKBGiaoVienCommand, Unit>
//        {
//            private readonly SystemDataContext _dataContext;
//            private readonly ICacheService _cacheService;
//            private readonly IStringLocalizer<Resources> _localizer;

//            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
//            {
//                _dataContext = dataContext;
//                _cacheService = cacheService;
//                _localizer = localizer;
//            }

//            public async Task<Unit> Handle(CreateTKBGiaoVienCommand request, CancellationToken cancellationToken)
//            {
//                var model = request.Model;
//                var systemLog = request.SystemLog;
//                Log.Information($"Create {TKBGiaoVienConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

//                var entity = AutoMapperUtils.AutoMap<CreateTKBGiaoVienModel, TKBGiaoVien>(model);

//                var checkCode = await _dataContext.TKBGiaoViens.AnyAsync(x => x.Code == entity.Code);
//                if (checkCode)
//                {
//                    throw new ArgumentException($"{_localizer["system-application.code.existed"]}");
//                }

//                entity.CreatedDate = DateTime.Now;

//                await _dataContext.TKBGiaoViens.AddAsync(entity);
//                await _dataContext.SaveChangesAsync();

//                Log.Information($"Create {TKBGiaoVienConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
//                systemLog.ListAction.Add(new ActionDetail()
//                {
//                    Description = $"Thêm mới tài khoản giáo viên mã: {entity.Code}",
//                    ObjectCode = TKBGiaoVienConstant.CachePrefix,
//                    ObjectId = entity.Id.ToString()
//                });

//                //Xóa cache
//                _cacheService.Remove(TKBGiaoVienConstant.BuildCacheKey());

//                return Unit.Value;
//            }
//        }
//    }

//    public class UpdateTKBGiaoVienCommand : IRequest<Unit>
//    {
//        public UpdateTKBGiaoVienModel Model { get; set; }
//        public SystemLogModel SystemLog { get; set; }

//        /// <summary>
//        /// Cập nhật tài khoản giáo viên
//        /// </summary>
//        /// <param name="model">Thông tin tài khoản giáo viên cần cập nhật</param>
//        /// <param name="systemLog">Thông tin lưu log</param>
//        public UpdateTKBGiaoVienCommand(UpdateTKBGiaoVienModel model, SystemLogModel systemLog)
//        {
//            Model = model;
//            SystemLog = systemLog;
//        }

//        public class Handler : IRequestHandler<UpdateTKBGiaoVienCommand, Unit>
//        {
//            private readonly SystemDataContext _dataContext;
//            private readonly ICacheService _cacheService;
//            private readonly IStringLocalizer<Resources> _localizer;

//            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
//            {
//                _dataContext = dataContext;
//                _cacheService = cacheService;
//                _localizer = localizer;
//            }

//            public async Task<Unit> Handle(UpdateTKBGiaoVienCommand request, CancellationToken cancellationToken)
//            {
//                var model = request.Model;
//                var systemLog = request.SystemLog;
//                Log.Information($"Update {TKBGiaoVienConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

//                var entity = await _dataContext.TKBGiaoViens.FirstOrDefaultAsync(x => x.Id == model.Id);
//                if (entity == null)
//                {
//                    throw new ArgumentException($"{_localizer["data.not-found"]}");
//                }
//                Log.Information($"Before Update {TKBGiaoVienConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

//                model.UpdateEntity(entity);

//                _dataContext.TKBGiaoViens.Update(entity);
//                await _dataContext.SaveChangesAsync();

//                Log.Information($"After Update {TKBGiaoVienConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

//                //Xóa cache
//                _cacheService.Remove(TKBGiaoVienConstant.BuildCacheKey(entity.Id.ToString()));
//                _cacheService.Remove(TKBGiaoVienConstant.BuildCacheKey());

//                systemLog.ListAction.Add(new ActionDetail()
//                {
//                    Description = $"Cập nhập tài khoản giáo viên mã: {entity.Code}",
//                    ObjectCode = TKBGiaoVienConstant.CachePrefix,
//                    ObjectId = entity.Id.ToString()
//                });

//                return Unit.Value;
//            }
//        }
//    }

//    public class DeleteTKBGiaoVienCommand : IRequest<Unit>
//    {
//        public int Id { get; set; }
//        public SystemLogModel SystemLog { get; set; }

//        /// <summary>
//        /// Xóa tài khoản giáo viên theo danh sách truyền vào
//        /// </summary>
//        /// <param name="id">Id tài khoản giáo viên cần xóa</param>
//        /// <param name="systemLog">Thông tin lưu log</param>
//        public DeleteTKBGiaoVienCommand(int id, SystemLogModel systemLog)
//        {
//            Id = id;
//            SystemLog = systemLog;
//        }

//        public class Handler : IRequestHandler<DeleteTKBGiaoVienCommand, Unit>
//        {
//            private readonly SystemDataContext _dataContext;
//            private readonly ICacheService _cacheService;
//            private readonly IStringLocalizer<Resources> _localizer;

//            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer)
//            {
//                _dataContext = dataContext;
//                _cacheService = cacheService;
//                _localizer = localizer;
//            }

//            public async Task<Unit> Handle(DeleteTKBGiaoVienCommand request, CancellationToken cancellationToken)
//            {
//                var id = request.Id;
//                var systemLog = request.SystemLog;
//                Log.Information($"Delete {TKBGiaoVienConstant.CachePrefix}: {id}");

//                var dt = await _dataContext.TKBGiaoViens.FirstOrDefaultAsync(x => x.Id == id);

//                if (dt == null)
//                {
//                    throw new ArgumentException($"{_localizer["data.not-found"]}");
//                }
//                _dataContext.TKBGiaoViens.Remove(dt);

//                systemLog.ListAction.Add(new ActionDetail()
//                {
//                    Description = $"Xóa tài khoản giáo viên mã: {dt.Code}",
//                    ObjectCode = TKBGiaoVienConstant.CachePrefix
//                });
//                await _dataContext.SaveChangesAsync();

//                //Xóa cache
//                _cacheService.Remove(TKBGiaoVienConstant.BuildCacheKey(id.ToString()));
//                _cacheService.Remove(TKBGiaoVienConstant.BuildCacheKey());
                
//                Log.Information($"Delete {TKBGiaoVienConstant.CachePrefix} completed");

//                return Unit.Value;
//            }
//        }
//    }
//}