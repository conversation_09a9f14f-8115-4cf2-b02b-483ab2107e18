using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentEmail.Core;
using FluentEmail.Core.Models;
using Serilog;
using System.Text.Json;
using Minio.DataModel;

namespace Core.Business
{
    public class GetTKBGiaoVienByIdQuery : IRequest<TKBGiaoVienModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin tài khoản giáo viên theo id
        /// </summary>
        /// <param name="id">Id tài khoản giáo viên</param>
        public GetTKBGiaoVienByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetTKBGiaoVienByIdQuery, TKBGiaoVienModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<TKBGiaoVienModel> Handle(GetTKBGiaoVienByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = TKBGiaoVienConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.TKBGiaoViens.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);

                    return AutoMapperUtils.AutoMap<TKBGiaoVien, TKBGiaoVienModel>(entity);
                });
                return item;
            }
        }
    }

    public class GetFilterTKBGiaoVienQuery : IRequest<PaginationList<TKBGiaoVienBaseModel>>
    {
        public TKBGiaoVienQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tài khoản giáo viên theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterTKBGiaoVienQuery(TKBGiaoVienQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterTKBGiaoVienQuery, PaginationList<TKBGiaoVienBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<TKBGiaoVienBaseModel>> Handle(GetFilterTKBGiaoVienQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.TKBGiaoViens.AsNoTracking()
                            select dt);

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.Ten.ToLower().Contains(ts) || x.HoTen.ToLower().Contains(ts));
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                int totalCount = data.Count();

                // Pagination
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                //Calculate nunber of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * (filter.PageSize);
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Query
                data = data.Skip(excludedRows).Take(filter.PageSize);
                int dataCount = data.Count();

                var listData = await data.ToListAsync();

                var listResult = AutoMapperUtils.AutoMap<TKBGiaoVien, TKBGiaoVienBaseModel>(listData);

                return new PaginationList<TKBGiaoVienBaseModel>()
                {
                    DataCount = dataCount,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listResult
                };
            }
        }
    }

    public class GetComboboxTKBGiaoVienQuery : IRequest<List<TKBGiaoVienSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách tài khoản giáo viên cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxTKBGiaoVienQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxTKBGiaoVienQuery, List<TKBGiaoVienSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<TKBGiaoVienSelectItemModel>> Handle(GetComboboxTKBGiaoVienQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = TKBGiaoVienConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.TKBGiaoViens.OrderBy(x => x.Id).AsNoTracking()
                                select new TKBGiaoVienSelectItemModel()
                                {
                                    Id = item.Id,
                                    MaCB = item.MaCB,
                                    HoTen = item.HoTen,
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.Name.ToLower().Contains(textSearch) || x.Note.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }

    public class GetListGiaoVienQuery : IRequest<List<GiaoVienModel>>
    {

        /// <summary>
        /// Lấy danh sách thông tin giáo viên
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetListGiaoVienQuery()
        {
        }

        public class Handler : IRequestHandler<GetListGiaoVienQuery, List<GiaoVienModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<List<GiaoVienModel>> Handle(GetListGiaoVienQuery request, CancellationToken cancellationToken)
            {
                var data = (from gv in _dataContext.TKBGiaoViens
                            join bmgv in _dataContext.TkbBoMonGiangViens on gv.Id equals bmgv.IdCb into bmgvGroup
                            from bmgv in bmgvGroup.DefaultIfEmpty()
                            join bm in _dataContext.TkbBoMons on bmgv.IdBm equals bm.IdBoMon into bmGroup
                            from bm in bmGroup.DefaultIfEmpty()
                            join cd in _dataContext.TkbChucDanhs on gv.ChucDanhID equals cd.IdChucDanh into cdGroup
                            from cd in cdGroup.DefaultIfEmpty()
                            join hv in _dataContext.TkbHocVis on gv.HocViID equals hv.IdHocVi into hvGroup
                            from hv in hvGroup.DefaultIfEmpty()
                            select new GiaoVienModel()
                            {
                                Id = gv.Id,
                                MaCB = gv.MaCB,
                                HoTen = gv.HoTen,
                                SoDienThoai = gv.SoDienThoai,
                                IdKhoa = gv.KhoaID,
                                IdBoMon = bm != null ? bm.IdBoMon : 0,
                                BoMon = bm != null ? bm.BoMon : "",
                                IdChucDanh = cd != null ? cd.IdChucDanh : 0,
                                ChucDanh = cd != null ? cd.ChucDanh : "",
                                IdHocVi = hv != null ? hv.IdHocVi : 0,
                                HocVi = hv != null ? hv.HocVi : ""
                            });

                return await data.ToListAsync();
            }
        }
    }
}
