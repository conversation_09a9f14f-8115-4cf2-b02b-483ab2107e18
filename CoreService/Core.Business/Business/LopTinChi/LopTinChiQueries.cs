using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxLopTinChiQuery : IRequest<List<LopTinChiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// L<PERSON>y danh sách lớp tín chỉ của sinh viên cho combobox
        /// </summary>
        /// <param name="count">S<PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxLopTinChiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxLopTinChiQuery, List<LopTinChiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<LopTinChiSelectItemModel>> Handle(GetComboboxLopTinChiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = LopTinChiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.TkbLopTinChis.OrderBy(x => x.SttLop)
                                select new LopTinChiSelectItemModel()
                                {
                                    Id = item.IdLopLt,
                                    Code = item.CaHoc.ToString(),
                                    Name = item.CaHoc.ToString()
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    textSearch = textSearch.ToLower().Trim();
                    list = list.Where(x => x.Name.ToLower().Contains(textSearch) || x.Note.ToLower().Contains(textSearch)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
