using Core.Data.Signing;
using Core.Shared;
using Core.Shared.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class MauChuKyBaseModel
    {
        public int Id { get; set; }

        public int UserId { get; set; }

        [Required(ErrorMessage = "mau-chu-ky.code.required")]
        [MaxLength(50, ErrorMessage = "mau-chu-ky.code.max-length")]
        public string Code { get; set; }

        [Required(ErrorMessage = "mau-chu-ky.name.required")]
        [MaxLength(100, ErrorMessage = "mau-chu-ky.name.max-length")]
        public string Name { get; set; }

        public short LoaiKySuDung { get; set; }

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }

        public DateTime? CreatedDate { get; set; }
    }

    public class MauChuKyModel : MauChuKyBaseModel
    {
        public string ImageBase64 { get; set; }

        public CauHinhHienThiChuKyModel CauHinhHienThiChuKy { get; set; }
    }

    public class CreateMauChuKyModel : MauChuKyModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateMauChuKyModel : MauChuKyModel
    {
        public int? ModifiedUserId { get; set; }

        public void UpdateEntity(SgMauChuKy entity)
        {
            entity.UserId = this.UserId;
            entity.Code = this.Code;
            entity.Name = this.Name;
            entity.ImageBase64 = this.ImageBase64;
            entity.CauHinhHienThiChuKy = this.CauHinhHienThiChuKy;
            entity.LoaiKySuDung = this.LoaiKySuDung;
            entity.IsActive = this.IsActive;
            entity.Order = this.Order;
            entity.ModifiedDate = DateTime.Now;
            entity.ModifiedUserId = this.ModifiedUserId;
        }
    }

    public class MauChuKySelectItemModel : SelectItemModel
    {
        public int UserId { get; set; }
        public short LoaiKySuDung { get; set; }
        public string LoaiKySuDungText { get; set; }
    }

    public class MauChuKyQueryFilter : BaseQueryFilterModel
    {
        public int? UserId { get; set; }
        public short? LoaiKySuDung { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
    }
}
