using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetFilterMauChuKyQuery : IRequest<PaginationList<MauChuKyBaseModel>>
    {
        public MauChuKyQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách mẫu chữ ký theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterMauChuKyQuery(MauChuKyQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterMauChuKyQuery, PaginationList<MauChuKyBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<MauChuKyBaseModel>> Handle(GetFilterMauChuKyQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SgMauChuKys.AsNoTracking()
                            select dt);

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.Code.ToLower().Contains(ts) || 
                                          x.Name.ToLower().Contains(ts));
                }

                if (filter.IsActive.HasValue)
                {
                    data = data.Where(x => x.IsActive == filter.IsActive);
                }

                if (filter.UserId.HasValue)
                {
                    data = data.Where(x => x.UserId == filter.UserId);
                }

                if (filter.LoaiKySuDung.HasValue)
                {
                    data = data.Where(x => x.LoaiKySuDung == filter.LoaiKySuDung);
                }

                if (filter.CreatedDateFrom.HasValue)
                {
                    data = data.Where(x => x.CreatedDate >= filter.CreatedDateFrom);
                }

                if (filter.CreatedDateTo.HasValue)
                {
                    data = data.Where(x => x.CreatedDate <= filter.CreatedDateTo);
                }

                var orderedData = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await orderedData.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await orderedData
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .Select(x => new MauChuKyBaseModel
                    {
                        Id = x.Id,
                        UserId = x.UserId,
                        Code = x.Code,
                        Name = x.Name,
                        LoaiKySuDung = x.LoaiKySuDung,
                        IsActive = x.IsActive,
                        Order = x.Order,
                        CreatedDate = x.CreatedDate,
                    })
                    .ToListAsync();

                return new PaginationList<MauChuKyBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }
}
