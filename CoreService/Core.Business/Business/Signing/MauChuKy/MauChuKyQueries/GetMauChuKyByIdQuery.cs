using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetMauChuKyByIdQuery : IRequest<MauChuKyModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin mẫu chữ ký theo Id
        /// </summary>
        /// <param name="id">Id mẫu chữ ký</param>
        public GetMauChuKyByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetMauChuKyByIdQuery, MauChuKyModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<MauChuKyModel> Handle(GetMauChuKyByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = MauChuKyConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SgMauChuKys.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);

                    return AutoMapperUtils.AutoMap<SgMauChuKy, MauChuKyModel>(entity);
                });
                return item;
            }
        }
    }
}
