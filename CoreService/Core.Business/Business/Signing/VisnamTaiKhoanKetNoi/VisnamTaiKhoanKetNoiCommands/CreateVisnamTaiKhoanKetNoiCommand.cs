using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateVisnamTaiKhoanKetNoiCommand : IRequest<Unit>
    {
        public CreateVisnamTaiKhoanKetNoiModel Model { get; set; }

        /// <summary>
        /// Thêm mới tài khoản kết nối Visnam
        /// </summary>
        /// <param name="model">Thông tin tài khoản kết nối Visnam cần thêm mới</param>
        public CreateVisnamTaiKhoanKetNoiCommand(CreateVisnamTaiKhoanKetNoiModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateVisnamTaiKhoanKetNoiCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(CreateVisnamTaiKhoanKetNoiCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create {VisnamTaiKhoanKetNoiConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateVisnamTaiKhoanKetNoiModel, SgVisnamTaiKhoanKetNoi>(model);

                entity.Secret = AesEncryption.Encrypt(entity.Secret, AesEncryption.KeyDefault);
                
                entity.CreatedUserId = _contextAccessor.UserId;
                entity.CreatedDate = DateTime.Now;

                await _dataContext.SgVisnamTaiKhoanKetNois.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {VisnamTaiKhoanKetNoiConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tài khoản kết nối Visnam cho User ID: {entity.UserId}",
                    ObjectCode = VisnamTaiKhoanKetNoiConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(VisnamTaiKhoanKetNoiConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
