using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetFilterVisnamTaiKhoanKetNoiQuery : IRequest<PaginationList<VisnamTaiKhoanKetNoiBaseModel>>
    {
        public VisnamTaiKhoanKetNoiQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tài khoản kết nối Visnam theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterVisnamTaiKhoanKetNoiQuery(VisnamTaiKhoanKetNoiQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterVisnamTaiKhoanKetNoiQuery,
            PaginationList<VisnamTaiKhoanKetNoiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<VisnamTaiKhoanKetNoiBaseModel>> Handle(
                GetFilterVisnamTaiKhoanKetNoiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SgVisnamTaiKhoanKetNois.AsNoTracking()
                    select dt);

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.Key.ToLower().Contains(ts));
                }

                if (filter.IsActive.HasValue)
                {
                    data = data.Where(x => x.IsActive == filter.IsActive);
                }

                if (filter.UserId.HasValue)
                {
                    data = data.Where(x => x.UserId == filter.UserId);
                }

                if (filter.CreatedDateFrom.HasValue)
                {
                    data = data.Where(x => x.CreatedDate >= filter.CreatedDateFrom);
                }

                if (filter.CreatedDateTo.HasValue)
                {
                    data = data.Where(x => x.CreatedDate <= filter.CreatedDateTo);
                }

                var orderedData = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await orderedData.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await orderedData
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .Select(x => new VisnamTaiKhoanKetNoiBaseModel
                    {
                        Id = x.Id,
                        UserId = x.UserId,
                        Key = x.Key,
                        Secret = "**********", // Mask secret để bảo mật
                        IsActive = x.IsActive,
                        Order = x.Order,
                        CreatedDate = x.CreatedDate,
                    })
                    .ToListAsync();

                return new PaginationList<VisnamTaiKhoanKetNoiBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }
        }
    }
}