using Core.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxVisnamTaiKhoanKetNoiQuery : IRequest<List<VisnamTaiKhoanKetNoiSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        /// <summary>
        /// Lấy danh sách tài khoản kết nối Visnam cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxVisnamTaiKhoanKetNoiQuery(int count = 0, string textSearch = "")
        {
            this.Count = count;
            this.TextSearch = textSearch;
        }

        public class Handler : IRequestHandler<GetComboboxVisnamTaiKhoanKetNoiQuery, List<VisnamTaiKhoanKetNoiSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<VisnamTaiKhoanKetNoiSelectItemModel>> Handle(GetComboboxVisnamTaiKhoanKetNoiQuery request, CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;

                string cacheKey = VisnamTaiKhoanKetNoiConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.SgVisnamTaiKhoanKetNois.AsNoTracking().Where(x => x.IsActive)
                                orderby item.Order, item.Id
                                select new VisnamTaiKhoanKetNoiSelectItemModel()
                                {
                                    Id = item.Id,
                                    Code = item.Key,
                                    UserId = item.UserId,
                                    Key = item.Key
                                });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    string ts = textSearch.Trim().ToLower();
                    list = list.Where(x => x.Key.ToLower().Contains(ts) || 
                                          x.UserName.ToLower().Contains(ts)).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}
