using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetFilterChungThuSoQuery : IRequest<PaginationList<ChungThuSoBaseModel>>
    {
        public ChungThuSoQueryFilter Filter { get; set; }

        /// <summary>
        /// L<PERSON>y danh sách chứng thư số theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterChungThuSoQuery(ChungThuSoQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterChungThuSoQuery, PaginationList<ChungThuSoBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<ChungThuSoBaseModel>> Handle(GetFilterChungThuSoQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SgChungThuSos.AsNoTracking()
                            select dt);

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.SerialNumber.ToLower().Contains(ts) || 
                                          x.SubjectName.ToLower().Contains(ts) ||
                                          x.Issuer.ToLower().Contains(ts));
                }

                if (filter.IsActive.HasValue)
                {
                    data = data.Where(x => x.IsActive == filter.IsActive);
                }

                if (filter.UserId.HasValue)
                {
                    data = data.Where(x => x.UserId == filter.UserId);
                }

                if (filter.Source.HasValue)
                {
                    data = data.Where(x => x.Source == filter.Source);
                }

                if (filter.NotBeforeFrom.HasValue)
                {
                    data = data.Where(x => x.NotBefore >= filter.NotBeforeFrom);
                }

                if (filter.NotBeforeTo.HasValue)
                {
                    data = data.Where(x => x.NotBefore <= filter.NotBeforeTo);
                }

                if (filter.NotAfterFrom.HasValue)
                {
                    data = data.Where(x => x.NotAfter >= filter.NotAfterFrom);
                }

                if (filter.NotAfterTo.HasValue)
                {
                    data = data.Where(x => x.NotAfter <= filter.NotAfterTo);
                }

                if (filter.IsExpired.HasValue)
                {
                    var now = DateTime.Now;
                    if (filter.IsExpired.Value)
                    {
                        data = data.Where(x => x.NotAfter < now);
                    }
                    else
                    {
                        data = data.Where(x => x.NotAfter >= now);
                    }
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                var listResult = AutoMapperUtils.AutoMap<SgChungThuSo, ChungThuSoBaseModel>(listData);

                return new PaginationList<ChungThuSoBaseModel>()
                {
                    DataCount = listResult.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listResult
                };
            }
        }
    }
}
