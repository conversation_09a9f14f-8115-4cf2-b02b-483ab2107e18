using Core.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxChungThuSoQuery : IRequest<List<ChungThuSoSelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }

        public int? UserId { get; set; }

        /// <summary>
        /// L<PERSON>y danh sách chứng thư số cho combobox
        /// </summary>
        /// <param name="count"><PERSON><PERSON> lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        /// <param name="userId">ID người dùng</param>
        public GetComboboxChungThuSoQuery(int count = 0, string textSearch = "", int? userId = null)
        {
            this.Count = count;
            this.TextSearch = textSearch;
            this.UserId = userId;
        }

        public class Handler : IRequestHandler<GetComboboxChungThuSoQuery, List<ChungThuSoSelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<ChungThuSoSelectItemModel>> Handle(GetComboboxChungThuSoQuery request,
                CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                var userId = request.UserId;

                string cacheKey = ChungThuSoConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.SgChungThuSos.AsNoTracking().Where(x => x.IsActive)
                            .OrderBy(x => x.Order).ThenBy(x => x.SerialNumber)
                        select new ChungThuSoSelectItemModel()
                        {
                            Id = item.Id,
                            UserId = item.UserId,
                            Code = item.SerialNumber,
                            Name = item.SubjectName,
                            SerialNumber = item.SerialNumber,
                            SubjectName = item.SubjectName,
                            NotBefore = item.NotBefore,
                            NotAfter = item.NotAfter,
                            Source = item.Source
                        });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    string ts = textSearch.Trim().ToLower();
                    list = list.Where(x => x.SerialNumber.ToLower().Contains(ts) ||
                                           x.SubjectName.ToLower().Contains(ts)).ToList();
                }

                // Nếu có userId thì lọc ra các chứng thư số của người dùng đó và còn hiệu lực
                if (userId.HasValue)
                {
                    list = list.Where(x =>
                        x.UserId == userId.Value && x.NotAfter >= DateTime.Now && x.NotBefore <= DateTime.Now).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }
        }
    }
}