using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateChungThuSoCommand : IRequest<Unit>
    {
        public CreateChungThuSoModel Model { get; set; }

        /// <summary>
        /// Thêm mới chứng thư số
        /// </summary>
        /// <param name="model">Thông tin chứng thư số cần thêm mới</param>
        public CreateChungThuSoCommand(CreateChungThuSoModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateChungThuSoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(CreateChungThuSoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create {ChungThuSoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateChungThuSoModel, SgChungThuSo>(model);

                var checkSerialNumber = await _dataContext.SgChungThuSos.AnyAsync(x => x.SerialNumber == entity.SerialNumber);
                if (checkSerialNumber)
                {
                    throw new ArgumentException($"{_localizer["chung-thu-so.serial-number.existed"]}");
                }

                entity.CreatedUserId = _contextAccessor.UserId;
                entity.CreatedDate = DateTime.Now;

                await _dataContext.SgChungThuSos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ChungThuSoConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới chứng thư số serial: {entity.SerialNumber}",
                    ObjectCode = ChungThuSoConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(ChungThuSoConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
