using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateManyChungThuSoCommand : IRequest<CreateManyChungThuSoResult>
    {
        public CreateManyChungThuSoModel Model { get; set; }

        /// <summary>
        /// Thêm mới nhiều chứng thư số cùng lúc
        /// </summary>
        /// <param name="model">Danh sách thông tin chứng thư số cần thêm mới</param>
        public CreateManyChungThuSoCommand(CreateManyChungThuSoModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateManyChungThuSoCommand, CreateManyChungThuSoResult>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<CreateManyChungThuSoResult> Handle(CreateManyChungThuSoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                var result = new CreateManyChungThuSoResult();

                Log.Information($"Create Many {ChungThuSoConstant.CachePrefix}: {model.ChungThuSos.Count} items");

                // Validation: Kiểm tra danh sách không rỗng
                if (model.ChungThuSos == null || !model.ChungThuSos.Any())
                {
                    throw new ArgumentException($"{_localizer["chung-thu-so.list.required"]}");
                }

                // Validation: Validate từng item trong danh sách
                var validationErrors = new List<string>();
                for (int i = 0; i < model.ChungThuSos.Count; i++)
                {
                    var item = model.ChungThuSos[i];
                    var validationContext = new ValidationContext(item);
                    var validationResults = new List<ValidationResult>();
                    
                    if (!Validator.TryValidateObject(item, validationContext, validationResults, true))
                    {
                        foreach (var validationResult in validationResults)
                        {
                            validationErrors.Add($"Item {i + 1}: {validationResult.ErrorMessage}");
                        }
                    }
                }

                if (validationErrors.Any())
                {
                    throw new ArgumentException($"Validation errors: {string.Join("; ", validationErrors)}");
                }

                // Validation: Kiểm tra trùng lặp SerialNumber trong danh sách input
                var duplicateSerialNumbers = model.ChungThuSos
                    .GroupBy(x => x.SerialNumber)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key)
                    .ToList();

                if (duplicateSerialNumbers.Any())
                {
                    throw new ArgumentException($"Duplicate SerialNumbers in input: {string.Join(", ", duplicateSerialNumbers)}");
                }

                // Validation: Kiểm tra SerialNumber đã tồn tại trong database
                var inputSerialNumbers = model.ChungThuSos.Select(x => x.SerialNumber).ToList();
                var existingSerialNumbers = await _dataContext.SgChungThuSos
                    .Where(x => inputSerialNumbers.Contains(x.SerialNumber))
                    .Select(x => x.SerialNumber)
                    .ToListAsync();

                if (existingSerialNumbers.Any())
                {
                    // Bỏ qua các SerialNumber đã tồn tại
                    model.ChungThuSos = model.ChungThuSos.Where(x => !existingSerialNumbers.Contains(x.SerialNumber)).ToList();
                }

                // Sử dụng transaction để đảm bảo tính toàn vẹn dữ liệu
                using var transaction = await _dataContext.Database.BeginTransactionAsync();
                try
                {
                    var entities = new List<SgChungThuSo>();
                    var currentTime = DateTime.Now;
                    var userId = _contextAccessor.UserId;

                    // Chuyển đổi models thành entities
                    foreach (var item in model.ChungThuSos)
                    {
                        var entity = AutoMapperUtils.AutoMap<CreateChungThuSoModel, SgChungThuSo>(item);
                        entity.CreatedUserId = userId;
                        entity.CreatedDate = currentTime;
                        entities.Add(entity);
                    }

                    // Bulk insert để tối ưu performance
                    await _dataContext.SgChungThuSos.AddRangeAsync(entities);
                    await _dataContext.SaveChangesAsync();

                    // Commit transaction
                    await transaction.CommitAsync();

                    result.SuccessCount = entities.Count;
                    result.CreatedIds = entities.Select(x => x.Id).ToList();

                    Log.Information($"Create Many {ChungThuSoConstant.CachePrefix} success: {result.SuccessCount} items created");

                    // Audit Trail: Ghi log SystemLog với thông tin tổng hợp
                    _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Thêm mới {result.SuccessCount} chứng thư số",
                        ObjectCode = ChungThuSoConstant.CachePrefix,
                        ObjectId = string.Join(",", result.CreatedIds)
                    });

                    // Cache Management: Clear cache sau khi insert thành công
                    _cacheService.Remove(ChungThuSoConstant.BuildCacheKey());

                    return result;
                }
                catch (Exception ex)
                {
                    // Rollback transaction nếu có lỗi
                    await transaction.RollbackAsync();
                    
                    // Log lỗi với đầy đủ context
                    Log.Error(ex, $"Error creating many {ChungThuSoConstant.CachePrefix}: {ex.Message}. Input: {JsonSerializer.Serialize(model)}");
                    
                    throw new Exception($"Failed to create multiple chứng thư số: {ex.Message}", ex);
                }
            }
        }
    }
}
