using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class UpdateChungThuSoCommand : IRequest<Unit>
    {
        public UpdateChungThuSoModel Model { get; set; }

        /// <summary>
        /// Cập nhật chứng thư số
        /// </summary>
        /// <param name="model">Thông tin chứng thư số cần cập nhật</param>
        public UpdateChungThuSoCommand(UpdateChungThuSoModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<UpdateChungThuSoCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(UpdateChungThuSoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Update {ChungThuSoConstant.CachePrefix}: {JsonSerializer.Serialize(model)}");

                var entity = await _dataContext.SgChungThuSos.FirstOrDefaultAsync(x => x.Id == model.Id);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                Log.Information($"Before Update {ChungThuSoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                model.UpdateEntity(entity);
                
                entity.ModifiedUserId = _contextAccessor.UserId;
                _dataContext.SgChungThuSos.Update(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"After Update {ChungThuSoConstant.CachePrefix}: {JsonSerializer.Serialize(entity)}");

                //Xóa cache
                _cacheService.Remove(ChungThuSoConstant.BuildCacheKey(entity.Id.ToString()));
                _cacheService.Remove(ChungThuSoConstant.BuildCacheKey());

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Cập nhật chứng thư số serial: {entity.SerialNumber}",
                    ObjectCode = ChungThuSoConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                return Unit.Value;
            }
        }
    }
}
