using Core.Data;
using Core.DataLog;
using Core.Shared;
using Core.Shared.Exceptions;
using FluentEmail.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using MongoDB.Driver;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Business
{
    /// <summary>
    /// Hàm này dùng cho Identity Server: lý do - chưa dùng được MediatR trong Identity Server
    /// </summary>
    public class DBAuthHandler : IAuthHandler
    {
        private readonly SystemDataContext _dataContext;
        private readonly IMongoCollection<SystemLog> _logs;
        private readonly IStringLocalizer<Resources> _stringLocalizer;

        public DBAuthHandler(SystemDataContext dataContext, IMongoDBDatabaseSettings settings, IStringLocalizer<Resources> stringLocalizer)
        {
            _dataContext = dataContext;

            var client = new MongoClient(settings.ConnectionString);
            var database = client.GetDatabase(settings.DatabaseName);

            _logs = database.GetCollection<SystemLog>(MongoCollections.SysLog);
            _stringLocalizer = stringLocalizer;
        }

        public async Task<LoginResponseModel> Login(LoginRequestModel model)
        {
            var user = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserName == model.UserName && x.PassWord == XCrypto.MD5(model.Password));

            if (user != null)
            {
                #region Lưu log vào hệ thống
                SystemLog log = new SystemLog()
                {
                    Id = string.Empty,
                    TraceId = Guid.NewGuid().ToString(),
                    UserId = user.UserId.ToString(),
                    UserName = user.UserName,
                    ActionCode = nameof(LogConstants.ACTION_LOGIN_SUCCESS),
                    ActionName = LogConstants.ACTION_LOGIN_SUCCESS,
                    CreatedDate = DateTime.Now,
                    Description = $"{user.UserName} - Đăng nhập hệ thống sử dụng mật khẩu"
                };
                await _logs.InsertOneAsync(log).ConfigureAwait(false);
                #endregion

                Log.Information($"Login success with username: {model.UserName}");

                return new LoginResponseModel()
                {
                    IdCanBo = user.UserId,
                    UserId = user.UserId,
                    UserName = model.UserName,
                    FullName = user.FullName
                };
            }
            await _logs.InsertOneAsync(new SystemLog()
            {
                Id = string.Empty,
                TraceId = Guid.NewGuid().ToString(),
                UserId = string.Empty,
                UserName = string.Empty,
                ActionCode = nameof(LogConstants.ACTION_LOGIN_FAIL),
                ActionName = LogConstants.ACTION_LOGIN_FAIL,
                CreatedDate = DateTime.Now,
                Description = $"{model.UserName} - Đăng nhập thất bại sử dụng tài khoản và mật khẩu"
            }).ConfigureAwait(false);

            throw new Exception("Kiểm tra lại thông tin đăng nhập");
        }

        public async Task<UserResponseModel> GetUserInfoById(int userId)
        {
            var user = await _dataContext.HtUsers.FindAsync(userId);

            if (user == null)
            {
                throw new Exception("Kiểm tra lại thông tin yêu cầu");
            }
            return new UserResponseModel()
            {
                MaCanBo = user.MaCanBoUser,
                UserId = user.UserId,
                UserName = user.UserName,
                FullName = user.FullName,
                Email = user.Email,
                Active = user.Active
            };
        }

        public async Task<UserResponseModel> GetUserInfoByUserName(string userName)
        {
            var user = await _dataContext.HtUsers.FirstOrDefaultAsync(x => x.UserName == userName);

            if (user == null)
            {
                throw new Exception("Kiểm tra lại thông tin yêu cầu");
            }
            return new UserResponseModel()
            {
                MaCanBo = user.MaCanBoUser,
                UserId = user.UserId,
                UserName = user.UserName,
                FullName = user.FullName,
                Email = user.Email,
                Active = user.Active
            };

        }

        public async Task<UserResponseModel> FindByExternalEmailAsync(string email, string issuer)
        {
            Log.Information($"Login with email: {email} with Issuer: {issuer}");
            var user = await _dataContext.HtUsers
                .Where(x => !string.IsNullOrEmpty(x.Email))
                .OrderBy(x => x.UserId)
                .FirstOrDefaultAsync(x => x.Email.ToLower().Trim() == email.ToLower().Trim());

            if (user == null)
            {
                #region Lưu log vào hệ thống
                await _logs.InsertOneAsync(new SystemLog()
                {
                    Id = string.Empty,
                    TraceId = Guid.NewGuid().ToString(),
                    UserId = string.Empty,
                    UserName = string.Empty,
                    ActionCode = nameof(LogConstants.ACTION_LOGIN_FAIL),
                    ActionName = LogConstants.ACTION_LOGIN_FAIL,
                    CreatedDate = DateTime.Now,
                    Description = $"{email} - Đăng nhập thất bại sử dụng email with Issuer: {issuer}"
                }).ConfigureAwait(false);
                #endregion
                return null;
            }

            #region Lưu log vào hệ thống
            SystemLog log = new SystemLog()
            {
                Id = string.Empty,
                TraceId = Guid.NewGuid().ToString(),
                UserId = user.UserId.ToString(),
                UserName = user.UserName,
                ActionCode = nameof(LogConstants.ACTION_LOGIN_SUCCESS),
                ActionName = LogConstants.ACTION_LOGIN_SUCCESS,
                CreatedDate = DateTime.Now,
                Description = $"{user.UserName} - Đăng nhập thành công sử dụng email with Issuer: {issuer}"
            };
            await _logs.InsertOneAsync(log).ConfigureAwait(false);
            #endregion

            return new UserResponseModel()
            {
                MaCanBo = user.MaCanBoUser,
                UserId = user.UserId,
                UserName = user.UserName,
                FullName = user.FullName,
                Email = user.Email,
                Active = user.Active
            };
        }

        public async Task<UserResponseModel> CreateExternalUserAsync(UserCreateModel user)
        {
            throw new BusinessException("RegisterContactAdmin");
            //var entity = new HtUser()
            //{
            //    UserName = user.UserName,
            //    FullName = user.FullName,
            //    Email = user.Email,
            //    Active = 1,
            //    PassWord = XCrypto.MD5(user.PassWord)
            //};
            //await _dataContext.HtUsers.AddAsync(entity);
            //await _dataContext.SaveChangesAsync();

            //return new UserResponseModel()
            //{
            //    MaCanBo = user.MaCanBoUser,
            //    UserId = user.UserId,
            //    UserName = user.UserName,
            //    FullName = user.FullName,
            //    Email = user.Email,
            //    Active = user.Active
            //};
        }
    }
}
