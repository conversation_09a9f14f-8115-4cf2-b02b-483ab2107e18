using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Business.ThirdPartyBussiness.SignServer;

public interface ISignServerService
{
    /// <summary>
    /// L<PERSON>y danh sách chứng thư số của người dùng
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<List<CertificateBase64Model>> GetCertificatesByUserIdAsync(int userId);
    
    /// <summary>
    /// Ký hash
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="certificateId"></param>
    /// <param name="base64Hash"></param>
    /// <returns></returns>
    Task<string> SignHashAsync(int userId, int certificateId, string base64Hash);
}