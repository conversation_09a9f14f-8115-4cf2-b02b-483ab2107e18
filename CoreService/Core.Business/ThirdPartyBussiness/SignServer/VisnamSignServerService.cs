using System.Collections.Generic;
using System.Threading.Tasks;
using Amazon.Runtime.Internal;
using VisnamSignServerClient;

namespace Core.Business.ThirdPartyBussiness.SignServer;

public class VisnamSignServerService : ISignServerService
{
    private readonly VisnamSignServerApiClient _client;

    public VisnamSignServerService(VisnamSignServerApiClient client)
    {
        _client = client;
    }

    public async Task<List<CertificateBase64Model>> GetCertificatesByUserIdAsync(int userId)
    {
        // TODO: Cần cập nhật lấy logic thông tin từ bảng SgVisnamTaiKhoanKetNoi
        var certificates = await _client.GetEndCertAsync(new EndCertRequestModel()
        {
            UserId = "4678b0d73a93431c9aea8d6be0d7ba06",
            UserKey = "GPBnrejn7KdPHj2IADhKgweq5EnxX7AUlBp0Mw07"
        });
        return [new CertificateBase64Model() { Data = certificates?.Data }];
    }

    public async Task<string> SignHashAsync(int userId, int certificateId, string base64Hash)
    {
        // TODO: Cần cập nhật lấy logic thông tin từ bảng SgVisnamTaiKhoanKetNoi
        var signHashResponse = await _client.SignHashAsync(new SignHashDataClientRequestModel()
        {
            Base64Hash = base64Hash,
            UserId = "4678b0d73a93431c9aea8d6be0d7ba06",
            UserKey = "GPBnrejn7KdPHj2IADhKgweq5EnxX7AUlBp0Mw07"
        });
        return signHashResponse?.Obj;
    }
}