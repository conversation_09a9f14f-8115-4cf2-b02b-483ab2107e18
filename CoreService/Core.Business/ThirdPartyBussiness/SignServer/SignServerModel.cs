using System;

namespace Core.Business.ThirdPartyBussiness.SignServer;

public class SignServerModel
{
    
}

public class CertificateRequestModel
{
    public string UserId { get; set; }
    public string UserKey { get; set; }
}

public class CertificateBase64Model
{
    public string Data { get; set; }
}

public class CertificateData
{
    public string SerialNumber { get; set; }
    public string CertificateBase64 { get; set; }
    public string SubjectName { get; set; }
    public string Issuer { get; set; }
    public DateTime NotBefore { get; set; }
    public DateTime NotAfter { get; set; }
}