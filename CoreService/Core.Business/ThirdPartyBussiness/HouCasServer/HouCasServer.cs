using Core.Shared;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Core.Business._3rdService.HouCasServer
{
    public class HouCasServer : IHouCasServer
    {
        private readonly string _casHostUrl;
        private readonly string _idHostUrl;
        private readonly IConfiguration _config;

        public HouCasServer(IConfiguration config)
        {
            _config = config;
            _casHostUrl = _config["HOU:CasServer:Url"];
            _idHostUrl = _config["HOU:IdentityServer:Url"];
        }

        public async Task<bool> AuthenticateHouCasServerAsync(string userName, string password)
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    var dt = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("username", userName),
                            new KeyValuePair<string, string>("password", password)
                        };
                    var req = new HttpRequestMessage(HttpMethod.Post, _casHostUrl + "/cas/v1/users") { Content = new FormUrlEncodedContent(dt) };
                    var res = await client.SendAsync(req);
                    if (res.IsSuccessStatusCode)
                    {
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error("{Message} - {Error}", ex.Message, ex.ToString());
                return false;
            }
        }

        public async Task<bool> AuthenticateIdentityCasServerAsync(string userName, string password)
        {
            try
            {
                using(HttpClient client = new HttpClient())
                {
                    var dt = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("grant_type", "password"),
                            new KeyValuePair<string, string>("username", userName),
                            new KeyValuePair<string, string>("password", password)
                        };
                    client.DefaultRequestHeaders.Add("Authorization", "Basic " + _config["HOU:IdentityServer:BasicToken"]);
                    var req = new HttpRequestMessage(HttpMethod.Post, _idHostUrl + "/mdm/rest/v2/oauth/token") { Content = new FormUrlEncodedContent(dt) };
                    var res = await client.SendAsync(req);

                    if (res.IsSuccessStatusCode)
                    {
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error("{Message} - {Error}", ex.Message, ex.ToString());
                return false;
            }
        }
    }
}
