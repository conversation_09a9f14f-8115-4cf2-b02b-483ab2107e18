using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Security.Authentication;
using Core.Shared;
using Serilog;
using Core.Business;
using MediatR;
using Microsoft.Extensions.Hosting;
using System.Text;
using System;
using RabbitMQ.Client.Events;
using System.Diagnostics.Tracing;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Serilog.Context;

namespace Core.API.Shared
{
    public class BackgroundServiceWorkerBase : BackgroundService
    {
        protected readonly IServiceScopeFactory _serviceScopeFactory;
        protected readonly IConfiguration _config;
        private readonly bool showRequestDuration = false;

        public BackgroundServiceWorkerBase(IServiceScopeFactory serviceScopeFactory, IConfiguration config)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _config = config;
            showRequestDuration = _config["AppSettings:ShowRequestDuration"] == "true";
        }

        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            stoppingToken.ThrowIfCancellationRequested();
            return Task.CompletedTask;
        }

        protected async Task<RequestUser> GetCurrentUserInfoFromHeader(IMediator mediator, BasicDeliverEventArgs eventArgs)
        {
            string traceId = "";
            int userId = 0;

            var header = eventArgs.BasicProperties.Headers;
            // Truy xuất TraceId, UserId từ header
            if (header != null)
            {
                if (header.TryGetValue(RabbitMQHeaderConstant.TraceId, out var traceIdObj))
                {
                    traceId = Encoding.UTF8.GetString((byte[])traceIdObj);
                }
                if (header.TryGetValue(RabbitMQHeaderConstant.UserId, out var userIdObj))
                {
                    string userIdString = Encoding.UTF8.GetString((byte[])userIdObj);
                    int.TryParse(userIdString, out userId);
                }
            }
            return await GetCurrentUserInfoByUserId(mediator, userId, traceId);
        }

        protected async Task ServiceExecuteFunction<T>(IServiceScope scope, BasicDeliverEventArgs eventArgs, Func<RequestUser, Task<T>> func)
        {
            var timer = new Stopwatch();

            if (showRequestDuration)
                timer.Start();

            var _mediator = scope.ServiceProvider
                    .GetRequiredService<IMediator>();
            var currentUser = await GetCurrentUserInfoFromHeader(_mediator, eventArgs);
            LogContext.PushProperty("TraceId", currentUser.TraceId.ToString());

            try
            {
                await func(currentUser);

                if (showRequestDuration)
                    timer.Stop();

                if (currentUser.SystemLog.ListAction.Any())
                {
                    currentUser.SystemLog.TimeExecution = timer.ElapsedMilliseconds;

                    //Bổ sung thông tin log
                    _ = _mediator.Send(new SystemLogCreateMultipleCommand(currentUser.SystemLog)).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                Log.Error("{Error}", ex.ToString());
            }
        }

        protected async Task ServiceExecuteFunction<T>(IServiceScope scope, BasicDeliverEventArgs eventArgs, Func<RequestUser, T> func)
        {
            var timer = new Stopwatch();

            if (showRequestDuration)
                timer.Start();

            var _mediator = scope.ServiceProvider
                    .GetRequiredService<IMediator>();
            var currentUser = await GetCurrentUserInfoFromHeader(_mediator, eventArgs);
            LogContext.PushProperty("TraceId", currentUser.TraceId.ToString());
            try
            {
                func(currentUser);

                if (showRequestDuration)
                    timer.Stop();

                if (currentUser.SystemLog.ListAction.Any())
                {
                    currentUser.SystemLog.TimeExecution = timer.ElapsedMilliseconds;

                    //Bổ sung thông tin log
                    _ = _mediator.Send(new SystemLogCreateMultipleCommand(currentUser.SystemLog)).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                Log.Error("{Error}", ex.ToString());
            }
        }

        private async Task<RequestUser> GetCurrentUserInfoByUserId(IMediator mediator, int userId, string traceId)
        {
            RequestUser currentUser = new RequestUser()
            {
                UserId = userId,
                SystemLog = new SystemLogModel()
                {
                    TraceId = traceId,
                    ListAction = new List<ActionDetail>()
                },
                TraceId = traceId,
            };

            if (userId != 0)
            {
                // Lấy thông tin người dùng đang truy cập để gán vào log
                var listUser = await mediator.Send(new GetComboboxUserQuery());
                var user = listUser?.FirstOrDefault(x => x.UserId == currentUser.UserId);
                if (user != null)
                {
                    currentUser.SystemLog.UserName = user.UserName;
                    currentUser.UserName = user.UserName;
                }
            }

            return currentUser;
        }
    }
}
