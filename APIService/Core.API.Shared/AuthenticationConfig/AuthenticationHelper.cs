using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using System.Security.Cryptography;
using Core.Shared;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Protocols;
using System.Linq;
using System.Threading;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace Core.API.Shared
{
    //public static class AuthenticationHelper
    //{
    //    /// <summary>
    //    /// Build token JWT
    //    /// </summary>
    //    /// <param name="user"></param>
    //    /// <param name="isRememberMe"></param>
    //    /// <param name="timeToLive"></param>
    //    /// <returns></returns>
    //    public static string BuildToken(IConfiguration config, BaseUserLoginModel user, bool isRememberMe, double timeToLive)
    //    {
    //        //var claims = new[]
    //        //{
    //        //    new Claim(ClaimTypes.Name, user.Id.ToString()),
    //        //    new Claim(JwtRegisteredClaimNames.NameId, user.Id.ToString()),
    //        //    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
    //        //};
    //        var claims = new[]
    //        {
    //            new Claim(ClaimTypes.Name, user.Id.ToString()),
    //            new Claim(JwtRegisteredClaimNames.NameId, user.Id.ToString()),
    //            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
    //            new Claim(ClaimConstants.USER_EMAIL, user.Email??""),
    //            new Claim(ClaimConstants.USER_NAME, user.UserName??""),
    //            new Claim(ClaimConstants.FULL_NAME, user.Name??""),
    //            new Claim(ClaimConstants.USER_ID, user.Id.ToString()),
    //            //new Claim(ClaimConstants.ROLES, JsonSerializer.Serialize(user.ListRole)),
    //            //new Claim(ClaimConstants.RIGHTS, JsonSerializer.Serialize(user.ListRight)),
    //            //new Claim(ClaimConstants.EXPIRES_AT, iat.ToUnixTime().ToString()),
    //            //new Claim(ClaimConstants.ISSUED_AT,  DateTime.UtcNow.tou().ToString()),
    //        };
    //        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(config["Authentication:Jwt:Key"]));
    //        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

    //        var token = new JwtSecurityToken(config["Authentication:Jwt:Issuer"],
    //                       config["Authentication:Jwt:Issuer"],
    //                       claims,
    //                       notBefore: DateTime.UtcNow,
    //                       expires: DateTime.UtcNow.AddSeconds(timeToLive),
    //                       signingCredentials: creds);
    //        if (isRememberMe)
    //        {
    //            token = new JwtSecurityToken(config["Authentication:Jwt:Issuer"],
    //                         config["Authentication:Jwt:Issuer"],
    //                         claims,
    //                         notBefore: DateTime.UtcNow,
    //                         expires: DateTime.UtcNow.AddDays(1),
    //                         signingCredentials: creds);
    //        }

    //        return new JwtSecurityTokenHandler().WriteToken(token);

    //    }
    //}

    public static class JwtService
    {
        private readonly static string secretKey = "&04a&vuf0rtfE3^hM!Bpw*6iNMw^VCRC&Hql16E1iD$uLEYdw**UloacynJv4ZvGNtu*28$@yzphGLi!IKjNUCZKGGSeZ@GpxNI";

        // Tạo token
        public static string GenerateToken(IConfiguration config, BaseUserLoginModel user, bool isRememberMe)
        {
            string key = config["Authentication:Jwt:Key"] ?? "";
            if (string.IsNullOrEmpty(key) || key.Length < 36)
            {
                key = secretKey; // Sử dụng secretKey mặc định nếu không có trong cấu hình
            }
            SymmetricSecurityKey securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(key));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            // Thông tin payload (claims)
            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.Name, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.NameId, user.Id.ToString()),
                new Claim(ClaimConstants.USER_EMAIL, user.Email??""),
                new Claim(ClaimConstants.USER_NAME, user.UserName??""),
                new Claim(ClaimConstants.FULL_NAME, user.Name??""),
                new Claim(ClaimConstants.USER_ID, user.Id.ToString()),
            };

            double timeToLive = Convert.ToDouble(config["Authentication:Jwt:TimeToLive"]);
            if (isRememberMe)
            {
                // Nếu là Remember Me, token sẽ có thời gian sống là 30 ngày
                timeToLive = 2592000;
            }

            var token = new JwtSecurityToken(
                issuer: config["Authentication:Jwt:Issuer"],       // có thể để null hoặc tên server
                audience: config["Authentication:Jwt:Issuer"],   // có thể để null hoặc tên client
                claims: claims,
                expires: DateTime.UtcNow.AddSeconds(timeToLive),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        // Validate token
        public static ClaimsPrincipal? ValidateToken(IConfiguration config, string token)
        {
            string key = config["Authentication:Jwt:Key"] ?? "";
            if (string.IsNullOrEmpty(key) || key.Length < 36)
            {
                key = secretKey; // Sử dụng secretKey mặc định nếu không có trong cấu hình
            }

            var tokenHandler = new JwtSecurityTokenHandler();
            var securityKey = Encoding.UTF8.GetBytes(key);

            try
            {
                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(securityKey),

                    ValidateIssuer = true,
                    ValidIssuer = config["Authentication:Jwt:Issuer"],

                    ValidateAudience = true,
                    ValidAudience = config["Authentication:Jwt:Issuer"],

                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);

                // Kiểm tra thêm xem thuật toán dùng để ký token có đúng không
                if (validatedToken is JwtSecurityToken jwtToken &&
                    jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                {
                    return principal;
                }
            }
            catch (Exception)
            {
                // Token không hợp lệ hoặc hết hạn
                return null;
            }

            return null;
        }
    }

    public class CodeVerifierHelper
    {

        public static string CodeVerifier;

        public static string CodeChallenge;

        public CodeVerifierHelper()
        {
            CodeVerifier = GenerateNonce();
            CodeChallenge = GenerateCodeChallenge(CodeVerifier);
        }

        private static string GenerateNonce()
        {
            const string chars = "abcdefghijklmnopqrstuvwxyz123456789";
            var random = new Random();
            var nonce = new char[128];
            for (int i = 0; i < nonce.Length; i++)
            {
                nonce[i] = chars[random.Next(chars.Length)];
            }

            return new string(nonce);
        }

        private static string GenerateCodeChallenge(string codeVerifier)
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(codeVerifier));
            var b64Hash = Convert.ToBase64String(hash);
            var code = Regex.Replace(b64Hash, "\\+", "-");
            code = Regex.Replace(code, "\\/", "_");
            code = Regex.Replace(code, "=+$", "");
            return code;
        }

    }

    /// <summary>
    /// Provides a randomly generating PKCE code verifier and it's corresponding code challenge.
    /// </summary>
    public class Pkce
    {
        /// <summary>
        /// The randomly generating PKCE code verifier.
        /// </summary>
        public string CodeVerifier;

        /// <summary>
        /// Corresponding PKCE code challenge.
        /// </summary>
        public string CodeChallenge;

        /// <summary>
        /// Initializes a new instance of the Pkce class.
        /// </summary>
        /// <param name="size">The size of the code verifier (43 - 128 charters).</param>
        public Pkce(uint size = 128)
        {
            CodeVerifier = GenerateCodeVerifier(size);
            CodeChallenge = GenerateCodeChallenge(CodeVerifier);
        }

        /// <summary>
        /// Generates a code_verifier based on rfc-7636.
        /// </summary>
        /// <param name="size">The size of the code verifier (43 - 128 charters).</param>
        /// <returns>A code verifier.</returns>
        /// <remarks> 
        /// code_verifier = high-entropy cryptographic random STRING using the 
        /// unreserved characters[A - Z] / [a-z] / [0-9] / "-" / "." / "_" / "~"
        /// from Section 2.3 of[RFC3986], with a minimum length of 43 characters
        /// and a maximum length of 128 characters.
        ///    
        /// ABNF for "code_verifier" is as follows.
        ///    
        /// code-verifier = 43*128unreserved
        /// unreserved = ALPHA / DIGIT / "-" / "." / "_" / "~"
        /// ALPHA = %x41-5A / %x61-7A
        /// DIGIT = % x30 - 39 
        ///    
        /// Reference: rfc-7636 https://datatracker.ietf.org/doc/html/rfc7636#section-4.1     
        ///</remarks>
        public static string GenerateCodeVerifier(uint size = 128)
        {
            if (size < 43 || size > 128)
                size = 128;

            const string unreservedCharacters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";
            Random random = new Random();
            char[] highEntropyCryptograph = new char[size];

            for (int i = 0; i < highEntropyCryptograph.Length; i++)
            {
                highEntropyCryptograph[i] = unreservedCharacters[random.Next(unreservedCharacters.Length)];
            }

            return new string(highEntropyCryptograph);
        }

        /// <summary>
        /// Generates a code_challenge based on rfc-7636.
        /// </summary>
        /// <param name="codeVerifier">The code verifier.</param>
        /// <returns>A code challenge.</returns>
        /// <remarks> 
        /// plain
        ///    code_challenge = code_verifier
        ///    
        /// S256
        ///    code_challenge = BASE64URL-ENCODE(SHA256(ASCII(code_verifier)))
        ///    
        /// If the client is capable of using "S256", it MUST use "S256", as
        /// "S256" is Mandatory To Implement(MTI) on the server.Clients are
        /// permitted to use "plain" only if they cannot support "S256" for some
        /// technical reason and know via out-of-band configuration that the
        /// server supports "plain".
        /// 
        /// The plain transformation is for compatibility with existing
        /// deployments and for constrained environments that can't use the S256
        /// transformation.
        ///    
        /// ABNF for "code_challenge" is as follows.
        ///    
        /// code-challenge = 43 * 128unreserved
        /// unreserved = ALPHA / DIGIT / "-" / "." / "_" / "~"
        /// ALPHA = % x41 - 5A / %x61-7A
        /// DIGIT = % x30 - 39
        /// 
        /// Reference: rfc-7636 https://datatracker.ietf.org/doc/html/rfc7636#section-4.2
        /// </remarks>
        public static string GenerateCodeChallenge(string codeVerifier)
        {
            using (var sha256 = SHA256.Create())
            {
                var challengeBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(codeVerifier));
                return Base64UrlEncoder.Encode(challengeBytes);
            }
        }
    }
}