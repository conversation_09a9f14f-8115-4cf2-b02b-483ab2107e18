using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Core.API.Shared
{
    public class LoginModel
    {
        public string Username { get; set; }
        public string Password { get; set; }
        public bool RememberMe { get; set; }
    }


    public class LoginResponse
    {
        public int UserId { get; set; }
        public BaseUserModel UserModel { get; set; }
        public string TokenString { get; set; }
        public DateTime TimeExpride { get; set; }
        //public BaseApplicationModel ApplicationModel { get; set; }
        public List<string> ListRight { get; set; }
        public List<string> ListRole { get; set; }
    }

    public class BaseUserLoginModel
    {
        public int Id { get; set; }
        public string UserName { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public bool IsLocked { get; set; }
        public string QrCodeString { get; set; }
        public int? OrganizationId { get; set; }
        public List<string> ListRole { get; set; }
        public List<string> ListRight { get; set; }
    }
    public class UserLoginModel : BaseUserLoginModel
    {
    }

    public class BaseUserModel
    {
        public int Id { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public string Name { get; set; }
        public string IdentityNumber { get; set; }
        public string PhoneNumber { get; set; }
        public int? OrganizationId { get; set; }
    }
    public class OAuthTokenModel
    {
        public string AccessToken { get; set; }
        public string IdentityToken { get; set; }
        public string Scope { get; set; }
        public string TokenType { get; set; }
        public string RefreshToken { get; set; }
        public int ExpiresIn { get; set; }
        public UserAuthModel UserInfo { get; set; }
    }

    public class OAuthUserInfoModel
    {
        public IEnumerable<Claim> Claims { get; set; }
    }
    public class TokenIntrospectionModel
    {
        public bool IsActive { get; set; }

        public IEnumerable<Claim> Claims { get; set; }
    }

    #region OIDC
    public class RequestAuthorizationCodeTokenModel
    {
        [Required(ErrorMessage = "authentication.code.required")]
        public string AuthorizationCode { get; set; }
        public string RedirectUri { get; set; }
    }

    public class UserAuthModel
    {
        public int Id { get; set; }
        public bool IsActive { get; set; }
        public string UserName { get; set; }
        public string FullName { get; set; }
        public string MaCanBo { get; set; }
        public string Email { get; set; }
        public List<string> Roles { get; set; }
    }
    #endregion

    #region HOU_CAS
    public class RequestValidateTicketModel
    {
        public string Ticket { get; set; }
        public string Service { get; set; }
    }
    #endregion
}
