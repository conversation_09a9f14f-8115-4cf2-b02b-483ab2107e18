using VaultSharp;
using VaultSharp.V1.Commons;
using Microsoft.Extensions.Configuration;
using Serilog;
using System.Text.Json;
using VaultSharp.V1.AuthMethods.Token;
using Core.Shared;

namespace Core.API.Shared
{
    public class VaultConfigurationProvider : ConfigurationProvider
    {
        public VaultOptions _config;
        private IVaultClient _client;

        public VaultConfigurationProvider(VaultOptions config)
        {
            _config = config;

            // Cấu hình Vault client
            var authMethod = new TokenAuthMethodInfo(_config.Token);
            var vaultClientSettings = new VaultClientSettings(_config.Address, authMethod);
            _client = new VaultClient(vaultClientSettings);
        }

        public override void Load()
        {
            LoadSecretAsync().Wait();
        }
         
        public async Task LoadSecretAsync()
        {
            // Tạo dictionary để lưu các key phẳng
            var flattenedDictionary = new Dictionary<string, string>();
            
            try
            {
                // L<PERSON>y danh sách cấu hình dùng chung
                if (!string.IsNullOrEmpty(_config.Mount) && !string.IsNullOrEmpty(_config.Secret))
                {
                    Secret<SecretData> secrets = await _client.V1.Secrets.KeyValue.V2.ReadSecretAsync(_config.Secret, null, _config.Mount);
                    foreach (var kvp in secrets.Data.Data)
                    {
                        // Chuyển đổi object thành chuỗi JSON
                        //var data = JsonSerializer.Serialize(kvp.Value);
                        // TODO: Tạm chuyển đổi để dùng string
                        var data = kvp.Value.ToString();

                        if (Utils.IsJson(data))
                        {
                            // Phân tích cú pháp JSON thành JsonDocument
                            using (JsonDocument document = JsonDocument.Parse(data))
                            {
                                // Bắt đầu chuyển đổi từ gốc của JSON object
                                Utils.FlattenJson(kvp.Key, document.RootElement, flattenedDictionary);
                            }
                        }
                        else
                        {
                            // Nếu không phải là JSON, xử lý chuỗi như một giá trị thông thường
                            flattenedDictionary[kvp.Key] = data;
                        }
                    }

                }

                // Lấy danh sách cấu hình của riêng service
                if (!string.IsNullOrEmpty(_config.Mount) && !string.IsNullOrEmpty(_config.SecretDetail))
                {
                    Secret<SecretData> secretsDetail = await _client.V1.Secrets.KeyValue.V2.ReadSecretAsync(_config.SecretDetail, null, _config.Mount);
                    foreach (var kvp in secretsDetail.Data.Data)
                    {
                        // Chuyển đổi object thành chuỗi JSON
                        var data = JsonSerializer.Serialize(kvp.Value);

                        if (Utils.IsJson(data))
                        {
                            // Phân tích cú pháp JSON thành JsonDocument
                            using (JsonDocument document = JsonDocument.Parse(data))
                            {
                                // Bắt đầu chuyển đổi từ gốc của JSON object
                                Utils.FlattenJson(kvp.Key, document.RootElement, flattenedDictionary);
                            }
                        }
                        else
                        {
                            // Nếu không phải là JSON, xử lý chuỗi như một giá trị thông thường
                            flattenedDictionary[kvp.Key] = data;
                        }
                    }
                }
               
                // Gán dữ liệu từ Vault vào cấu hình
                foreach (var kvp in flattenedDictionary)
                {
                    Data.Add(kvp.Key, string.IsNullOrEmpty(kvp.Value) ? string.Empty : kvp.Value);
                    //Console.WriteLine($"{kvp.Key} - {kvp.Value}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message}");
                Log.Error("{Message} - {Error}", ex.Message, ex.ToString());
            }
        }
    }

    public class VaultConfigurationSource : IConfigurationSource
    {
        private VaultOptions _config;

        public VaultConfigurationSource(Action<VaultOptions> config)
        {
            _config = new VaultOptions();
            config.Invoke(_config);
        }

        public IConfigurationProvider Build(IConfigurationBuilder builder)
        {
            return new VaultConfigurationProvider(_config);
        }
    }

    public class VaultOptions
    {
        public bool Enabled { get; set; }
        public string Address { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public string Mount { get; set; } = string.Empty;
        public string Secret { get; set; } = string.Empty;
        public string SecretDetail { get; set; } = string.Empty;
    }

    public static class VaultExtensions
    {
        public static IConfigurationBuilder AddVault(this IConfigurationBuilder configuration, Action<VaultOptions> options)
        {
            var vaultOptions = new VaultConfigurationSource(options);
            configuration.Add(vaultOptions);
            return configuration;
        }
    }
}