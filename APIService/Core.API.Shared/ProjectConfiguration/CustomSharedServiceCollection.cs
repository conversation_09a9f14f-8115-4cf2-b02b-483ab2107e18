using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Core.Business;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Core.Business.Core;
using Microsoft.Extensions.Localization;
using Serilog.Sinks.Elasticsearch;
using Serilog;
using System.Reflection;
using Serilog.Exceptions;
using Serilog.Events;
using Core.Shared.ContextAccessor;

namespace Core.API.Shared
{
    public static class CustomSharedServiceCollection
    {
        /// <summary>
        /// Cache services in the project.
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterCacheComponents(this IServiceCollection services, IConfiguration configuration)
        {
            if (configuration["redis:enabled"] == "true")
            {
                services.AddSingleton<ICacheService, RedisCacheService>();
            }
            else
            {
                services.AddSingleton<ICacheService, InMemoryCacheService>();
            }
            services.AddSingleton<IMemoryCacheService, MemoryCacheService>();
            return services;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterCustomerModelParsingServiceComponents(this IServiceCollection services)
        {
            //Custom Data Annotation parsing + message
            services.AddControllers()
                .AddDataAnnotationsLocalization(o =>
                {
                    o.DataAnnotationLocalizerProvider = (type, factory) => factory.Create(typeof(DataAnnotations));
                })
                .ConfigureApiBehaviorOptions(options =>
                {
                    options.InvalidModelStateResponseFactory = context =>
                    {
                        string messages = "";
                        string key = "";
                        List<string> errors = new List<string>();
                        List<Dictionary<string, string>> errorDetail = new List<Dictionary<string, string>>();
                        var keys = context.ModelState.Keys.ToArray();
                        var errMess = context.ModelState.Values.ToArray();
                        for (int i = 0; i < keys.Length; i++)
                        {
                            messages = string.Join("; ", errMess[i].Errors
                                   .Select(x => x.ErrorMessage));
                            errors.AddRange(errMess[i].Errors.Select(x => x.ErrorMessage));
                            key = char.ToLowerInvariant(keys[i][0]) + keys[i].Substring(1);
                            errorDetail.Add(new Dictionary<string, string>() { { key, messages } });
                        }
                        return Helper.TransformData(new ResponseError(Code.BadRequest, string.Join("; ", errors), errorDetail));
                    };
                });

            return services;
        }

        public static IServiceCollection RegisterAPIVersionServiceComponents(this IServiceCollection services)
        {
            services.AddApiVersioning(
                 options =>
                 {
                     options.AssumeDefaultVersionWhenUnspecified = true;
                     options.ReportApiVersions = true;
                 });
            //services.AddVersionedApiExplorer(x =>
            //{
            //    x.GroupNameFormat = "'v'VVV";
            //    // note: this option is only necessary when versioning by url segment. the SubstitutionFormat
            //    // can also be used to control the format of the API version in route templates
            //    x.SubstituteApiVersionInUrl = true;
            //});

            return services;
        }

        public static IServiceCollection AddCustomAuthenServiceComponents(this IServiceCollection services)
        {
            // Authorize related configuration
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = "Custom Scheme";
                options.DefaultChallengeScheme = "Custom Scheme";
            }).AddCustomAuth(o => { });

            return services;
        }

        public static IServiceCollection RegisterLocalizationServiceComponents(this IServiceCollection services)
        {
            services.AddLocalization();
            services.AddSingleton<IStringLocalizerFactory, JsonStringLocalizerFactory>();
            services.Configure<RequestLocalizationOptions>(
                options =>
                {
                    var supportedCultures = new List<CultureInfo>
                    {
                        new CultureInfo("vi-VN"),
                        new CultureInfo("en-US"),
                    };
                    options.DefaultRequestCulture = new RequestCulture(culture: "vi-VN");
                    options.SupportedCultures = supportedCultures;
                    options.RequestCultureProviders = new List<IRequestCultureProvider>
                                            {
                                                //Theo thứ tự ưu tiên
                                                new CookieRequestCultureProvider(),
                                                new QueryStringRequestCultureProvider(),
                                                new RouteDataRequestCultureProvider()
                                            };
                });
            return services;
        }

        public static IServiceCollection RegisterDataAnotationLocalizationServiceComponent(this IServiceCollection services)
        {
            services.AddMvc()
                    .AddViewLocalization()
                    .AddDataAnnotationsLocalization(options =>
                    {
                        options.DataAnnotationLocalizerProvider = (type, factory) =>
                        {
                            return factory.Create(typeof(DataAnnotations));
                        };
                    });

            return services;
        }

        public static IServiceCollection RegisterIConfigurationComponents(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IConfiguration>(configuration);

            return services;
        }

        public static IServiceCollection RegisterLogComponents(this IServiceCollection services, IConfiguration configuration)
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            // Configure serilog
            var logger = new LoggerConfiguration()
                    .ReadFrom.Configuration(configuration)
                    .Enrich.FromLogContext()
                    .Enrich.WithExceptionDetails();

            Log.Logger = logger.CreateLogger();

            return services;
        }

        public static IServiceCollection RegisterApplicationContextAccessor(this IServiceCollection services)
        {
            ApplicationContextAccessor.Configure(services);

            return services;
        }
    }
}