using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using System.Security.Claims;

namespace Core.API.Shared
{
    /// <summary>
    /// Attribute để validate API Key
    /// </summary>
    public class ApiKeyAuthenticationAttribute : Attribute, IAsyncActionFilter
    {
        private const string API_KEY_HEADER_NAME = "X-API-Key";

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // Lấy API Key từ header
            if (!context.HttpContext.Request.Headers.TryGetValue(API_KEY_HEADER_NAME, out var extractedApiKey))
            {
                context.Result = new UnauthorizedObjectResult(new ResponseObject<object>(null, "API Key is missing", Code.Unauthorized));
                return;
            }

            var apiKey = extractedApiKey.FirstOrDefault();
            if (string.IsNullOrEmpty(apiKey))
            {
                context.Result = new UnauthorizedObjectResult(new ResponseObject<object>(null, "API Key is empty", Code.Unauthorized));
                return;
            }

            // Validate API Key using MediatR with cache
            var mediator = context.HttpContext.RequestServices.GetRequiredService<IMediator>();
            var localizer = context.HttpContext.RequestServices.GetRequiredService<IStringLocalizer<Resources>>();

            var now = DateTime.Now;
            var apiKeyEntity = await mediator.Send(new GetApiKeyByKeyQuery(apiKey));

            if (apiKeyEntity == null)
            {
                context.Result = new UnauthorizedObjectResult(new ResponseObject<object>(null, localizer["api-key.invalid"], Code.Unauthorized));
                return;
            }

            if (!apiKeyEntity.IsActive)
            {
                context.Result = new UnauthorizedObjectResult(new ResponseObject<object>(null, "API Key is inactive", Code.Unauthorized));
                return;
            }

            if (apiKeyEntity.ValidFrom > now)
            {
                context.Result = new UnauthorizedObjectResult(new ResponseObject<object>(null, "API Key is not yet valid", Code.Unauthorized));
                return;
            }

            if (apiKeyEntity.ValidTo < now)
            {
                context.Result = new UnauthorizedObjectResult(new ResponseObject<object>(null, "API Key has expired", Code.Unauthorized));
                return;
            }

            // Tạo claims cho user authentication
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, apiKeyEntity.UserId.ToString()),
                new Claim(ClaimConstants.USER_ID, apiKeyEntity.UserId.ToString()),
                new Claim(ClaimConstants.API_KEY, apiKeyEntity.Key),
                new Claim("authentication_method", "api_key")
            };

            // Tạo ClaimsIdentity và ClaimsPrincipal
            var identity = new ClaimsIdentity(claims, "ApiKey");
            var principal = new ClaimsPrincipal(identity);

            // Set user principal vào HttpContext
            context.HttpContext.User = principal;

            // Thêm thông tin API Key vào HttpContext để sử dụng trong controller
            context.HttpContext.Items["ApiKey"] = apiKeyEntity;

            await next();
        }
    }
}
