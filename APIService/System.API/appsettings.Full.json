{
  "AppSettings": {
    "EnableSwagger": "true",
    "Tittle": "System UniCore Service",
    "Description": "Net Core Framework",
    "TermsOfService": "",
    "Contact": {
      "Name": "HaiND",
      "Email": "<EMAIL>",
      "Url": "https://github.com/duchaindh94"
    },
    "License": {
      "Name": "The MIT License (MIT).",
      "Url": "https://github.com/dotnet/core"
    },
    "UseSerilogRequestLogging": "false",
    "ReturnDetailError500Message": "false", // true - Hi<PERSON>n thị chi tiết lỗi 500 dùng cho test, false - dùng cho môi trường prod để hạn chế dữ liệu bảo mật
    "CorsOrigins": "http://localhost:8000", // Các domain được phép truy cập API
    "EnableCache": "true", // <PERSON>ó sử dụng cache hay không
    "ShowRequestDuration": "true", // Hi<PERSON><PERSON> thị thời gian xử lý request
    "IgnoreLoadPermission": "false", // Bỏ qua việc load permission - dùng cho case tài khoản portal của tuyển sinh và các case tương tự (tài khoản đăng nhập không nằm ở bảng htUsers)
    "EnableLoadTeacherInfoInMiddleware": "true", // Dùng cho phân hệ UniTeacher để lấy thông tin giáo viên từ middleware
    "ClearCacheCronJob": {
      "Enable": "false",
      "CronExpression": "*/10 * * * *"
    },
    "HostedService": {
      "NotificationConsumer": {
        "Enable": "true"
      }
    },
    "UploadConfig": {
      "FolderUpload": "files", // Thư mục lưu file upload với trường hợp upload và lưu file vào thư mục
      "AllowedExtensions": [ ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx" ],
      "MaxFileSize": "10" // Mb
    },
    "IPHeader": "X-Forwarded-For" // Header chứa IP client" - "RemoteIpAddress", "X-Forwarded-For", "X-Real-Ip"
  },
  "Database": {
    "UserId": "m6JrNW3dDEHTP9XqbSj4QjMJNNQKEebBttro3Vmb6Pw=",
    "Password": "qQrWvbaV647kn+jgD/RtbBFRY4/hXSqoHbhYQYgexc4nV5qzoykRWR4wm+E1puMx",
    "Workflow": {
      "ConnectionString": {
        "MSSQLDatabase": "server=**************;database=UNISOFT_FULL6_DEV;User ID=Unisoftdev;password=*****************;TrustServerCertificate=True;"
      }
    },
    "System": {
      "ConnectionString": {
        "MSSQLDatabase": "server=**************;database=UNISOFT_FULL6_TEST;User ID={0};password=***;TrustServerCertificate=True;",
        "MSSQLDatabaseRead": "server=**************;database=UNISOFT_FULL6_TEST;User ID={0};password=***;TrustServerCertificate=True;"
      }
    }
  },
  "Signing": {
    "Visnam": {
      "Domain": "demobachmai.vin-hsm.com",
      "Schema": "https",
      "Port": 443
    }
  },
  "MongoDBDatabaseSettings": {
    "ConnectionString": "***********************************************************************************",
    "DatabaseName": "UniCore_System_Log"
  },
  "redis": {
    "enabled": "true",
    "configuration": "************:6379,password=thienan123,defaultDatabase=0",
    "instanceName": "UniSoft:",
    "timeLive": "30000"
  },
  "RabbitMq": {
    "Uri": "amqp://unisoft:unisoft123@************:5672/unisoft-dev"
  },
  "EmailSettings": {
    "DefaultFromEmail": "<EMAIL>",
    "DefaultFromName": "UniSoft",
    "Host": "smtp.gmail.com",
    "Port": 587,
    "UserName": "<EMAIL>",
    "Password": "",
    "SSL": "1"
  },
  "Authentication": {
    "HOU": {
      "CasServerUrl": "https://cas.hou.edu.vn"
    },
    "Jwt": {
      "Enable": "false",
      "Key": "P6nq35iYhryaTKyJYdG62SejaZAjDtHrbq4zzT4h",
      "Issuer": "UNISOFT-AUTH-BASE",
      "TimeToLive": "3600"
    },
    "IdentityServer": {
      "Enable": "true",
      "EnableCacheJWKS": "false",
      "Uri": "https://sso2.unisoft.edu.vn",
      "ClientId": "uni-hrm-portal-client",
      "Secret": "pt0bM7sY!9*cpT7s$MjGB4s"
    },    
    "Keycloak": {
      "Enable": "false",
      "EnableCacheJWKS": "false",
      "Uri": "http://localhost:8080",
      "Realm" : "unisoft",
      "ClientId": "unisoft-client"
    },
    "AdminUserName": "admin"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Core.API.Shared.CustomAuthHandler": "None"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.File"
    ],
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Core.API.Shared.CustomAuthHandler": "Error",
        "Microsoft.EntityFrameworkCore.Database.Command": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "File",
              "Args": {
                "path": "/opt/logs_folder/system/log-.txt",
                "rollingInterval": "Day",
                "fileSizeLimitBytes": 10485760,
                "retainedFileCountLimit": 100,
                "rollOnFileSizeLimit": true,
                "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{CorrelationId}][{TraceIdentifier}] <{SourceContext}> {Message}{NewLine}{Exception}"
              }
            },
            {
              "Name": "Console",
              "Args": {
                "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console",
                "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{CorrelationId}][{TraceIdentifier}] <{SourceContext}> {Message}{NewLine}{Exception}"
              }
            },
            {
              "Name": "GrafanaLoki",
              "Args": {
                "uri": "http://localhost:3100",
                "credentials": {
                  "login": "grafanalokiuser",
                  "password": "grafanalokipass"
                },
                "labels": [
                  {
                    "key": "app",
                    "value": "system-api-test"
                  }
                ]
              }
            }
          ]
        }
      }
    ]
  }
}