using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Threading.Tasks;
using System.Threading;
using Core.Business;
using Serilog;
using MediatR;

namespace System.API.Services
{
    public class StartupService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;

        public StartupService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                Log.Information("StartupService");
                try
                {
                    // Nên chạy migration trước khi seed data
                    await scope.ServiceProvider
                        .GetRequiredService<IMediator>()
                        .Send(new RunSystemMigrationsCommand());

                    await scope.ServiceProvider
                        .GetRequiredService<IMediator>()
                        .Send(new SeedDataCommand(new Core.Shared.SystemLogModel()));
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error during startup service initialization");
                }
            }
        }

        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    }
}
