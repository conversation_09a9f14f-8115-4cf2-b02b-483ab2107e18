using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Logging;
using Serilog;
using System;
using System.Text;

namespace Core.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            try
            {
                ////Fix lỗi: .NET6 and DateTime problem. Cannot write DateTime with Kind=UTC to PostgreSQL type 'timestamp without time zone'
                //AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
                //AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
                var host = CreateHostBuilder(args).Build();
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

                var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                Log.Information($"API started in folder: {AppDomain.CurrentDomain.BaseDirectory} - (with evirontment - {environment}) at: " + DateTime.Now.ToString());

                host.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "The service terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                })
                .UseSerilog();
    }
}
