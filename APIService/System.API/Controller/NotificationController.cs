using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business.Core;
using Core.Shared;
using Core.API.Shared;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    /// <summary>
    /// Module cache management
    /// </summary>
    [ApiController]
    [Route("system/v1/notification")]
    [ApiExplorerSettings(GroupName = "102. Notification Service", IgnoreApi = false)]
    [Authorize]
    public class NotificationController : ApiControllerBase
    {
        public NotificationController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// G<PERSON>i thông báo đến user
        /// </summary>
        /// <param name="model">Thông tin cần gửi thông báo</param>
        /// <returns></returns>
        [HttpPost, Route("send-noti-user-hub")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendNotificationToUserByHub([FromBody] NotificationModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SendNotificationToUserByHubCommand(model, u));
            });
        }

        /// <summary>
        /// Gửi thông báo broadcast
        /// </summary>
        /// <param name="model">Thông tin cần gửi thông báo</param>
        /// <returns></returns>
        [HttpPost, Route("send-noti-broadcast-hub")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendBroadcastNotificationByHub([FromBody] NotificationBroadcastModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SendBroadcastNotificationByHubCommand(model, u));
            });
        }

        /// <summary>
        /// Gửi thông báo đến user qua message queue
        /// </summary>
        /// <param name="model">Thông tin cần gửi thông báo</param>
        /// <returns></returns>
        [HttpPost, Route("send-noti-user-queue")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendNotificationToUserByQueue([FromBody] NotificationModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                //model.TraceId = u.SystemLog.TraceId;
                return await _mediator.Send(new SendNotificationToUserByMQCommand(model, u));
            });
        }

        /// <summary>
        /// Gửi thông báo broadcast qua message queue
        /// </summary>
        /// <param name="model">Thông tin cần gửi thông báo</param>
        /// <returns></returns>
        [HttpPost, Route("send-noti-broadcast-queue")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] NotificationBroadcastModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                //model.TraceId = u.SystemLog.TraceId;
                return await _mediator.Send(new SendBroadcastNotificationByMQCommand(model, u));
            });
        }


        /// <summary>
        /// Teacher - Gửi thông báo đến user qua message queue
        /// </summary>
        /// <param name="model">Thông tin cần gửi thông báo</param>
        /// <returns></returns>
        [HttpPost, Route("send-noti-user-teacher-queue")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendTeacherNotificationToUserByQueue([FromBody] TeacherNotificationModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                //model.TraceId = u.SystemLog.TraceId;
                return await _mediator.Send(new SendTeacherNotificationToUserByMQCommand(model, u));
            });
        }

    }
}
