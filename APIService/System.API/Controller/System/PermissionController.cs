using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/permission")]
    [ApiExplorerSettings(GroupName = "05. Permission (Danh mục quyền)")]
    [Authorize]
    public class PermissionController : ApiControllerBase
    {
        public PermissionController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// L<PERSON>y danh sách quyền người dùng cho combobox
        /// </summary> 
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<PermissionSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var result = await _mediator.Send(new GetComboboxPermissionQuery(count, ts));

                return result;
            });
        }
    }
}
