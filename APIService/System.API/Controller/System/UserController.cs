using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.Shared;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;
using System.Linq;
using Core.Business.System;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/user")]
    [ApiExplorerSettings(GroupName = "07. User (Người dùng)")]
    [Authorize]
    public class UserController : ApiControllerBase
    {
        public UserController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// Thêm mới người dùng
        /// </summary>
        /// <param name="model">Thông tin người dùng</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_ADD))]
        [ProducesResponseType(typeof(ResponseObject<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateUserModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_CREATE);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_CREATE;


                model.CreatedUserId = u.UserId;
                return await _mediator.Send(new CreateUserCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy người dùng theo id
        /// </summary>
        /// <param name="id">Id người dùng</param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<UserModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetUserByIdQuery(id));
            });
        }

        /// <summary>
        /// Cập nhật người dùng
        /// </summary>
        /// <param name="model">Thông tin người dùng cần cập nhật</param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] UpdateUserModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_UPDATE);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_UPDATE;


                model.UserId = id;
                return await _mediator.Send(new UpdateUserCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Cập nhật thông tin cá nhân
        /// </summary>
        /// <param name="model">Thông tin người dùng cần cập nhật</param>
        /// <returns></returns>
        [HttpPut, Route("update-user-info")]
        [ProducesResponseType(typeof(ResponseObject<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromBody] UserCurrentUpdateModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_UPDATE_USER_INFO);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_UPDATE_USER_INFO;

                model.UserId = u.UserId;
                return await _mediator.Send(new UpdateCurrentUserCommand(model, u.SystemLog));
            });
        }

        ///// <summary>
        ///// Khóa/mở khóa người dùng
        ///// </summary>
        ///// <param name="id">Id người dùng cần cập nhật trạng thái</param>
        ///// <returns></returns>
        //[HttpPut, Route("update-lock-status")]
        //[ProducesResponseType(typeof(ResponseObject<int>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> UpdateLockStatus([FromQuery] int id)
        //{
        //    return await ExecuteFunction(async (RequestUser u) =>
        //    {
        //        u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_UPDATE_LOCK);
        //        u.SystemLog.ActionName = LogConstants.ACTION_USER_UPDATE_LOCK;
        //        

        //        return await _mediator.Send(new LockAndUnlockUserCommand(id, u.UserId, u.SystemLog));
        //    });
        //}

        /// <summary>
        /// Xóa người dùng theo id truyền vào
        /// </summary>
        /// <param name="id">id người dùng cần xóa</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_DELETE))]
        [ProducesResponseType(typeof(ResponseObject<List<ResponeDeleteModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete([FromRoute] int id)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_DELETE);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_DELETE;


                return await _mediator.Send(new DeleteUserCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách người dùng theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<UserBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] UserQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetFilterUserQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách người dùng cho combobox
        /// </summary> 
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<UserSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var result = await _mediator.Send(new GetComboboxUserQuery(count, ts));

                return result;
            });
        }

        /// <summary>
        /// Lấy danh sách thông tin hệ người dùng đang đăng nhập được phép truy cập
        /// </summary>
        /// <param name="phanHe">Mã phân hệ</param>
        /// <returns></returns>
        [HttpGet, Route("get-access-lop-current-user")]
        [ProducesResponseType(typeof(ResponseObject<List<UsersAccessHeByUserModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAccessHeLopByUserQuery(string phanHe)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetAccessHeLopByUserQuery(u.UserId));
            });
        }

        /// <summary>
        /// Lấy danh sách quyền người dùng đang đăng nhập
        /// </summary>
        /// <param name="phanHe">Mã phân hệ</param>
        /// <returns></returns>
        [HttpGet, Route("get-permission-current-user")]
        [ProducesResponseType(typeof(ResponseObject<UserPermissionModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserPermissionQuery(string phanHe)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var listPhanHe = await _mediator.Send(new GetComboboxPhanHeQuery());
                var idPhanHe = listPhanHe.FirstOrDefault(x => x.PhanHe == phanHe)?.IdPh;
                if (idPhanHe == null)
                {
                    return new UserPermissionModel();
                }
                return await _mediator.Send(new GetUserPermissionQuery(u.UserId, idPhanHe.Value));
            });
        }

        /// <summary>
        /// Lấy danh sách nhóm người dùng theo người dùng
        /// </summary>
        /// <param name="userId">Id người dùng</param>
        /// <returns></returns>
        [HttpGet, Route("get-role-by-user")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<List<int>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserRoleQuery([FromQuery] int userId)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetRoleOfUserQuery(userId));
            });
        }

        /// <summary>
        /// Cập nhật danh sách nhóm người dùng thuộc người dùng
        /// </summary>
        /// <param name="model">Thông tin cần cập nhật</param>
        /// <returns></returns>
        [HttpPost, Route("update-role-by-user")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_UPDATE_ROLE))]
        [ProducesResponseType(typeof(ResponseObject<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateRoleOfUser([FromBody] RoleOfUserCreateModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new UpdateRoleOfUserCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy người dùng từ token
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("get-current-user")]
        [ProducesResponseType(typeof(ResponseObject<UserModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCurrenUser()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetUserByIdQuery(u.UserId));
            });
        }

        /// <summary>
        /// Gửi mail thông báo tạo mật khẩu mới
        /// </summary>
        /// <param name="model">Thông tin cần cập nhật</param>
        /// <returns></returns>
        [HttpPost, Route("send-mail-forgot-password")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_SEND_MAIL_FORGOT_PASSWORD))]
        [ProducesResponseType(typeof(ResponseObject<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendEmailForgotPassword([FromBody] SendEmailForgotPasswordModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_SENDMAIL_FORGOT_PASS);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_SENDMAIL_FORGOT_PASS;

                model.IsIgnoreCheckCache = true;
                return await _mediator.Send(new SendEmailForgotPasswordCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Gửi mail thông báo tạo mật khẩu mới cho người đùng
        /// </summary>
        /// <param name="model">Thông tin cần cập nhật</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost, Route("send-mail-user-forgot-password")]
        [ProducesResponseType(typeof(ResponseObject<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendEmailUserForgotPassword([FromBody] SendEmailForgotPasswordModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_SENDMAIL_FORGOT_PASS);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_SENDMAIL_FORGOT_PASS;

                model.IsIgnoreCheckCache = false;
                return await _mediator.Send(new SendEmailForgotPasswordCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Gán lớp quản lý cho giảng viên
        /// </summary>
        /// <param name="model">Thông tin cần cập nhật</param>
        /// <returns></returns>
        [HttpPost, Route("assign-class")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_DELETE_ASSIGN_CLASS))]
        [ProducesResponseType(typeof(ResponseObject<bool>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GanLopQuanLy([FromBody] GanLopQuanLyModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_ASSIGN_CLASS);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_ASSIGN_CLASS;
                return await _mediator.Send(new GanLopQuanLyCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa quyền quản lý lớp của giảng viên theo id truyền vào
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("delete-assign-class/{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_DELETE_ASSIGN_CLASS))]
        [ProducesResponseType(typeof(ResponseObject<List<ResponeDeleteModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteLopQuanLy([FromRoute] int id)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                u.SystemLog.ActionCode = nameof(LogConstants.ACTION_USER_DELETE_ASSIGN_CLASS);
                u.SystemLog.ActionName = LogConstants.ACTION_USER_DELETE_ASSIGN_CLASS;


                return await _mediator.Send(new DeleteGanLopQuanLyCommand(id, u.SystemLog));
            });
        }


        /// <summary>
        /// Lấy chi tiết hệ theo id người dùng
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet, Route("access-he/{UserId}")]
        [ProducesResponseType(typeof(ResponseObject<ChucVuModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.QUAN_LY_NGUOI_DUNG_VIEW))]
        public async Task<IActionResult> UsersAccessHeById([FromRoute] int UserId)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetAccessHeByIdUsersQuery(UserId)));
        }
    }
}
