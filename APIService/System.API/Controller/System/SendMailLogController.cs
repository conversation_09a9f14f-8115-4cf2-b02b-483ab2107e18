using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.DataLog;
using Core.Shared;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;

namespace Core.API
{
    /// <summary>
    /// Module nhật ký gửi mail
    /// </summary>
    [ApiController]
    [Route("system/v1/send-mail-log")]
    [ApiExplorerSettings(GroupName = "100. SendMail Log (Nhật ký gửi mail)")]
    [Authorize]
    public class SendMailLogController : ApiControllerBase
    {
        public SendMailLogController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        //[HttpPost, Route("")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public async Task<IActionResult> Create([FromBody] SendMailLog model)
        //{
        //    return await ExecuteFunction(async (RequestUser u) =>
        //    {
        //        return await _mediator.Send(new SendMailLogCreateCommand(model));
        //    });
        //}

        //[HttpPut, Route("")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public async Task<IActionResult> Update([FromBody] SendMailLog model)
        //{
        //    return await ExecuteFunction(async (RequestUser u) =>
        //    {
        //        return await _mediator.Send(new SendMailLogUpdateCommand(model));
        //    });
        //}

        [HttpGet, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.SEND_MAIL_LOG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<SendMailLog>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById([FromQuery] string id)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SendMailLogGetByIdQuery(id));
            });
        }

        //[HttpDelete, Route("")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public async Task<IActionResult> Detele([FromBody] List<string> listId)
        //{
        //    return await ExecuteFunction(async (RequestUser u) =>
        //    {
        //        return await _mediator.Send(new SendMailLogDeleteCommand(listId));
        //    });
        //}

        /// <summary>
        /// Lấy danh sách nhật ký gửi mail theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns>Danh sách nhật ký gửi mail</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.SEND_MAIL_LOG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<List<SendMailLog>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] SendMailLogQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SendMailLogFilterQuery(filter));
            });
        }
    }
}
