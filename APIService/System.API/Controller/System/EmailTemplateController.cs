using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Shared.ContextAccessor;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/email-template")]
    [ApiExplorerSettings(GroupName = "08. EmailTemplate (Mẫu email)")]
    [Authorize]
    public class EmailTemplateController : ApiControllerBaseV2
    {
        public EmailTemplateController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        #region CRUD
        /// <summary>
        /// Thêm mới mẫu email
        /// </summary>
        /// <param name="model">Thông tin mẫu email</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.EMAIL_TEMPLATE_ADD))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateEmailTemplateModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_EMAIL_TEMPLATE_CREATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_EMAIL_TEMPLATE_CREATE;

                return await _mediator.Send(new CreateEmailTemplateCommand(model));
            });
        }

        /// <summary>
        /// Lấy mẫu email theo id
        /// </summary>
        /// <param name="id">Id mẫu email</param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.EMAIL_TEMPLATE_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<EmailTemplateModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetEmailTemplateByIdQuery(id));
            });
        }

        /// <summary>
        /// Cập nhật mẫu email
        /// </summary>
        /// <param name="model">Thông tin mẫu email cần cập nhật</param>
        /// <param name="id">Id mẫu email</param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.EMAIL_TEMPLATE_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] UpdateEmailTemplateModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_EMAIL_TEMPLATE_UPDATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_EMAIL_TEMPLATE_UPDATE;

                model.Id = id;
                return await _mediator.Send(new UpdateEmailTemplateCommand(model));
            });
        }

        ///// <summary>
        ///// Xóa mẫu email
        ///// </summary>
        ///// <param name="id">id mẫu email cần xóa</param>
        ///// <returns></returns>
        //[HttpDelete, Route("{id}")]
        ////[ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.EMAIL_TEMPLATE_DELETE))]
        //[ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> Delete([FromRoute] int id)
        //{
        //    return await ExecuteFunction(async () =>
        //    {
        //        _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_EMAIL_TEMPLATE_DELETE);
        //        _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_EMAIL_TEMPLATE_DELETE;

        //        return await _mediator.Send(new DeleteEmailTemplateCommand(id));
        //    });
        //}

        /// <summary>
        /// Lấy danh sách mẫu email theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.EMAIL_TEMPLATE_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<EmailTemplateBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] EmailTemplateQueryFilter filter)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetFilterEmailTemplateQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách mẫu email cho combobox
        /// </summary> 
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<EmailTemplateSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                var result = await _mediator.Send(new GetComboboxEmailTemplateQuery(count, ts));

                return result;
            });
        }
        #endregion

        ///// <summary>
        ///// Gửi email theo template
        ///// </summary> 
        ///// <param name="model">Thông tin gửi mail</param>
        ///// <response code="200">Thành công</response>
        //[HttpPost, Route("send-mail")]
        //[ProducesResponseType(typeof(ResponseObject<bool>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> SendMail([FromBody] SendMailUsingTemplateModel model)
        //{
        //    return await ExecuteFunction(async (RequestUser u) =>
        //    {
        //        model.Data = new LoginModel()
        //        {
        //            Username = "Test UserName"
        //        };
        //        var result = await _mediator.Send(new SenMailUsingEmailTemplateQuery(model, u.SystemLog));

        //        return result;
        //    });
        //}
    }
}
