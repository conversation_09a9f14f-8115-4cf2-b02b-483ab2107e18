using System;
using System.IO;
using System.Net.Mime;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Business;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using MimeMapping;

namespace API.Controller
{
    [ApiController]
    [Route("system/v1/file")]
    [ApiExplorerSettings(GroupName = "10. File Attachment")]
    [Authorize]
    public class FileController : ApiControllerBaseV2
    {
        public FileController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        /// <summary>
        /// Upload file
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="source"><PERSON><PERSON><PERSON> l<PERSON> tr<PERSON> (mặc định là lưu vào server)</param>
        /// <param name="file">File upoad</param>
        /// <returns></returns>
        [HttpPost]
        [Route("upload-object")]
        [ProducesResponseType(typeof(ResponseObject<MinIOFileUploadResult>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UploadObject(IFormFile file, [FromQuery] string bucketName = "", [FromQuery] short source = 0)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new UploadFileAttachmentCommand(file, bucketName, source));
            });
        }

        /// <summary>
        /// Download file base64
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("download-base64/{id}")]
        [ProducesResponseType(typeof(ResponseObject<Base64FileDataResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFileAsBase64(Guid id)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new DownloadFileAttachmentByIdQuery(id));
            });
        }

        /// <summary>
        /// Download file
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("download-object/{id}")]
        [ProducesResponseType(typeof(FileStreamResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> DownloadObject(Guid id)
        {
            var fileData = await _mediator.Send(new DownloadFileAttachmentByIdQuery(id));

            if (fileData == null)
            {
                return NotFound("File không tồn tại");
            }

            byte[] fileBytes = Convert.FromBase64String(fileData.FileData);

            MemoryStream memory = new MemoryStream(fileBytes);

            var cd = new ContentDisposition
            {
                FileName = Utils.RemoveVietnameseSign(fileData.FileName),
                Inline = true
            };
            Response.Headers.Add("Content-Disposition", cd.ToString());

            return File(memory, MimeUtility.GetMimeMapping(fileData.FileName));
        }

        /// <summary>
        /// Delete file
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<Base64FileDataResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete([FromRoute] Guid id)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new DeleteFileDinhKemCommand(id, true));
            });
        }
    }
}
