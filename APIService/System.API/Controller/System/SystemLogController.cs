using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Business;
using Core.DataLog;
using Core.Shared;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;

namespace Core.API
{
    /// <summary>
    /// Module nhật ký hệ thống
    /// </summary>
    [ApiController]
    [Route("system/v1/system-log")]
    [ApiExplorerSettings(GroupName = "99. System Log (Nhật ký hệ thống)")]
    [Authorize]
    public class SystemLogController : ApiControllerBase
    {
        public SystemLogController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        //[HttpPost, Route("")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public async Task<IActionResult> Create([FromBody] SystemLog model)
        //{
        //    return await ExecuteFunction(async (RequestUser u) =>
        //    {
        //        return await _mediator.Send(new SystemLogCreateCommand(model));
        //    });
        //}

        //[HttpPut, Route("")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public async Task<IActionResult> Update([FromBody] SystemLog model)
        //{
        //    return await ExecuteFunction(async (RequestUser u) =>
        //    {
        //        return await _mediator.Send(new SystemLogUpdateCommand(model));
        //    });
        //}

        [HttpGet, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHAT_KY_HE_THONG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<SystemLog>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById([FromQuery] string id)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SystemLogGetByIdQuery(id));
            });
        }

        //[HttpDelete, Route("")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public async Task<IActionResult> Detele([FromBody] List<string> listId)
        //{
        //    return await ExecuteFunction(async (RequestUser u) =>
        //    {
        //        return await _mediator.Send(new SystemLogDeleteCommand(listId));
        //    });
        //}

        /// <summary>
        /// Lấy danh sách nhật ký hệ thống theo điều kiện lọc
        /// </summary> 
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns>Danh sách nhật ký hệ thống</returns> 
        /// <response code="200">Thành công</response>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NHAT_KY_HE_THONG_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<List<SystemLog>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] SystemLogQueryFilter filter)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SystemLogFilterQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách Action Code For Combobox
        /// </summary>      
        /// <returns>Danh sách Action Code và Action Name</returns>    
        [HttpGet, Route("get-all-action-code-for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ActionCodeForComboboxModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetActionCodeForCombobox()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SystemLogGetActionCodeForComboboxQuery());
            });
        }
    }
}
