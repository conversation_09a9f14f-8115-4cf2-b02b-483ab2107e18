using Core.API.Shared;
using Core.Business;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/api-key")]
    [ApiExplorerSettings(GroupName = "13. ApiKey (Quản lý API Key)")]
    [Authorize]
    public class ApiKeyController : ApiControllerBaseV2
    {
        public ApiKeyController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        #region CRUD

        /// <summary>
        /// Thêm mới API Key
        /// </summary>
        /// <param name="model">Thông tin API Key</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.API_KEY_ADD))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateApiKeyModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_API_KEY_CREATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_API_KEY_CREATE;

                return await _mediator.Send(new CreateApiKeyCommand(model));
            });
        }

        /// <summary>
        /// Cập nhật API Key
        /// </summary>
        /// <param name="model">Thông tin API Key</param>
        /// <param name="id">Id API Key</param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.API_KEY_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] UpdateApiKeyModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_API_KEY_UPDATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_API_KEY_UPDATE;

                model.Id = id;
                return await _mediator.Send(new UpdateApiKeyCommand(model));
            });
        }

        /// <summary>
        /// Xóa API Key
        /// </summary>
        /// <param name="id">Id API Key</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.API_KEY_DELETE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_API_KEY_DELETE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_API_KEY_DELETE;

                return await _mediator.Send(new DeleteApiKeyCommand(id));
            });
        }

        /// <summary>
        /// Vô hiệu hóa API Key
        /// </summary>
        /// <param name="id">Id API Key</param>
        /// <returns></returns>
        [HttpPut, Route("{id}/revoke")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.API_KEY_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Revoke(int id)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_API_KEY_UPDATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_API_KEY_UPDATE;

                return await _mediator.Send(new RevokeApiKeyCommand(id));
            });
        }

        /// <summary>
        /// Lấy thông tin API Key theo Id
        /// </summary>
        /// <param name="id">Id API Key</param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.API_KEY_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<ApiKeyModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(int id)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetApiKeyByIdQuery(id));
            });
        }

        /// <summary>
        /// Lấy danh sách API Key có phân trang
        /// </summary>
        /// <param name="filter">Bộ lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.API_KEY_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ApiKeyBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromBody] ApiKeyQueryFilter filter)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetFilterApiKeyQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách API Key cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        /// <param name="userId">ID người dùng</param>
        /// <returns></returns>
        [HttpGet, Route("combobox")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.API_KEY_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<List<ApiKeySelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCombobox(int count = 0, string textSearch = "", int? userId = null)
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetComboboxApiKeyQuery(count, textSearch, userId));
            });
        }
        #endregion
    }
}
