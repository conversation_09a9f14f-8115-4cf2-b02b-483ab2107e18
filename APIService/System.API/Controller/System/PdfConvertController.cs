using Core.API.Shared;
using Core.Business.System.Pdf;
using Core.Shared;
using Core.Shared.ContextAccessor;
using Core.Shared.Model;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;

namespace System.API.Controller
{
    [ApiController]
    [Route("system/v1/pdf-convert")]
    [ApiExplorerSettings(GroupName = "100. Pdf Convert")]
    public class PdfConvertController : ApiControllerBaseV2
    {
        public PdfConvertController(
          Func<IContextAccessor> contextAccessorFactory,
          IMediator mediator,
          IStringLocalizer<Core.Shared.Resources> localizer,
          IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        /// <summary>
        /// Convert pdf from word base64
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("word-metadata-to-pdf")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ConvertPdfFromWordBase64(PdfConvertFromWordBase64Model request)
        {
            return await ExecuteFunction(async () =>
            {
                // Đọc file từ thư mục và convert về base64
                return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));
            });
        }
    }
}
