using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/bo-mon")]
    [ApiExplorerSettings(GroupName = "47. Bộ môn")]
    [Authorize]
    public class BoMonController : ApiControllerBase
    {
        public BoMonController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách bộ môn cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<BoMonSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxBoMonQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách bộ môn có lọc
        /// </summary>
        /// <param name="filter"></param>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<BoMonBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_VIEW))]
        public async Task<IActionResult> Filter([FromBody] BoMonFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterBoMonQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết bộ môn
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<BoMonModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetBoMonByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới bộ môn
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateBoMonModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BO_MON_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BO_MON_CREATE;


                return await _mediator.Send(new CreateBoMonCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel bộ môn
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyBoMonModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BO_MON_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BO_MON_CREATE_MANY;


                return await _mediator.Send(new CreateManyBoMonCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa bộ môn
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateBoMonModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BO_MON_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BO_MON_UPDATE;
                return await _mediator.Send(new UpdateBoMonCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa bộ môn
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BO_MON_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BO_MON_DELETE;

                return await _mediator.Send(new DeleteBoMonCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa nhiều bộ môn
        /// </summary>
        /// <param name="listId"></param>
        /// <returns></returns>
        [HttpPost, Route("delete-many")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_DELETE))]
        public async Task<IActionResult> DeleteMany(List<int> listId)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BO_MON_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BO_MON_DELETE;

                return await _mediator.Send(new DeleteManyBoMonCommand(listId, u.SystemLog));
            });
        }


        /// <summary>
        /// Lấy danh sách giảng viên theo bộ môn
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("giang-vien-filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<GiangVienBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_VIEW))]
        public async Task<IActionResult> GiangVienFilter([FromBody] GiangVienFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterGiangVienTheoBoMonQuery(filter)));
        }



        /// <summary>
        /// Lấy danh sách giảng viên chưa gán bộ môn
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("giang-vien-chua-gan-filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<GiangVienBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_VIEW))]
        public async Task<IActionResult> GiangVienChuaGanFilter([FromBody] GiangVienFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterGiangVienChuaGanBoMonQuery(filter)));
        }


        /// <summary>
        /// Lấy danh sách môn học theo bộ môn
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("mon-hoc-filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<MonHocBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_VIEW))]
        public async Task<IActionResult> MonHocFilter([FromBody] MonHocModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterMonHocTheoBoMonQuery(filter)));
        }


        /// <summary>
        /// Lấy danh sách môn học Chưa gán bộ môn
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("mon-hoc-chua-gan-filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<MonHocBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_VIEW))]
        public async Task<IActionResult> MonHocChuaGanFilter([FromBody] MonHocModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterMonHocChuaGanBoMonQuery(filter)));
        }


        /// <summary>
        /// Xóa môn học khỏi bộ môn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("update-mon-hoc-bo-mon")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_DELETE))]
        public async Task<IActionResult> UpdateMonHocBoMon([FromBody] UpdateMonHocModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BO_MON_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BO_MON_UPDATE;
                return await _mediator.Send(new UpdateMonHocBoMonCommand(request, u.SystemLog));
            });
        }


        /// <summary>
        /// Thêm mới bộ môn giảng viên
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-bo-mon-giang-vien")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_ADD))]
        public async Task<IActionResult> CreateBoMonGiangVien([FromBody] CreateBoMonGiangVienModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BO_MON_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BO_MON_CREATE;


                return await _mediator.Send(new CreateBoMonGiangVienCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Xóa bộ môn giảng viên
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("delete-bo-mon-giang-vien")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.BO_MON_ADD))]
        public async Task<IActionResult> DeleteBoMonGiangVien([FromBody] DeleteBoMonGiangVienModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_BO_MON_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_BO_MON_DELETE;


                return await _mediator.Send(new DeleteBoMonGiangVienCommand(model, u.SystemLog));
            });
        }

    }
}
