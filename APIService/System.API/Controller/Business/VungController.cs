using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/vung")]
    [ApiExplorerSettings(GroupName = "501. Vùng")]
    [Authorize]
    public class VungController : ApiControllerBase
    {
        public VungController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }


        /// <summary>
        /// Thêm mới vùng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VUNG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateVungModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_VUNG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_VUNG_CREATE;


                return await _mediator.Send(new CreateVungCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa vùng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VUNG_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateVungModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_VUNG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_VUNG_UPDATE;
                return await _mediator.Send(new UpdateVungCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa vùng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VUNG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_VUNG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_VUNG_DELETE;

                return await _mediator.Send(new DeleteVungCommand(id, u.SystemLog));
            });
        }

        /// <summary>
        /// Lấy danh sách vùng cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<VungSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxVungQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách vùng có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<VungBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VUNG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] VungFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterVungQuery(filter)));
        }

        /// <summary>
        /// Lấy chi tiết vùng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<VungModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.VUNG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetVungByIdQuery(id)));
        }

    }
}
