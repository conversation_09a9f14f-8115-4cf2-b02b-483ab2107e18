using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/loai-thanh-phan-diem")]
    [ApiExplorerSettings(GroupName = "66. Loại thành phần điềm")]
   // [Authorize]
    public class LoaiThanhPhanDiemController : ApiControllerBase
    {
        public LoaiThanhPhanDiemController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách Loại điểm thành phần cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<LoaiThanhPhanDiemSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxLoaiThanhPhanDiemQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Loại điểm thành phần có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<LoaiThanhPhanDiemBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_DIEM_THANH_PHAN_VIEW))]
        public async Task<IActionResult> Filter([FromBody] LoaiThanhPhanDiemFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterLoaiThanhPhanDiemQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Loại điểm thành phần
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<LoaiThanhPhanDiemModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_DIEM_THANH_PHAN_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetLoaiThanhPhanDiemByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Loại điểm thành phần
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_DIEM_THANH_PHAN_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateLoaiThanhPhanDiemModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_DIEM_THANH_PHAN_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_DIEM_THANH_PHAN_CREATE;


                return await _mediator.Send(new CreateLoaiThanhPhanDiemCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Loại điểm thành phần
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_DIEM_THANH_PHAN_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyLoaiThanhPhanDiemModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_DIEM_THANH_PHAN_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_DIEM_THANH_PHAN_CREATE_MANY;


                return await _mediator.Send(new CreateManyLoaiThanhPhanDiemCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Loại điểm thành phần
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_DIEM_THANH_PHAN_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateLoaiThanhPhanDiemModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_DIEM_THANH_PHAN_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_DIEM_THANH_PHAN_UPDATE;
                return await _mediator.Send(new UpdateLoaiThanhPhanDiemCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Loại điểm thành phần
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_DIEM_THANH_PHAN_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_DIEM_THANH_PHAN_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_DIEM_THANH_PHAN_DELETE;

                return await _mediator.Send(new DeleteLoaiThanhPhanDiemCommand(id, u.SystemLog));
            });
        }

    }
}
