using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/chuong-trinh-dao-tao-kien-thuc")]
    [ApiExplorerSettings(GroupName = "62. Chương trình đào tạo kiến thức")]
    [Authorize]
    public class ChuongTrinhDaoTaoKienThucController : ApiControllerBase
    {
        public ChuongTrinhDaoTaoKienThucController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách chương trình đào tạo kiến thức cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ChuongTrinhDaoTaoKienThucSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxChuongTrinhDaoTaoKienThucQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách chương trình đào tạo kiến thức có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ChuongTrinhDaoTaoKienThucBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_KIEN_THUC_VIEW))]
        public async Task<IActionResult> Filter([FromBody] ChuongTrinhDaoTaoKienThucFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterChuongTrinhDaoTaoKienThucQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết chương trình đào tạo kiến thức
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<ChuongTrinhDaoTaoKienThucModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_KIEN_THUC_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetChuongTrinhDaoTaoKienThucByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới chương trình đào tạo kiến thức
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_KIEN_THUC_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateChuongTrinhDaoTaoKienThucModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_CREATE;


                return await _mediator.Send(new CreateChuongTrinhDaoTaoKienThucCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel chương trình đào tạo kiến thức
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_KIEN_THUC_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyChuongTrinhDaoTaoKienThucModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_CREATE_MANY;


                return await _mediator.Send(new CreateManyChuongTrinhDaoTaoKienThucCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa chương trình đào tạo kiến thức
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_KIEN_THUC_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateChuongTrinhDaoTaoKienThucModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_UPDATE;
                return await _mediator.Send(new UpdateChuongTrinhDaoTaoKienThucCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa chương trình đào tạo kiến thức
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUONG_TRINH_DAO_TAO_KIEN_THUC_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUONG_TRINH_DAO_TAO_KIEN_THUC_DELETE;

                return await _mediator.Send(new DeleteChuongTrinhDaoTaoKienThucCommand(id, u.SystemLog));
            });
        }

    }
}
