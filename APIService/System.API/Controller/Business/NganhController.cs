using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/Nganh")]
    [ApiExplorerSettings(GroupName = "21. Ngành")]
    [Authorize]
    public class NganhController : ApiControllerBase
    {
        public NganhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách ngành cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<NganhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxNganhQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách ngành có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<NganhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NGANH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] NganhFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterNganhQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết ngành
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<NganhModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NGANH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetNganhByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới ngành
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NGANH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateNganhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NGANH_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NGANH_CREATE;


                return await _mediator.Send(new CreateNganhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel ngành
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NGANH_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyNganhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NGANH_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NGANH_CREATE_MANY;


                return await _mediator.Send(new CreateManyNganhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa ngành
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NGANH_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateNganhModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NGANH_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NGANH_UPDATE;
                return await _mediator.Send(new UpdateNganhCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa ngành
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NGANH_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NGANH_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NGANH_DELETE;

                return await _mediator.Send(new DeleteNganhCommand(id, u.SystemLog));
            });
        }

    }
}
