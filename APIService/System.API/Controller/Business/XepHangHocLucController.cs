using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xep-hang-hoc-luc")]
    [ApiExplorerSettings(GroupName = "69. Xếp hạng học lực")]
    [Authorize]
    public class XepHangHocLucController : ApiControllerBase
    {
        public XepHangHocLucController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách Xếp hạng học lực cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepHangHocLucSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepHangHocLucQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Xếp hạng học lực có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XepHangHocLucBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_HOC_LUC_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XepHangHocLucFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXepHangHocLucQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Xếp hạng học lực
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XepHangHocLucModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_HOC_LUC_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXepHangHocLucByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Xếp hạng học lực
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_HOC_LUC_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXepHangHocLucModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_HOC_LUC_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_HOC_LUC_CREATE;


                return await _mediator.Send(new CreateXepHangHocLucCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Xếp hạng học lực
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_HOC_LUC_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXepHangHocLucModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_HOC_LUC_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_HOC_LUC_CREATE_MANY;


                return await _mediator.Send(new CreateManyXepHangHocLucCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Xếp hạng học lực
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_HOC_LUC_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateXepHangHocLucModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_HOC_LUC_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_HOC_LUC_UPDATE;
                return await _mediator.Send(new UpdateXepHangHocLucCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Xếp hạng học lực
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_HOC_LUC_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_HOC_LUC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_HOC_LUC_DELETE;

                return await _mediator.Send(new DeleteXepHangHocLucCommand(id, u.SystemLog));
            });
        }

    }
}
