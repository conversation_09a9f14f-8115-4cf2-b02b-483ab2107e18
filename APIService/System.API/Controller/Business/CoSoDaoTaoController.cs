using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/co-so-dao-tao")]
    [ApiExplorerSettings(GroupName = "46. Cơ sở đào tạo")]
    [Authorize]
    public class CoSoDaoTaoController : ApiControllerBase
    {
        public CoSoDaoTaoController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách cơ sở đào tạo cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<CoSoDaoTaoSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxCoSoDaoTaoQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách cơ sở đào tạo có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<CoSoDaoTaoBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CO_SO_DAO_TAO_VIEW))]
        public async Task<IActionResult> Filter([FromBody] CoSoDaoTaoFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterCoSoDaoTaoQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết cơ sở đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<CoSoDaoTaoModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CO_SO_DAO_TAO_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetCoSoDaoTaoByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới cơ sở đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CO_SO_DAO_TAO_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateCoSoDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CO_SO_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CO_SO_DAO_TAO_CREATE;


                return await _mediator.Send(new CreateCoSoDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel cơ sở đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CO_SO_DAO_TAO_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyCoSoDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CO_SO_DAO_TAO_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CO_SO_DAO_TAO_CREATE_MANY;


                return await _mediator.Send(new CreateManyCoSoDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa cơ sở đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CO_SO_DAO_TAO_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateCoSoDaoTaoModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CO_SO_DAO_TAO_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CO_SO_DAO_TAO_UPDATE;
                return await _mediator.Send(new UpdateCoSoDaoTaoCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa cơ sở đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CO_SO_DAO_TAO_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CO_SO_DAO_TAO_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CO_SO_DAO_TAO_DELETE;

                return await _mediator.Send(new DeleteCoSoDaoTaoCommand(id, u.SystemLog));
            });
        }

    }
}
