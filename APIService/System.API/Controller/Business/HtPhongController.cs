using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/phong")]
    [ApiExplorerSettings(GroupName = "81. Phòng")]
    [Authorize]
    public class PhongController : ApiControllerBase
    {
        public PhongController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách phòng cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<PhongSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxPhongQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách phòng có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<PhongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] PhongFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterPhongQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết phòng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PhongModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetPhongByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới phòng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreatePhongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHONG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHONG_CREATE;


                return await _mediator.Send(new CreatePhongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel phòng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyPhongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHONG_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHONG_CREATE_MANY;


                return await _mediator.Send(new CreateManyPhongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa phòng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdatePhongModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHONG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHONG_UPDATE;
                return await _mediator.Send(new UpdatePhongCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa phòng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHONG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHONG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHONG_DELETE;

                return await _mediator.Send(new DeletePhongCommand(id, u.SystemLog));
            });
        }

    }
}
