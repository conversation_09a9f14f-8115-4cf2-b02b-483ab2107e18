using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xu-ly-ky-luat")]
    [ApiExplorerSettings(GroupName = "32. Xử lý kỷ luật")]
    [Authorize]
    public class XuLyKyLuatController : ApiControllerBase
    {
        public XuLyKyLuatController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách xử lý kỷ luật cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XuLySelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXuLyQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách xử lý kỷ luật có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XuLyBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XU_LY_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XuLyFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXuLyQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết xử lý kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XuLyModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XU_LY_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXuLyByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới xử lý kỷ luật
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XU_LY_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXuLyModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XU_LY_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XU_LY_CREATE;


                return await _mediator.Send(new CreateXuLyCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel xử lý kỷ luật
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XU_LY_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXuLyModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XU_LY_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XU_LY_CREATE_MANY;


                return await _mediator.Send(new CreateManyXuLyCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa xử lý kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XU_LY_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateXuLyModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XU_LY_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XU_LY_UPDATE;
                return await _mediator.Send(new UpdateXuLyCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa xử lý kỷ luật
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XU_LY_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XU_LY_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XU_LY_DELETE;

                return await _mediator.Send(new DeleteXuLyCommand(id, u.SystemLog));
            });
        }

    }
}
