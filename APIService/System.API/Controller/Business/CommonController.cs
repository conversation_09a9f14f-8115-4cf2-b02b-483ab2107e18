using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/common")]
    [ApiExplorerSettings(GroupName = "17. Common")]
    [Authorize]

    public class CommonController : ApiControllerBase
    {
        public CommonController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// L<PERSON>y học kỳ hiện tại
        /// </summary>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("get-hoc-ky")]
        [ProducesResponseType(typeof(ResponseObject<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetHocKyHienTai()
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHocKyHienTaiQuery()));
        }

        /// <summary>
        /// Lấy năm học hiện tại
        /// </summary>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("get-nam-hoc")]
        [ProducesResponseType(typeof(ResponseObject<int>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetNamHocHienTai()
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetNamHocHienTaiQuery()));
        }

        /// <summary>
        /// Lấy danh sách khóa học cho combobox
        /// </summary>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("nam-hoc-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<NamHocSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListNamHocCombobox()
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetComboboxNamHocQuery()));
        }

        /// <summary>
        /// Lấy mã hóa Thiên An
        /// </summary>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("get-ma-hoa-ta")]
        [ProducesResponseType(typeof(ResponseObject<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetMaHoaTA()
        {
            return await ExecuteFunction(u => Utils.Base64DecodeTA(Utils.EncryptTA(u.MaCB)));
        }
    }
}
