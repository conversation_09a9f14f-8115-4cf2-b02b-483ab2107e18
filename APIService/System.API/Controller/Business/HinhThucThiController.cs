using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/hinh-thuc-thi")]
    [ApiExplorerSettings(GroupName = "49. Hình thức thi")]
    [Authorize]
    public class HinhThucThiController : ApiControllerBase
    {
        public HinhThucThiController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách hình thức thi cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HinhThucThiSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHinhThucThiQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách hình thức thi có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<HinhThucThiBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_THI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] HinhThucThiFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterHinhThucThiQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết hình thức thi
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<HinhThucThiModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_THI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHinhThucThiByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới hình thức thi
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_THI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateHinhThucThiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HINH_THUC_THI_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HINH_THUC_THI_CREATE;


                return await _mediator.Send(new CreateHinhThucThiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel hình thức thi
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_THI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyHinhThucThiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HINH_THUC_THI_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HINH_THUC_THI_CREATE_MANY;


                return await _mediator.Send(new CreateManyHinhThucThiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa hình thức thi
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_THI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateHinhThucThiModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HINH_THUC_THI_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HINH_THUC_THI_UPDATE;
                return await _mediator.Send(new UpdateHinhThucThiCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa hình thức thi
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HINH_THUC_THI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HINH_THUC_THI_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HINH_THUC_THI_DELETE;

                return await _mediator.Send(new DeleteHinhThucThiCommand(id, u.SystemLog));
            });
        }

    }
}
