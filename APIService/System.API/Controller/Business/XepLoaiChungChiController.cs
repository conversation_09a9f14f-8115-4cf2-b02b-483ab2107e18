using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xep-loai-chung-chi")]
    [ApiExplorerSettings(GroupName = "74. Xếp loại chúng chỉ")]
    [Authorize]
    public class XepLoaiChungChiController : ApiControllerBase
    {
        public XepLoaiChungChiController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách Xếp loại chúng chỉ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepLoaiChungChiSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepLoaiChungChiQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Xếp loại chúng chỉ có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XepLoaiChungChiBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_CHUNG_CHI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XepLoaiChungChiFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXepLoaiChungChiQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Xếp loại chúng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XepLoaiChungChiModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_CHUNG_CHI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXepLoaiChungChiByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Xếp loại chúng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_CHUNG_CHI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXepLoaiChungChiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_CHUNG_CHI_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_CHUNG_CHI_CREATE;


                return await _mediator.Send(new CreateXepLoaiChungChiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Xếp loại chúng chỉ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_CHUNG_CHI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXepLoaiChungChiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_CHUNG_CHI_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_CHUNG_CHI_CREATE_MANY;


                return await _mediator.Send(new CreateManyXepLoaiChungChiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Xếp loại chúng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_CHUNG_CHI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateXepLoaiChungChiModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_CHUNG_CHI_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_CHUNG_CHI_UPDATE;
                return await _mediator.Send(new UpdateXepLoaiChungChiCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Xếp loại chúng chỉ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_CHUNG_CHI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_CHUNG_CHI_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_CHUNG_CHI_DELETE;

                return await _mediator.Send(new DeleteXepLoaiChungChiCommand(id, u.SystemLog));
            });
        }

    }
}
