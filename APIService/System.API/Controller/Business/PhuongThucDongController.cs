using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/phuong-thuc-dong")]
    [ApiExplorerSettings(GroupName = "114. Phương thức đóng")]
    [Authorize]
    public class PhuongThucDongController : ApiControllerBase
    {
        public PhuongThucDongController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách phương thức đóng cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<PhuongThucDongSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxPhuongThucDongQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách phương thức đóng có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<PhuongThucDongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_THUC_DONG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] PhuongThucDongQueryFilter filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterPhuongThucDongQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết bênh viện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PhuongThucDongModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_THUC_DONG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetPhuongThucDongByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới bênh viện
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_THUC_DONG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreatePhuongThucDongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_THUC_DONG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_THUC_DONG_CREATE;


                return await _mediator.Send(new CreatePhuongThucDongCommand(model, u));
            });
        }


        /// <summary>
        /// Sửa bênh viện
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{request.id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_THUC_DONG_EDIT))]
        public async Task<IActionResult> Update([FromBody] UpdatePhuongThucDongModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_THUC_DONG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_THUC_DONG_UPDATE;
                return await _mediator.Send(new UpdatePhuongThucDongCommand(request, u));
            });
        }

        /// <summary>
        /// Xóa bênh viện
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_THUC_DONG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_THUC_DONG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_THUC_DONG_DELETE;

                return await _mediator.Send(new DeletePhuongThucDongCommand(id, u));
            });
        }

    }
}
