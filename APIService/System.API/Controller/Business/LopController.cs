using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/lop")]
    [ApiExplorerSettings(GroupName = "48. Lớp")]
    [Authorize]
    public class LopController : ApiControllerBase
    {
        public LopController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách lớp cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<LopSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxLopQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách lớp có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<LopBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOP_VIEW))]
        public async Task<IActionResult> Filter([FromBody] LopFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterLopQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết lớp
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<LopModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOP_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetLopByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới lớp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOP_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateLopModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOP_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOP_CREATE;


                return await _mediator.Send(new CreateLopCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa lớp
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOP_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateLopModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOP_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOP_UPDATE;
                return await _mediator.Send(new UpdateLopCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa lớp
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOP_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOP_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOP_DELETE;

                return await _mediator.Send(new DeleteLopCommand(id, u.SystemLog));
            });
        }

    }
}
