using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/loai-quyet-dinh")]
    [ApiExplorerSettings(GroupName = "44. Loại quyết định")]
    [Authorize]
    public class LoaiQuyetDinhController : ApiControllerBase
    {
        public LoaiQuyetDinhController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách loại quyết định cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<LoaiQuyetDinhSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxLoaiQuyetDinhQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách loại quyết định có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<LoaiQuyetDinhBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_QUYET_DINH_VIEW))]
        public async Task<IActionResult> Filter([FromBody] LoaiQuyetDinhFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterLoaiQuyetDinhQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết loại quyết định
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<LoaiQuyetDinhModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_QUYET_DINH_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetLoaiQuyetDinhByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới loại quyết định
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_QUYET_DINH_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateLoaiQuyetDinhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_QUYET_DINH_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_QUYET_DINH_CREATE;


                return await _mediator.Send(new CreateLoaiQuyetDinhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel loại quyết định
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_QUYET_DINH_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyLoaiQuyetDinhModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_QUYET_DINH_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_QUYET_DINH_CREATE_MANY;


                return await _mediator.Send(new CreateManyLoaiQuyetDinhCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa loại quyết định
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_QUYET_DINH_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateLoaiQuyetDinhModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_QUYET_DINH_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_QUYET_DINH_UPDATE;
                return await _mediator.Send(new UpdateLoaiQuyetDinhCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa loại quyết định
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.LOAI_QUYET_DINH_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_LOAI_QUYET_DINH_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_LOAI_QUYET_DINH_DELETE;

                return await _mediator.Send(new DeleteLoaiQuyetDinhCommand(id, u.SystemLog));
            });
        }

    }
}
