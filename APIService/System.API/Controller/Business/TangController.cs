using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/tang")]
    [ApiExplorerSettings(GroupName = "82. Tầng")]
    [Authorize]
    public class TangController : ApiControllerBase
    {
        public TangController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách tầng cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<TangSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxTangQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách tầng có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<TangBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TANG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] TangFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterTangQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết tầng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<TangModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TANG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetTangByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới tầng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TANG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateTangModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TANG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TANG_CREATE;


                return await _mediator.Send(new CreateTangCommand(model, u.SystemLog));
            });
        }


        /// <summary>
        /// Sửa tầng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TANG_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateTangModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TANG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TANG_UPDATE;
                return await _mediator.Send(new UpdateTangCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa tầng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.TANG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_TANG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_TANG_DELETE;

                return await _mediator.Send(new DeleteTangCommand(id, u.SystemLog));
            });
        }

    }
}
