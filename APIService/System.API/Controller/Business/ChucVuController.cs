using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/chuc-vu")]
    [ApiExplorerSettings(GroupName = "35. Chức vụ")]
    [Authorize]
    public class ChucVuController : ApiControllerBase
    {
        public ChucVuController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách chức vụ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ChucVuSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxChucVuQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách chức vụ có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ChucVuBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_VU_VIEW))]
        public async Task<IActionResult> Filter([FromBody] ChucVuFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterChucVuQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết chức vụ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<ChucVuModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_VU_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetChucVuByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới chức vụ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_VU_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateChucVuModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUC_VU_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUC_VU_CREATE;


                return await _mediator.Send(new CreateChucVuCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel chức vụ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_VU_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyChucVuModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUC_VU_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUC_VU_CREATE_MANY;


                return await _mediator.Send(new CreateManyChucVuCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa chức vụ
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_VU_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateChucVuModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUC_VU_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUC_VU_UPDATE;
                return await _mediator.Send(new UpdateChucVuCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa chức vụ
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUC_VU_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_CHUC_VU_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_CHUC_VU_DELETE;

                return await _mediator.Send(new DeleteChucVuCommand(id, u.SystemLog));
            });
        }

    }
}
