using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Business.System;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/phan-he")]
    [ApiExplorerSettings(GroupName = "01. Phân hệ")]
    [Authorize]
    public class PhanHeController : ApiControllerBase
    {
        public PhanHeController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// Lấy danh sách phân hệ được phân quyền cho người dùng đang đăng nhập
        /// </summary>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("authorized-for-users")]
        [ProducesResponseType(typeof(ResponseObject<List<PhanHeModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListPhanHeAuthorizedByUserIdQuery()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetListPhanHeAuthorizedByUserIdQuery(u.UserId));
            });
        }

        /// <summary>
        /// Lấy tất cả danh sách phân hệ
        /// </summary>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("all-active")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ResponseObject<List<PhanHeSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetComboboxPhanHeQuery()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxPhanHeQuery(0, ""));
            });
        }

        /// <summary>
        /// Lấy danh sách phân hệ cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<PhanHeSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetComboboxPhanHeQuery(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxPhanHeQuery(count, ts));
            });
        }
    }
}