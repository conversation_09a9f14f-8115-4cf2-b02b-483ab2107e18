using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/giao-an")]
    [ApiExplorerSettings(GroupName = "85. Giáo án")]
    [Authorize]
    public class GiaoAnController : ApiControllerBase
    {
        public GiaoAnController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }

        /// <summary>
        /// Lấy chi tiết giáo án theo giáo án, lớp, môn
        /// </summary>
        /// <param name="keyGroup"></param>
        /// <returns></returns>
        [HttpPost, Route("get-detail")]
        [ProducesResponseType(typeof(ResponseObject<GiaoAnModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIAO_AN_VIEW))]
        public async Task<IActionResult> GetByKeyGroup([FromBody] KeySelectGiaoAnModel keyGroup)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetGiaoAnByKeyGroupQuery(keyGroup)));
        }

        /// <summary>
        /// Thêm mới giáo án
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIAO_AN_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateGiaoAnModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_GIAO_AN_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_GIAO_AN_CREATE;


                return await _mediator.Send(new CreateGiaoAnCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa giáo án
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIAO_AN_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateGiaoAnModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_GIAO_AN_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_GIAO_AN_UPDATE;
                return await _mediator.Send(new UpdateGiaoAnCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa giáo án
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.GIAO_AN_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_GIAO_AN_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_GIAO_AN_DELETE;

                return await _mediator.Send(new DeleteGiaoAnCommand(id, u.SystemLog));
            });
        }
    }
}
