using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/khu-vuc")]
    [ApiExplorerSettings(GroupName = "25. Khu vực")]
    [Authorize]
    public class KhuVucController : ApiControllerBase
    {
        public KhuVucController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách khu vực cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<KhuVucSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxKhuVucQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách khu vực có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<KhuVucBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHU_VUC_VIEW))]
        public async Task<IActionResult> Filter([FromBody] KhuVucFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterKhuVucQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết khu vực
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<KhuVucModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHU_VUC_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetKhuVucByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới khu vực
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHU_VUC_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateKhuVucModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_KHU_VUC_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_KHU_VUC_CREATE;


                return await _mediator.Send(new CreateKhuVucCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel khu vực
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHU_VUC_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyKhuVucModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_KHU_VUC_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_KHU_VUC_CREATE_MANY;


                return await _mediator.Send(new CreateManyKhuVucCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa khu vực
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHU_VUC_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateKhuVucModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_KHU_VUC_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_KHU_VUC_UPDATE;
                return await _mediator.Send(new UpdateKhuVucCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa khu vực
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.KHU_VUC_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_KHU_VUC_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_KHU_VUC_DELETE;

                return await _mediator.Send(new DeleteKhuVucCommand(id, u.SystemLog));
            });
        }

    }
}
