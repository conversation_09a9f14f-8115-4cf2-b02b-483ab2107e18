using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xep-loai-hoc-tap-thang-4")]
    [ApiExplorerSettings(GroupName = "70. Xếp loại học tập thang 4")]
    [Authorize]
    public class XepLoaiHocTapThang4Controller : ApiControllerBase
    {
        public XepLoaiHocTapThang4Controller(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách Xếp loại học tập thang 4 cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepLoaiHocTapThang4SelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepLoaiHocTapThang4Query(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách Xếp loại học tập thang 4 có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XepLoaiHocTapThang4BaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_TOT_NGHIEP_THANG_4_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XepLoaiHocTapThang4FilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXepLoaiHocTapThang4Query(filter)));
        }


        /// <summary>
        /// Lấy chi tiết Xếp loại học tập thang 4
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XepLoaiHocTapThang4Model>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_TOT_NGHIEP_THANG_4_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXepLoaiHocTapThang4ByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới Xếp loại học tập thang 4
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_TOT_NGHIEP_THANG_4_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXepLoaiHocTapThang4Model model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_CREATE;


                return await _mediator.Send(new CreateXepLoaiHocTapThang4Command(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel Xếp loại học tập thang 4
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_TOT_NGHIEP_THANG_4_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXepLoaiHocTapThang4Model model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_CREATE_MANY;


                return await _mediator.Send(new CreateManyXepLoaiHocTapThang4Command(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa Xếp loại học tập thang 4
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_TOT_NGHIEP_THANG_4_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateXepLoaiHocTapThang4Model request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_UPDATE;
                return await _mediator.Send(new UpdateXepLoaiHocTapThang4Command(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa Xếp loại học tập thang 4
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_LOAI_TOT_NGHIEP_THANG_4_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_LOAI_TOT_NGHIEP_THANG_4_DELETE;

                return await _mediator.Send(new DeleteXepLoaiHocTapThang4Command(id, u.SystemLog));
            });
        }

    }
}
