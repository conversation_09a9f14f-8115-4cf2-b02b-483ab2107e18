using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/diem-ren-luyen-quy-doi")]
    [ApiExplorerSettings(GroupName = "43. Điểm rèn luyện quy đổi")]
    [Authorize]
    public class DiemRenLuyenQuyDoiController : ApiControllerBase
    {
        public DiemRenLuyenQuyDoiController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách điểm rèn luyện quy đổi cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<DiemRenLuyenQuyDoiSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxDiemRenLuyenQuyDoiQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách điểm rèn luyện quy đổi có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<DiemRenLuyenQuyDoiBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_REN_LUYEN_QUY_DOI_VIEW))]
        public async Task<IActionResult> Filter([FromBody] DiemRenLuyenQuyDoiFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterDiemRenLuyenQuyDoiQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết điểm rèn luyện quy đổi
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<DiemRenLuyenQuyDoiModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_REN_LUYEN_QUY_DOI_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetDiemRenLuyenQuyDoiByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới điểm rèn luyện quy đổi
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_REN_LUYEN_QUY_DOI_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateDiemRenLuyenQuyDoiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DIEM_REN_LUYEN_QUY_DOI_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DIEM_REN_LUYEN_QUY_DOI_CREATE;


                return await _mediator.Send(new CreateDiemRenLuyenQuyDoiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel điểm rèn luyện quy đổi
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_REN_LUYEN_QUY_DOI_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyDiemRenLuyenQuyDoiModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DIEM_REN_LUYEN_QUY_DOI_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DIEM_REN_LUYEN_QUY_DOI_CREATE_MANY;


                return await _mediator.Send(new CreateManyDiemRenLuyenQuyDoiCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa điểm rèn luyện quy đổi
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_REN_LUYEN_QUY_DOI_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateDiemRenLuyenQuyDoiModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DIEM_REN_LUYEN_QUY_DOI_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DIEM_REN_LUYEN_QUY_DOI_UPDATE;
                return await _mediator.Send(new UpdateDiemRenLuyenQuyDoiCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa điểm rèn luyện quy đổi
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.DIEM_REN_LUYEN_QUY_DOI_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_DIEM_REN_LUYEN_QUY_DOI_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_DIEM_REN_LUYEN_QUY_DOI_DELETE;

                return await _mediator.Send(new DeleteDiemRenLuyenQuyDoiCommand(id, u.SystemLog));
            });
        }

    }
}
