using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/phuong")]
    [ApiExplorerSettings(GroupName = "41.Phường")]
    [Authorize]
    public class PhuongController : ApiControllerBase
    {
        public PhuongController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách phường cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON>nh công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<PhuongSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxPhuongQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách phường có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<PhuongBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_VIEW))]
        public async Task<IActionResult> Filter([FromBody] PhuongFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterPhuongQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết phường
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PhuongModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetPhuongByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới phường
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_ADD))]
        public async Task<IActionResult> Create([FromBody] CreatePhuongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_CREATE;


                return await _mediator.Send(new CreatePhuongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel phường
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyPhuongModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_CREATE_MANY;


                return await _mediator.Send(new CreateManyPhuongCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa phường
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdatePhuongModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_UPDATE;
                return await _mediator.Send(new UpdatePhuongCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa phường
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.PHUONG_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_PHUONG_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_PHUONG_DELETE;

                return await _mediator.Send(new DeletePhuongCommand(id, u.SystemLog));
            });
        }

    }
}
