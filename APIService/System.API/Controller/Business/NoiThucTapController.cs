using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/noi-thuc-tap")]
    [ApiExplorerSettings(GroupName = "62. Nơi thực tập")]
    [Authorize]
    public class NoiThucTapController : ApiControllerBase
    {
        public NoiThucTapController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// L<PERSON>y danh sách nơi thực tập cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Th<PERSON><PERSON> công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<NoiThucTapSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxNoiThucTapQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách nơi thực tập có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<NoiThucTapBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NOI_THUC_TAP_VIEW))]
        public async Task<IActionResult> Filter([FromBody] NoiThucTapFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterNoiThucTapQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết nơi thực tập
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<NoiThucTapModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NOI_THUC_TAP_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetNoiThucTapByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới nơi thực tập
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NOI_THUC_TAP_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateNoiThucTapModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NOI_THUC_TAP_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NOI_THUC_TAP_CREATE;


                return await _mediator.Send(new CreateNoiThucTapCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel nơi thực tập
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NOI_THUC_TAP_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyNoiThucTapModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NOI_THUC_TAP_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NOI_THUC_TAP_CREATE_MANY;


                return await _mediator.Send(new CreateManyNoiThucTapCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa nơi thực tập
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NOI_THUC_TAP_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateNoiThucTapModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NOI_THUC_TAP_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NOI_THUC_TAP_UPDATE;
                return await _mediator.Send(new UpdateNoiThucTapCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa nơi thực tập
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.NOI_THUC_TAP_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_NOI_THUC_TAP_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_NOI_THUC_TAP_DELETE;

                return await _mediator.Send(new DeleteNoiThucTapCommand(id, u.SystemLog));
            });
        }

    }
}
