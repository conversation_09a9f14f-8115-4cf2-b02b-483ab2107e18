using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xa")]
    [ApiExplorerSettings(GroupName = "42. Xã")]
    [Authorize]
    public class XaController : ApiControllerBase
    {
        public XaController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách xã cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XaSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXaQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách xã có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XaBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XA_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XaFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXaQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết xã
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XaModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XA_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] string id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXaByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới xã
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XA_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXaModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XA_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XA_CREATE;


                return await _mediator.Send(new CreateXaCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel xã
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XA_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXaModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XA_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XA_CREATE_MANY;


                return await _mediator.Send(new CreateManyXaCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa xã
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XA_EDIT))]
        public async Task<IActionResult> Update(string id, [FromBody] UpdateXaModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XA_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XA_UPDATE;
                return await _mediator.Send(new UpdateXaCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa xã
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XA_DELETE))]
        public async Task<IActionResult> Delete(string id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XA_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XA_DELETE;

                return await _mediator.Send(new DeleteXaCommand(id, u.SystemLog));
            });
        }

    }
}
