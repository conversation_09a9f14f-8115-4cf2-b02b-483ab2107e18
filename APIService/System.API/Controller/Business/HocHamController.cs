using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/hoc-ham")]
    [ApiExplorerSettings(GroupName = "19. Học hàm")]
    [Authorize]
    public class HocHamController : ApiControllerBase
    {
        public HocHamController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách học hàm cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<HocHamSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxHocHamQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách học hàm có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<HocHamBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_HAM_VIEW))]
        public async Task<IActionResult> Filter([FromBody] HocHamFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterHocHamQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết học hàm
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<HocHamModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_HAM_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetHocHamByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới học hàm
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_HAM_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateHocHamModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_HAM_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_HAM_CREATE;


                return await _mediator.Send(new CreateHocHamCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel học hàm
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_HAM_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyHocHamModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_HAM_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_HAM_CREATE_MANY;


                return await _mediator.Send(new CreateManyHocHamCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa học hàm
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_HAM_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateHocHamModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_HAM_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_HAM_UPDATE;
                return await _mediator.Send(new UpdateHocHamCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa học hàm
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.HOC_HAM_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_HOC_HAM_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_HOC_HAM_DELETE;

                return await _mediator.Send(new DeleteHocHamCommand(id, u.SystemLog));
            });
        }

    }
}
