using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;



namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/xep-hang-nam-dao-tao")]
    [ApiExplorerSettings(GroupName = "68. Xếp hạng năm đào tạo")]
    [Authorize]
    public class XepHangNamDaoTaoController : ApiControllerBase
    {
        public XepHangNamDaoTaoController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {

        }
        /// <summary>
        /// Lấy danh sách xếp hạng năm đào tạo cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<XepHangNamDaoTaoSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxXepHangNamDaoTaoQuery(count, ts));
            });
        }

        /// <summary>
        /// Lấy danh sách xếp hạng năm đào tạo có lọc
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<XepHangNamDaoTaoBaseModel>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_NAM_DAO_TAO_VIEW))]
        public async Task<IActionResult> Filter([FromBody] XepHangNamDaoTaoFilterModel filter)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetFilterXepHangNamDaoTaoQuery(filter)));
        }


        /// <summary>
        /// Lấy chi tiết xếp hạng năm đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<XepHangNamDaoTaoModel>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_NAM_DAO_TAO_VIEW))]
        public async Task<IActionResult> GetById([FromRoute] int id)
        {
            return await ExecuteFunction(async _ => await _mediator.Send(new GetXepHangNamDaoTaoByIdQuery(id)));
        }

        /// <summary>
        /// Thêm mới xếp hạng năm đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_NAM_DAO_TAO_ADD))]
        public async Task<IActionResult> Create([FromBody] CreateXepHangNamDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_NAM_DAO_TAO_CREATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_NAM_DAO_TAO_CREATE;


                return await _mediator.Send(new CreateXepHangNamDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Import excel xếp hạng năm đào tạo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("create-many")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_NAM_DAO_TAO_ADD))]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyXepHangNamDaoTaoModel model)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_NAM_DAO_TAO_CREATE_MANY);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_NAM_DAO_TAO_CREATE_MANY;


                return await _mediator.Send(new CreateManyXepHangNamDaoTaoCommand(model, u.SystemLog));
            });
        }

        /// <summary>
        /// Sửa xếp hạng năm đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_NAM_DAO_TAO_EDIT))]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateXepHangNamDaoTaoModel request)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_NAM_DAO_TAO_UPDATE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_NAM_DAO_TAO_UPDATE;
                return await _mediator.Send(new UpdateXepHangNamDaoTaoCommand(id, request, u.SystemLog));
            });
        }

        /// <summary>
        /// Xóa xếp hạng năm đào tạo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<Unit>>), StatusCodes.Status200OK)]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.XEP_HANG_NAM_DAO_TAO_DELETE))]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async u =>
            {
                u.SystemLog.ActionCode = nameof(SystemLogConstants.ACTION_XEP_HANG_NAM_DAO_TAO_DELETE);
                u.SystemLog.ActionName = SystemLogConstants.ACTION_XEP_HANG_NAM_DAO_TAO_DELETE;

                return await _mediator.Send(new DeleteXepHangNamDaoTaoCommand(id, u.SystemLog));
            });
        }

    }
}
