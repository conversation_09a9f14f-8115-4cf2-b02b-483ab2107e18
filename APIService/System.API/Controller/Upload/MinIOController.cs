//using Asp.Versioning;
//using Core.API.Shared;
//using Core.Business;
//using Core.Shared;
//using MediatR;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Hosting;
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Localization;
//using MimeMapping;
//using System;
//using System.IO;
//using System.Net.Mime;
//using System.Threading.Tasks;

//namespace Core.API
//{
//    [ApiVersion("1.0")]
//    [ApiController]
//    [Route("system/v1/core/minio")]
//    [ApiExplorerSettings(GroupName = "MinIO - 01. MinIO")]
//    [Authorize]
//    public class MinIOController : ApiControllerBase
//    {
//        private readonly IWebHostEnvironment _webHostEnvironment;
//        private readonly IStringLocalizer<Resources> _localizer;
//        public MinIOController(IMediator mediator, IStringLocalizer<Resources> localizer, IWebHostEnvironment webHostEnvironment, IConfiguration config) : base(mediator, localizer, config)
//        {
//            _localizer = localizer;
//            _webHostEnvironment = webHostEnvironment;
//        }

//        /// <summary>
//        /// Lấy url download file
//        /// </summary>
//        /// <param name="bucketName"></param>
//        /// <param name="objectName"></param>
//        /// <param name="expiresInt"></param>
//        /// <returns></returns>
//        [HttpGet]
//        [Route("get-object-presign-url")]
//        [ProducesResponseType(typeof(ResponseObject<MinIOFileUploadResult>), StatusCodes.Status200OK)]
//        public async Task<IActionResult> GetObjectPresignUrlAsync(string bucketName, string objectName, int expiresInt = 3600)
//        {
//            return await ExecuteFunction(async (RequestUser u) =>
//            {
//                var ms = new MinIOService();
//                var url = await ms.GetObjectPresignUrlAsync(bucketName, objectName, expiresInt);
//                return new ResponseObject<string>(url, MessageConstants.GetDataSuccessMessage, Code.Success);
//            });
//        }

//        /// <summary>
//        /// Upload file
//        /// </summary>
//        /// <param name="bucketName"></param>
//        /// <param name="fileName"></param>
//        /// <param name="file">File upoad</param>
//        /// <returns></returns>
//        [HttpPost]
//        [Route("upload-object")]
//        [ProducesResponseType(typeof(ResponseObject<MinIOFileUploadResult>), StatusCodes.Status200OK)]
//        public async Task<IActionResult> UploadObject(string bucketName, string fileName, IFormFile file)
//        {
//            return await ExecuteFunction(async (RequestUser u) =>
//            {
//                if (file == null)
//                    throw new ArgumentException("File is null");
//                var ms = new MinIOService();
//                fileName = fileName ?? file.FileName ?? "NoName";
//                var fileStream = file.OpenReadStream();
//                var memory = new MemoryStream();
//                fileStream.CopyTo(memory);
//                var rs = await ms.UploadObjectAsync(bucketName, fileName, memory);
//                return new ResponseObject<MinIOFileUploadResult>(rs, "Tải lên file thành công", Code.Success);
//            });
//        }

//        /// <summary>
//        /// Upload file Base 64
//        /// </summary>
//        /// <param name="bucketName"></param>
//        /// <param name="file"></param>
//        /// <returns></returns>
//        [HttpPost]
//        [Route("upload-object-base64")]
//        [ProducesResponseType(typeof(ResponseObject<MinIOFileUploadResult>), StatusCodes.Status200OK)]
//        public async Task<IActionResult> UploadObjectBase64(string bucketName, [FromBody] Base64FileData file)
//        {
//            return await ExecuteFunction(async (RequestUser u) =>
//            {
//                if (string.IsNullOrEmpty(file.Name))
//                    throw new ArgumentException("Tên file không được để trống");
//                if (string.IsNullOrEmpty(file.FileData))
//                    throw new ArgumentException("Chưa tải lên file");
//                file.Name = file.Name ?? "NoName";
//                if (file.FileData.IndexOf(',') >= 0)
//                    file.FileData = file.FileData.Substring(file.FileData.IndexOf(',') + 1);
//                var bytes = Convert.FromBase64String(file.FileData);
//                var memory = new MemoryStream(bytes);
//                var ms = new MinIOService();
//                return await ms.UploadObjectAsync(bucketName, file.Name, memory);
//            });
//        }

//        /// <summary>
//        /// Download file
//        /// </summary>
//        /// <param name="bucketName"></param>
//        /// <param name="objectName"></param>
//        /// <returns></returns>
//        [HttpGet]
//        [Route("download-object")]
//        [ProducesResponseType(typeof(FileStreamResult), StatusCodes.Status200OK)]
//        public async Task<IActionResult> DownloadObject(string bucketName, string objectName)
//        {
//            var ms = new MinIOService();
//            var fileName = Path.GetFileName(objectName);
//            var memory = await ms.DownloadObjectAsync(bucketName, objectName);
//            memory.Position = 0;
//            var cd = new ContentDisposition
//            {
//                FileName = Utils.RemoveVietnameseSign(fileName),
//                Inline = true
//            };
//            Response.Headers.Add("Content-Disposition", cd.ToString());
//            return File(memory, MimeUtility.GetMimeMapping(fileName));
//        }
//        /// <summary>
//        /// Download file base64
//        /// </summary>
//        /// <param name="bucketName"></param>
//        /// <param name="objectName"></param>
//        /// <returns></returns>
//        [HttpGet]
//        [Route("download-object-base64")]
//        [ProducesResponseType(typeof(ResponseObject<Base64FileData>), StatusCodes.Status200OK)]
//        public async Task<IActionResult> DownloadObjectBase64(string bucketName, string objectName)
//        {
//            return await ExecuteFunction(async (RequestUser u) =>
//            {
//                var ms = new MinIOService();
//                var fileName = Path.GetFileName(objectName);
//                var memory = await ms.DownloadObjectAsync(bucketName, objectName);
//                var fileData = memory.ToArray();
//                var file = new Base64FileData()
//                {
//                    Name = fileName,
//                    FileData = Convert.ToBase64String(fileData)
//                };
//                return file;
//            });
//        }
//    }
//}
