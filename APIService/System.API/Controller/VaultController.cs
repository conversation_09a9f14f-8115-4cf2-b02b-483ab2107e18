using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using Core.API.Shared;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using System;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;
using VaultSharp;
using Serilog;

namespace Core.API.Controller
{
    /// <summary>
    /// Module vault management
    /// </summary>
    [ApiController]
    [Route("system/v1/vault")]
    [ApiExplorerSettings(GroupName = "Vault Service", IgnoreApi = true)]
    public class VaultController : ApiControllerBase
    {
        public VaultController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        [HttpGet, Route("test")]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> TestConnection(string secretPath)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                // Cấu hình Vault client
                var vaultAddress = "http://************:8200";
                var token = "9fR6P1sY52cMDDtTnIWG";

                var authMethod = new TokenAuthMethodInfo(token);
                var vaultClientSettings = new VaultClientSettings(vaultAddress, authMethod);
                IVaultClient vaultClient = new VaultClient(vaultClientSettings);

                // Lấy secret từ Vault
                //var secretPath = "haind";
                //Secret<SecretData> secret = vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(secretPath).Result;

                Secret<SecretData> secret = await vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync("haind", null, "secret");

                // In ra giá trị của secret
                var secretValue = secret.Data.Data["your-secret-key"];
                Console.WriteLine($"Secret Value: {secretValue}");

                return true;
            });
        }

        [HttpPost, Route("encrypt")]
        [ProducesResponseType(typeof(ResponseObject<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Encrypt(EncryptedDataModel data)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return AesEncryption.Encrypt(_config, data.Text);
            });
        }

        [HttpPost, Route("decrypt")]
        [ProducesResponseType(typeof(ResponseObject<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Decrypt(EncryptedDataModel data)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return AesEncryption.Decrypt(_config, data.Text);
            });
        }

        public class EncryptedDataModel
        {
            public string Text { get; set; }
        }
    }
}
