using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using Serilog;
using System;
using System.Text;
using Core.Business.Core;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Business;
using Nest;

namespace Core.API.Controller
{
    /// <summary>
    /// Module test redis
    /// </summary>
    [ApiController]
    [Route("system/v1/seed-data")]
    [ApiExplorerSettings(GroupName = "101. Seed Data", IgnoreApi = false)]
    [AllowAnonymous]
    public class SeedDataController : ApiControllerBase
    {
        private readonly IDistributedCache _distributedCache;
        private readonly IRedisHandler _redisHandler;
        private readonly ISendMailHandler _sendMailHandler;
        public SeedDataController(IDistributedCache distributedCache, IRedisHandler redisHandler, IMediator mediator, IStringLocalizer<Resources> localizer, ISendMailHandler sendMailHandler, IConfiguration config) : base(mediator, localizer, config)
        {
            _distributedCache = distributedCache;
            _redisHandler = redisHandler;
            _sendMailHandler = sendMailHandler;
        }

        /// <summary>
        /// DeleteAsync
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("seed")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SeedDataAsync()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new SeedDataCommand(u.SystemLog));
            });
        }

        /// <summary>
        /// RunMigrationsAsync
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("run-migrations")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> RunMigrationsAsync()
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new RunSystemMigrationsCommand());
            });
        }

        ///// <summary>
        ///// Send mail test
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost, Route("single-email")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public async Task<IActionResult> SendMailFluentEmail()
        //{
        //    EmailMetadata emailMetadata = new("<EMAIL>",
        //         "FluentEmail Test email");
        //    await _sendMailHandler.SendMailFluentEmail(emailMetadata);
        //    return Ok();
        //}

        ///// <summary>
        ///// Send mail using razor template
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost, Route("send-mail-test-template")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //public async Task<IActionResult> SendMailFluentEmailRazorTemplate()
        //{
        //    EmailMetadata emailMetadata = new("<EMAIL>",
        //         "FluentEmail Test email");
        //    LoginModel dt = new LoginModel()
        //    {
        //        Username = "HaiND",
        //        Password = "123123123",
        //        RememberMe = true
        //    };

        //    var template = @"Dear <b>{{ Username }}</b>,<br/>
        //    Thank you for being an {{ RememberMe }} esteemed <b>{{ Password }}</b> member.";

        //    _ = _sendMailHandler.SendMailUsingTemplate(emailMetadata, template, dt);

        //    return Ok();
        //}
    }
}
