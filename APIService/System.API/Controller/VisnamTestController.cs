using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using System;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Business.ThirdPartyBussiness.SignServer;
using Core.Shared.Constants;
using Core.Shared.ContextAccessor;
using Core.Shared.Infrastructure;

namespace Core.API.Controller
{
    /// <summary>
    /// Module test redis
    /// </summary>
    [ApiController]
    [Route("system/v1/visnam-test")]
    [ApiExplorerSettings(GroupName = "00. Visnam Test", IgnoreApi = false)]
    [AllowAnonymous]
    public class VisnamTestController : ApiControllerBaseV2
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ISignServerService _signServer;

        public VisnamTestController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator, 
            IStringLocalizer<Resources> localizer,
            IConfiguration config, IServiceProvider serviceProvider) : base(contextAccessorFactory, mediator, localizer, config)
        {
            _serviceProvider = serviceProvider;
            _signServer = _serviceProvider.GetNamedService<ISignServerService>(SignServerServiceContants.Visnam);
        }

        /// <summary>
        /// Test get end cert
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("test-get-end-cert")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCertificatesByUserIdAsync()
        {
            return await ExecuteFunction(async () =>
            {
                return await _signServer.GetCertificatesByUserIdAsync(1);
            });
        }
        
        // Test ký hash
        [HttpPost, Route("test-sign-hash")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SignHashAsync()
        {
            return await ExecuteFunction(async () =>
            {
                return await _signServer.SignHashAsync(1, 1, "test");
            });
        }
    }
}
