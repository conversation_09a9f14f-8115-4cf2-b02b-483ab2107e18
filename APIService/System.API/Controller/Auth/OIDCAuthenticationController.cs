using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using IdentityModel.Client;
using Microsoft.AspNetCore.Http;
using Core.Business;
using Core.API.Shared;
using Serilog;

namespace Core.API
{
    /// <summary>
    /// JWT cho hệ thống
    /// </summary>
    [ApiController]
    [Route("auth/v1/oidc")]
    [ApiExplorerSettings(GroupName = "01. OIDC")]
    public class OIDCAuthenticationController : ApiControllerBase
    {
        public OIDCAuthenticationController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// Lấy access token theo mã code xác thực từ ID4
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [Route("access-token")]
        [AllowAnonymous, HttpPost]
        [ProducesResponseType(typeof(ResponseObject<TokenResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RequestAuthorizationCodeTokenAsync(RequestAuthorizationCodeTokenModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var client = new HttpClient();
                
                var response = await client.RequestAuthorizationCodeTokenAsync(new AuthorizationCodeTokenRequest
                {
                    Address = _config["Authentication:IdentityServer:Uri"] + "/connect/token",
                    ClientId = _config["Authentication:IdentityServer:ClientId"],
                    ClientSecret = _config["Authentication:IdentityServer:Secret"],
                    Code = model.AuthorizationCode,
                    //CodeVerifier = codeVerifier,
                    RedirectUri = model.RedirectUri ?? _config["Authentication:IdentityServer:Redirecturi"]
                });

                if (response.IsError)
                {
                    return new Response(Code.ServerError, "Thực hiện request access token không thành công");
                }
                var rs = AutoMapperUtils.AutoMap<TokenResponse, OAuthTokenModel>(response);

                #region Lấy thông tin user và trả thông tin ra cho client
                try
                {
                    var userId = Helper.GetValueFromJWTTokenByKey(rs.AccessToken, "sub");
                    var userIdNumber = int.Parse(userId);

                    var userModel = await _mediator.Send(new GetUserBaseForAuthQuery(userIdNumber));
                    if (userModel != null)
                    {
                        rs.UserInfo = new UserAuthModel()
                        {
                            Id = userModel.UserId,
                            IsActive = userModel.Active == 1,
                            MaCanBo = userModel.MaCanBoUser,
                            FullName = userModel.FullName,
                            UserName = userModel.UserName
                        };
                    }
                }
                catch (Exception)
                {
                }
                #endregion
                return new ResponseObject<OAuthTokenModel>(rs, "Success", Code.Success);
            });
        }

        /// <summary>
        /// Lấy access token mới
        /// </summary>
        /// <param name="refreshToken"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [Route("refresh-token")]
        [AllowAnonymous, HttpPost]
        [ProducesResponseType(typeof(ResponseObject<TokenResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RequestRefreshTokenAsync(string refreshToken)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var client = new HttpClient();
                var response = await client.RequestRefreshTokenAsync(new RefreshTokenRequest
                {
                    Address = _config["Authentication:IdentityServer:Uri"] + "/connect/token",
                    ClientId = _config["Authentication:IdentityServer:ClientId"],
                    ClientSecret = _config["Authentication:IdentityServer:Secret"],
                    RefreshToken = refreshToken
                });
                if (response.IsError)
                {
                    Log.Error(response.Error);
                    return new Response(Code.ServerError, "Thực hiện request access token không thành công");
                }
                var rs = AutoMapperUtils.AutoMap<TokenResponse, OAuthTokenModel>(response);

                #region Lấy thông tin user và trả thông tin ra cho client
                try
                {
                    var userId = Helper.GetValueFromJWTTokenByKey(rs.AccessToken, "sub");
                    var userIdNumber = int.Parse(userId);

                    var userModel = await _mediator.Send(new GetUserBaseForAuthQuery(userIdNumber));
                    rs.UserInfo = new UserAuthModel()
                    {
                        Id = userModel.UserId,
                        MaCanBo = userModel.MaCanBoUser,
                        FullName = userModel.FullName,
                        UserName = userModel.UserName
                    };
                }
                catch (Exception)
                {
                }
                #endregion
                return new ResponseObject<OAuthTokenModel>(rs, "Success", Code.Success);
            });
        }

        [Route("revoke-token")]
        [AllowAnonymous, HttpPost]
        [ProducesResponseType(typeof(ResponseObject<TokenResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RevokeTokenAsync(string accessToken)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var client = new HttpClient();
                var response = await client.RevokeTokenAsync(new TokenRevocationRequest()
                {
                    Address = _config["Authentication:IdentityServer:Uri"] + "/connect/revocation",
                    ClientId = _config["IdentityServer:ClientId"],
                    ClientSecret = _config["IdentityServer:Secret"],
                    Token = accessToken
                });
                if (response.IsError)
                {
                    throw new Exception("Có lỗi xảy ra");
                }
                return response;
            });
        }

        [Route("user-info")]
        [AllowAnonymous, HttpPost]
        [ProducesResponseType(typeof(ResponseObject<OAuthUserInfoModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserInfoAsync(string idToken)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var client = new HttpClient();
                var response = await client.GetUserInfoAsync(new UserInfoRequest
                {
                    Address = _config["Authentication:IdentityServer:Uri"] + "/connect/userinfo",
                    Token = idToken
                });
                if (response.IsError)
                {
                    throw new Exception("Có lỗi xảy ra");
                }
                var rs = AutoMapperUtils.AutoMap<UserInfoResponse, OAuthUserInfoModel>(response);
                return rs;
            });
        }

        [Route("introspect-token")]
        [AllowAnonymous, HttpPost]
        [ProducesResponseType(typeof(ResponseObject<TokenResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> IntrospectTokenAsync(string accessToken)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var client = new HttpClient();

                var response = await client.IntrospectTokenAsync(new TokenIntrospectionRequest
                {
                    Address = _config["Authentication:IdentityServer:Uri"] + "/connect/introspect",
                    ClientId = _config["IdentityServer:ClientId"],
                    ClientSecret = _config["IdentityServer:Secret"],

                    Token = accessToken
                });
                if (response.IsError)
                {
                    throw new Exception("Có lỗi xảy ra");
                }
                var rs = AutoMapperUtils.AutoMap<TokenIntrospectionResponse, TokenIntrospectionModel>(response);
                return rs;
            });
        }
    }
}
