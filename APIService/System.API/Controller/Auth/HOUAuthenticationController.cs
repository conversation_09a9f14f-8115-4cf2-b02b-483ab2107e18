using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using IdentityModel.Client;
using Microsoft.AspNetCore.Http;
using Core.Business;
using Core.API.Shared;
using System.Collections.Generic;
using Core.Business.System;
using System.Linq;

namespace Core.API
{
    [ApiController]
    [Route("auth/v1/hou")]
    [ApiExplorerSettings(GroupName = "02. Hou CAS Validation")]
    public class HOUAuthenticationController : ApiControllerBase
    {
        public HOUAuthenticationController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }
        /// <summary>
        /// Lấy access token theo tiket của CAS
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [Route("validate-ticket")]
        [AllowAnonymous, HttpPost]
        [ProducesResponseType(typeof(ResponseObject<TokenResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RequestAuthorizationCodeTokenAsync(RequestValidateTicketModel model)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                var client = new HttpClient();

                var rs = await client.GetAsync(_config["Authentication:HOU:CasServerUrl"] + "/cas/serviceValidate?ticket=" + model.Ticket + "&service=" + model.Service);
                var contents = await rs.Content.ReadAsStringAsync();

                if (!contents.Contains("<cas:user>"))
                {
                    return new Response(Code.ServerError, "Không lấy được thông tin user");
                };
                var res = new OAuthTokenModel();

                #region Lấy thông tin user, token và trả thông tin ra cho client
                //TODO: Cần cập nhật convert xml về object và đọc thông tin từ object để lấy được userName
                var userName = contents.Substring(contents.IndexOf("<cas:user>") + 10, contents.IndexOf("</cas:user>") - (contents.IndexOf("<cas:user>") + 10));

                var userModel = await _mediator.Send(new GetUserBaseForAuthQueryFromUserName(userName));
                if (userModel != null)
                {
                    res.AccessToken = JwtService.GenerateToken(_config, new UserLoginModel()
                    {
                        UserName = userModel.UserName,
                        Id = userModel.UserId
                    }, true);
                    res.UserInfo = new UserAuthModel()
                    {
                        Id = userModel.UserId,
                        IsActive = userModel.Active == 1,
                        MaCanBo = userModel.MaCanBoUser,
                        FullName = userModel.FullName,
                        UserName = userModel.UserName
                    };
                }
                #endregion

                return new ResponseObject<OAuthTokenModel>(res, "Success", Code.Success);
            });
        }
    }
}
