using Core.Business.Core;
using Core.Shared;
using MediatR;
using Microsoft.Extensions.Hosting;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Serilog;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading;
using Core.API.Shared;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace System.API.MessageQueue
{
    // https://learn.microsoft.com/en-us/dotnet/core/extensions/scoped-service
    public class NotificationConsumer : BackgroundServiceWorkerBase
    {
        private const string ClassName = nameof(NotificationConsumer);
        private readonly IConnection _conFactory;
        private readonly IModel _channel;

        public NotificationConsumer(IServiceScopeFactory serviceScopeFactory, IConfiguration config) : base(serviceScopeFactory, config)
        {
            #region Khởi tạo thông tin kết nối RabbitMQ
            var factory = new ConnectionFactory
            {
                Uri = new Uri(_config["RabbitMQ:Uri"])
            };

            _conFactory = factory.CreateConnection();

            _channel = _conFactory.CreateModel();
            _channel.InitConfiguration(typeof(NotificationModel));
            _channel.InitConfiguration(typeof(NotificationBroadcastModel));
            _channel.InitConfiguration(typeof(TeacherNotificationModel));
            #endregion
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            stoppingToken.ThrowIfCancellationRequested();

            await DoWorkAsync(stoppingToken);
        }

        private Task DoWorkAsync(CancellationToken stoppingToken)
        {
            Log.Information($"{ClassName} is working.");

            try
            {
                #region Notification
                var consumerNotification = new EventingBasicConsumer(_channel);
                consumerNotification.Received += async (model, eventArgs) =>
                {
                    var body = eventArgs.Body.ToArray();
                    var message = Encoding.UTF8.GetString(body);
                    NotificationModel data = JsonSerializer.Deserialize<NotificationModel>(message);

                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        await ServiceExecuteFunction(scope, eventArgs, async (u) =>
                        {
                            Log.Information($"Subscribe message {Utils.GetEventNameAttribute(typeof(NotificationModel))}");
                            await scope.ServiceProvider
                                .GetRequiredService<IMediator>()
                                .Send(new SendNotificationToUserByHubCommand(data, u));
                        });
                    }
                };

                _channel.BasicConsume(Utils.GetEventNameAttribute(typeof(NotificationModel)), true, consumerNotification);
                #endregion

                #region NotificationBroadcast
                var consumerNotificationBroadcast = new EventingBasicConsumer(_channel);
                consumerNotificationBroadcast.Received += async (model, eventArgs) =>
                {
                    var body = eventArgs.Body.ToArray();
                    var message = Encoding.UTF8.GetString(body);
                    if (string.IsNullOrEmpty(message)) return;

                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        await ServiceExecuteFunction(scope, eventArgs, async (u) =>
                    {
                        Log.Information($"Subscribe message {Utils.GetEventNameAttribute(typeof(NotificationBroadcastModel))}");
                        NotificationBroadcastModel data = JsonSerializer.Deserialize<NotificationBroadcastModel>(message);
                        await _serviceScopeFactory.CreateScope().ServiceProvider
                            .GetRequiredService<IMediator>()
                            .Send(new SendBroadcastNotificationByHubCommand(data, u));
                    });
                    }
                };

                _channel.BasicConsume(Utils.GetEventNameAttribute(typeof(NotificationBroadcastModel)), true, consumerNotificationBroadcast);
                #endregion

                #region Teacher Notification
                var consumerTeacherNotification = new EventingBasicConsumer(_channel);
                consumerTeacherNotification.Received += async (model, eventArgs) =>
                {
                    var body = eventArgs.Body.ToArray();
                    var message = Encoding.UTF8.GetString(body);
                    TeacherNotificationModel data = JsonSerializer.Deserialize<TeacherNotificationModel>(message);

                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        await ServiceExecuteFunction(scope, eventArgs, async (u) =>
                        {
                            Log.Information($"Subscribe message {Utils.GetEventNameAttribute(typeof(TeacherNotificationModel))}");
                            await scope.ServiceProvider
                                .GetRequiredService<IMediator>()
                                .Send(new SendTeacherNotificationToUserByHubCommand(data, u));
                        });
                    }
                };

                _channel.BasicConsume(Utils.GetEventNameAttribute(typeof(TeacherNotificationModel)), true, consumerTeacherNotification);
                #endregion
            }
            catch (Exception ex)
            {
                Log.Error($"EventingBasicConsumer Error: " + ex.Message);
            }

            return Task.CompletedTask;
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            Log.Information($"{ClassName} is stopping.");
            await base.StopAsync(stoppingToken);
        }

        public override void Dispose()
        {
            Log.Information($"Dispose {ClassName}");
            _channel.Close();
            _conFactory.Close();
            base.Dispose();
        }
    }
}
