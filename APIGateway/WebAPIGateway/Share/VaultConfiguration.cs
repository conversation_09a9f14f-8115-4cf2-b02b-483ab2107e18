using System.Text.Json;
using VaultSharp;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;

namespace WebAPIGateway.Share
{
    public class VaultConfigurationProvider : ConfigurationProvider
    {
        public VaultOptions _config;
        private IVaultClient _client;

        public VaultConfigurationProvider(VaultOptions config)
        {
            _config = config;

            // Cấu hình Vault client
            var authMethod = new TokenAuthMethodInfo(_config.Token);
            var vaultClientSettings = new VaultClientSettings(_config.Address, authMethod);
            _client = new VaultClient(vaultClientSettings);
        }

        public override void Load()
        {
            LoadSecretAsync().Wait();
        }

        public async Task LoadSecretAsync()
        {
            // Tạo dictionary để lưu các key phẳng
            var flattenedDictionary = new Dictionary<string, string>();

            try
            {
                // Lấy danh sách cấu hình dùng chung
                if (!string.IsNullOrEmpty(_config.Mount) && !string.IsNullOrEmpty(_config.SecretCommon))
                {
                    Secret<SecretData> secrets = await _client.V1.Secrets.KeyValue.V2.ReadSecretAsync(_config.SecretCommon, null, _config.Mount);
                    foreach (var kvp in secrets.Data.Data)
                    {
                        // Chuyển đổi object thành chuỗi JSON
                        var data = JsonSerializer.Serialize(kvp.Value);

                        if (IsJson(data))
                        {
                            // Phân tích cú pháp JSON thành JsonDocument
                            using (JsonDocument document = JsonDocument.Parse(data))
                            {
                                // Bắt đầu chuyển đổi từ gốc của JSON object
                                FlattenJson(kvp.Key, document.RootElement, flattenedDictionary);
                            }
                        }
                        else
                        {
                            // Nếu không phải là JSON, xử lý chuỗi như một giá trị thông thường
                            flattenedDictionary[kvp.Key] = data;
                        }
                    }

                }

                // Lấy danh sách cấu hình của riêng service
                if (!string.IsNullOrEmpty(_config.Mount) && !string.IsNullOrEmpty(_config.SecretDetail))
                {
                    Secret<SecretData> secretsDetail = await _client.V1.Secrets.KeyValue.V2.ReadSecretAsync(_config.SecretDetail, null, _config.Mount);
                    foreach (var kvp in secretsDetail.Data.Data)
                    {
                        // Chuyển đổi object thành chuỗi JSON
                        var data = JsonSerializer.Serialize(kvp.Value);

                        if (IsJson(data))
                        {
                            // Phân tích cú pháp JSON thành JsonDocument
                            using (JsonDocument document = JsonDocument.Parse(data))
                            {
                                // Bắt đầu chuyển đổi từ gốc của JSON object
                                FlattenJson(kvp.Key, document.RootElement, flattenedDictionary);
                            }
                        }
                        else
                        {
                            // Nếu không phải là JSON, xử lý chuỗi như một giá trị thông thường
                            flattenedDictionary[kvp.Key] = data;
                        }
                    }
                }

                // Gán dữ liệu từ Vault vào cấu hình
                foreach (var kvp in flattenedDictionary)
                {
                    Data.Add(kvp.Key, string.IsNullOrEmpty(kvp.Value) ? string.Empty : kvp.Value);
                    //Console.WriteLine($"{kvp.Key} - {kvp.Value}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message}");
            }
        }

        public static bool IsJson(string input)
        {
            input = input.Trim();
            if (input.StartsWith("{") && input.EndsWith("}") || // Đối tượng JSON
                input.StartsWith("[") && input.EndsWith("]"))   // Mảng JSON
            {
                try
                {
                    using (JsonDocument doc = JsonDocument.Parse(input))
                    {
                        return true;
                    }
                }
                catch (JsonException)
                {
                    return false;
                }
            }
            return false;
        }

        // Hàm đệ quy để chuyển đổi các key
        public static void FlattenJson(string prefix, JsonElement element, Dictionary<string, string> dictionary)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Object:
                    foreach (JsonProperty property in element.EnumerateObject())
                    {
                        var key = string.IsNullOrEmpty(prefix) ? property.Name : $"{prefix}:{property.Name}";
                        FlattenJson(key, property.Value, dictionary);
                    }
                    break;
                case JsonValueKind.Array:
                    int index = 0;
                    foreach (JsonElement arrayElement in element.EnumerateArray())
                    {
                        var key = $"{prefix}:{index}";
                        FlattenJson(key, arrayElement, dictionary);
                        index++;
                    }
                    break;
                default:
                    dictionary[prefix] = element.ToString();
                    break;
            }
        }
    }

    public class VaultConfigurationSource : IConfigurationSource
    {
        private VaultOptions _config;

        public VaultConfigurationSource(Action<VaultOptions> config)
        {
            _config = new VaultOptions();
            config.Invoke(_config);
        }

        public IConfigurationProvider Build(IConfigurationBuilder builder)
        {
            return new VaultConfigurationProvider(_config);
        }
    }

    public class VaultOptions
    {
        public bool Enabled { get; set; }
        public string Address { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public string Mount { get; set; } = string.Empty;
        public string SecretCommon { get; set; } = string.Empty;
        public string SecretDetail { get; set; } = string.Empty;
    }

    public static class VaultExtensions
    {
        public static IConfigurationBuilder AddVault(this IConfigurationBuilder configuration, Action<VaultOptions> options)
        {
            var vaultOptions = new VaultConfigurationSource(options);
            configuration.Add(vaultOptions);
            return configuration;
        }
    }
}