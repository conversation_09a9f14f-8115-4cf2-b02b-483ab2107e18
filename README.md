# Note

## UPDATE từ T7-205

- <PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> sang Code First => Khi có update database thì cần thực hiện cập nhật model và tạo migrations
- Cú pháp đặt tên Migration: <Tên phân hệ>_<Mô tả nội dung update> => Ví dụ: System_AddRoleUser, System_UpdateUserModel

### Hướng dẫn tạo migrations

- Vì có nhiều DataContext cho datacontext đọc và ghi nên cần chỉ định đúng datacontext ghi để chạy migration
- add-migration System_Init -Context SystemDataContext
- update-database -Context SystemDataContext
- Script-Migration 20250403180228_add_log 20250403180006_update_0404 -Context SystemDataContext
- Script-Migration -Context SystemDataContext

- dotnet ef migrations add System_Init -Context SystemDataContext
- dotnet ef database update --context SystemDataContext

## Lưu ý khi build

- Copy file `/file/Spire.License.dll` vào trong thư mục build source code để không bị markdown khi sử dụng thư viện `Sprire.Doc`

## Hướng dẫn build docker image

### Đăng nhập

```bash
docker login -u username -p password docker.io
```

### Build và push web api gw image

```bash
docker build -t thienanunisoft/web-api-gw:latest . -f Dockerfile/dockerfile_web_api_gw
docker push thienanunisoft/web-api-gw:latest

docker image tag thienanunisoft/web-api-gw:latest thienanunisoft/web-api-gw:dev
docker push thienanunisoft/web-api-gw:dev

docker image tag thienanunisoft/web-api-gw:latest thienanunisoft/web-api-gw:test
docker push thienanunisoft/web-api-gw:test
```

### Build và push system api

```bash
docker build -t thienanunisoft/system-api:latest . -f Dockerfile/dockerfile_system_api
docker push thienanunisoft/system-api:latest

docker image tag thienanunisoft/system-api:latest thienanunisoft/system-api:dev
docker push thienanunisoft/system-api:dev

docker image tag thienanunisoft/system-api:latest thienanunisoft/system-api:test
docker push thienanunisoft/system-api:test

dotnet restore
dotnet publish ./APIService/System.API/System.API.csproj -c Release -o ./system-api-publish
docker build -t thienanunisoft/system-api:test . -f Dockerfile/dockerfile_system_api_build

```

### Pull và run web-api-gw

```bash
docker pull thienanunisoft/web-api-gw
docker run -d --restart unless-stopped --name web-api-gw -p 8000:80 thienanunisoft/web-api-gw
```

### Pull và run system-api

```bash
docker pull thienanunisoft/system-api
docker run -d --restart unless-stopped --name system-api -p 8002:80 thienanunisoft/system-api
```

## Grafana Loki

https://grafana.com/docs/loki/latest/get-started/quick-start/

## Prometheus

https://medium.com/c-sharp-programming/net-core-microservice-metrics-with-prometheus-and-grafana-d228bea89283

- Prometheus Web api gateway

```text
http://localhost:8000/metrics
```

- Prometheus system api

```text
http://localhost:8002/metrics

```

## Danh sách các site

- API Gateway - Ocelot
	- http://localhost:8000/
- HRM API
	- http://localhost:8001/
	
## Danh sách các tính năng của core
- [x] Phân chia rõ ràng tầng Data - Bussiness - Controller
- [x] Hỗ trợ docker
- [x] Đa ngôn ngữ - tính năng - data annotation
- [ ] Multi tenant
- [x] Api version
- [x] Swagger 
- [x] Tích hợp elastic search
- [ ] SignalR
- [x] Multi database
- [x] Multi object storage
- [x] Tích hợp RabbitMQ - Distributed event bus
- [x] Micro service
- [x] Object storage: MinIO - AWS S3
- [x] Authentication: WSO2 - JWT - Keycloak
- [x] CI-CD: gitlab
- [x] Cache: Redis - MemoryCache
- [x] Redis: Redis cache - Redis distributed lock
- [x] Database: Postgres - SQL Server
- [x] Logging: Serilog - Seq
- [ ] Monitoring: Prometheus - Grafana
- [x] Message queue: RabbitMQ
- [x] Sentry
- [x] Distributed cache: Redis cache
- [ ] Distributed event bus: RabbitMQ
- [x] Distributed authentication: WSO2 - JWT - Keycloak
- [ ] Distributed configuration: Consul
- [x] API gateway: Ocelot
- [x] API documentation: Swagger

- Tự động tăng số không trùng lặp sử dụng redis vì redis chạy dữ liệu đơn luồng, truy suất nhanh => đảm bảo không bị trùng lặp dữ liệu khi tăng dần => cách sử dụng tương tự cache

## Hướng dẫn cấu hình thêm Cron Job

- Service đã cấu hình CronJob mẫu tại project System.API: ClearCacheCronJobService
- Khi phát sinh CronJob mới cần bổ sung các cấu hình theo cú pháp sau
	- appsettings.json trong phần "AppSettings"
		```json
			    "ClearCacheCronJob": {
					"Enable": "true",
					"CronExpression": "* * * * *"
				}
		```
		- Trong đó "CronExpression" tham khảo tại link sau https://crontab.guru/

	- `CronJob`
		- Tại thư mục này trong api service thực hiện khai báo CronJob implement interface ICronJob

	- `CronJobServiceCollection`
		- Tại file này, thực hiện thêm cấu hình gọi đến CronJob để đăng ký chạy theo thời gian được cấu hình trong `CronExpression`

## Build docker
- docker build -t econtract-api:dev .
- docker run -d -p 30200:80 -p 30201:1234 --name econtract-api econtract-api:dev
- docker run -d -p 30200:80 --name econtract-api -e ASPNETCORE_ENVIRONMENT=Development econtract-api:dev
- docker push econtract-api:dev

## Docker compose
- docker-compose up -d
- docker-compose down
- docker-compose up -d --build
- docker-compose up -d --build --force-recreate

## Docker compose with dockerfile
- docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d --build
- docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d --build --force-recreate

## Migration
- dotnet ef migrations add init_data -Context SystemDataContext
- dotnet ef database update --context SystemDataContext
- dotnet ef migrations add init_data -Context SystemDataContext -o Data/Migrations
- dotnet ef database update --context SystemDataContext

## CI/CD
- https://docs.gitlab.com/ee/ci/yaml/README.html

## Grafana
- https://grafana.com/docs/grafana/latest/installation/docker/

## Kestrel
- https://docs.microsoft.com/en-us/aspnet/core/fundamentals/servers/kestrel?view=aspnetcore-5.0

## Health check API
curl --location --request GET 'http://localhost:30200/health'

## Tạo model từ DB có sẵn
Scaffold-DbContext "server=**************;database=UNISOFT_FULL6_DEV;User ID=Unisoftdev;password=*****************;" Microsoft.EntityFrameworkCore.SqlServer -OutputDir Systems/Context/All

## Hướng dẫn chạy migration
- Vì có nhiều DataContext cho datacontext đọc và ghi nên cần chỉ định đúng datacontext ghi để chạy migration
- add-migration init -Context SystemDataContext
- update-database -Context SystemDataContext
- Script-Migration 20250403180228_add_log 20250403180006_update_0404 -Context SystemDataContext
- Script-Migration -Context SystemDataContext

- dotnet ef migrations add init_data -Context SystemDataContext
- dotnet ef database update --context SystemDataContext

## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

